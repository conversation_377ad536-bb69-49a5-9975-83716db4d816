import { EmailIcon, PhoneIcon } from "@chakra-ui/icons";
import {
  Box,
  Button,
  Container,
  HStack,
  Heading,
  Icon,
  Link,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import * as React from "react";
import {
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaMapMarkerAlt,
  FaTwitter,
} from "react-icons/fa/index.js";
import { SocialButton } from "./SocialButton";

const Footer = () => {
  const bgColor = useColorModeValue("gray.900", "gray.50");
  const textColor = useColorModeValue("gray.200", "gray.700");
  const borderColor = useColorModeValue("gray.700", "gray.300");

  return (
    <Box bg={bgColor} color={textColor}>
      <Container maxW="8xl" py={16}>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8}>
          {/* Company Info */}
          <Stack spacing={6}>
            <Text fontSize="lg" fontWeight="bold">
              TenK Solutions
            </Text>
            <Text fontSize="sm">
              Empowering businesses with innovative IT solutions in the DMV area
              since 2021.
            </Text>
            <HStack spacing={4}>
              <Link href="tel:+1234567890">
                <Icon as={PhoneIcon} boxSize={5} />
              </Link>
              <Link href="mailto:<EMAIL>">
                <Icon as={EmailIcon} boxSize={5} />
              </Link>
              <Link href="https://goo.gl/maps/your-location">
                <Icon as={FaMapMarkerAlt} boxSize={5} />
              </Link>
            </HStack>
          </Stack>

          {/* Quick Links */}
          <Stack spacing={4}>
            <Heading size="md" color={textColor}>
              Quick Links
            </Heading>
            <Stack spacing={2}>
              <Link href="/about">About Us</Link>
              <Link href="/services">Services</Link>
              <Link href="/case-studies">Case Studies</Link>
              <Link href="/partners">Partners</Link>
              <Link href="/contact-us">Contact</Link>
            </Stack>
          </Stack>

          {/* Resources */}
          <Stack spacing={4}>
            <Heading size="md" color={textColor}>
              Resources
            </Heading>
            <Stack spacing={2}>
              <Link href="/tech-stack">Tech Stack</Link>
              <Link href="/files/Capability-Statement.pdf">Capabilities</Link>
              <Link href="/privacy">Privacy Policy</Link>
              <Link href="/terms">Terms of Service</Link>
            </Stack>
          </Stack>

          {/* Get Started */}
          <Stack spacing={4}>
            <Heading size="md" color={textColor}>
              Get Started
            </Heading>
            <Text>Ready to transform your IT infrastructure?</Text>
            <Link href="/contact-us" _hover={{ textDecoration: "none" }}>
              <Button colorScheme="brand.primary" size="lg" width="full">
                Free Consultation
              </Button>
            </Link>
          </Stack>
        </SimpleGrid>

        {/* Bottom Section */}
        <Box borderTopWidth={1} borderColor={borderColor} pt={8} mt={12}>
          <Container maxW="8xl">
            <SimpleGrid
              columns={{ base: 1, md: 2 }}
              spacing={8}
              alignItems="center"
            >
              <Text fontSize="sm">
                © {new Date().getFullYear()} TenK Solutions, LLC. All rights
                reserved.
              </Text>
              <HStack spacing={6} justify={{ base: "center", md: "flex-end" }}>
                <SocialButton
                  label="LinkedIn"
                  href="https://www.linkedin.com/company/tenksolutions"
                >
                  <FaLinkedin />
                </SocialButton>
                <SocialButton
                  label="Twitter"
                  href="https://twitter.com/tenk_solutions"
                >
                  <FaTwitter />
                </SocialButton>
                <SocialButton
                  label="Facebook"
                  href="https://www.facebook.com/tenksolutionsllc"
                >
                  <FaFacebook />
                </SocialButton>
                <SocialButton
                  label="Instagram"
                  href="https://www.instagram.com/tenksolutions"
                >
                  <FaInstagram />
                </SocialButton>
              </HStack>
            </SimpleGrid>
          </Container>
        </Box>
      </Container>
    </Box>
  );
};
export default Footer;
