import { ChakraProvider, ColorModeScript, extendTheme } from "@chakra-ui/react";
import React from "react";
import { themeByAI, brandGradients } from "#utils/themes";

// Enhanced theme for stories with additional utilities
const storyTheme = extendTheme({
  ...themeByAI,
  fonts: {
    body: "system-ui, sans-serif",
    heading: "'Exo 2', Georgia, sans-serif",
    mono: "Menlo, monospace",
  },
  colors: {
    ...themeByAI.colors,
    // Add story-specific color utilities
    story: {
      background: {
        light: "#ffffff",
        dark: "#1a202c",
      },
      border: {
        light: "#e2e8f0",
        dark: "#2d3748",
      },
    },
  },
  components: {
    ...themeByAI.components,
    // Enhanced component styles for better story presentation
    Container: {
      baseStyle: {
        maxW: 'container.xl',
        px: { base: 4, md: 8 },
      },
      variants: {
        story: {
          maxW: 'full',
          px: 6,
          py: 4,
        },
      },
    },
  },
  // Global styles for stories
  styles: {
    global: (props: any) => ({
      body: {
        bg: props.colorMode === 'dark' ? 'gray.900' : 'white',
        color: props.colorMode === 'dark' ? 'white' : 'gray.800',
      },
      // Story-specific global styles
      '.ladle-story': {
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '2rem',
      },
    }),
  },
});

interface StoryThemeProviderProps {
  children: React.ReactNode;
  colorMode?: 'light' | 'dark';
}

/**
 * Theme provider specifically designed for Ladle stories
 * Integrates with the existing Chakra UI theme system
 */
export const StoryThemeProvider: React.FC<StoryThemeProviderProps> = ({ 
  children, 
  colorMode = 'light' 
}) => {
  return (
    <>
      <ColorModeScript initialColorMode={colorMode} />
      <ChakraProvider theme={storyTheme}>
        <div className="ladle-story">
          {children}
        </div>
      </ChakraProvider>
    </>
  );
};

/**
 * Utility function to wrap stories with theme provider
 * Usage: export default { decorators: [withTheme] }
 */
export const withTheme = (Story: React.ComponentType, context: any) => (
  <StoryThemeProvider colorMode={context.globalTypes?.theme?.value || 'light'}>
    <Story />
  </StoryThemeProvider>
);

/**
 * Common story decorators for consistent presentation
 */
export const storyDecorators = {
  // Center content in viewport
  centered: (Story: React.ComponentType) => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center', 
      minHeight: '50vh',
      padding: '2rem' 
    }}>
      <Story />
    </div>
  ),
  
  // Add padding around content
  padded: (Story: React.ComponentType) => (
    <div style={{ padding: '2rem' }}>
      <Story />
    </div>
  ),
  
  // Full width container
  fullWidth: (Story: React.ComponentType) => (
    <div style={{ width: '100%' }}>
      <Story />
    </div>
  ),
  
  // Card-like presentation
  card: (Story: React.ComponentType) => (
    <div style={{ 
      maxWidth: '600px', 
      margin: '2rem auto',
      padding: '2rem',
      borderRadius: '12px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      backgroundColor: 'white'
    }}>
      <Story />
    </div>
  ),
};

// Export theme utilities for use in stories
export { storyTheme, brandGradients };
