import {
  But<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Text,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";

type QCardProps = { question: string; answer: string; cta: string };
export const QuestionCard = ({ question, answer, cta }: QCardProps) => {
  const color_primary = useColorModeValue(
    "colors.brand.primary.900",
    "colors.brand.primary.100",
  );
  return (
    <VStack
      p={{ base: 5, sm: 10 }}
      border={"1px"}
      borderRadius={"sm"}
      borderColor={color_primary}
      textAlign={"center"}
      spacing={{ base: 4, lg: 8 }}
      shadow="lg"
    >
      <Heading fontSize={{ base: "2xl", lg: "3xl" }}>{question}</Heading>
      <Text
        textAlign={"initial"}
        fontFamily={"'Open Sans', sans-serif"}
        fontSize={{ base: "lg", lg: "xl" }}
        my={3}
      >
        {answer}
      </Text>
      <Link href={"/contact-us"} title="Contact TenK Solutions">
        <Button rounded={"full"} title={`TenK Solutions: ${cta}`}>
          {cta}
        </Button>
      </Link>
    </VStack>
  );
};
