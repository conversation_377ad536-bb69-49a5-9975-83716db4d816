import React from "react";
import type { StoryDefault, Story } from "@ladle/react";
import { StoryThemeProvider, storyDecorators } from "../stories/StoryThemeProvider";
import { VStack, HStack, Text } from "@chakra-ui/react";
import { ButtonWithRipple } from "./ButtonWithRipple";

export default {
  title: "Components/Atoms/ButtonWithRipple",
  component: ButtonWithRipple,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
  args: {
    children: "Click me!",
  },
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["solid", "outline", "ghost", "link"],
      description: "Button variant style",
    },
    size: {
      control: { type: "select" },
      options: ["xs", "sm", "md", "lg"],
      description: "Button size",
    },
    colorScheme: {
      control: { type: "select" },
      options: ["blue", "red", "green", "purple", "teal", "brand.primary", "brand.secondary"],
      description: "Color scheme",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disable the button",
    },
    isLoading: {
      control: { type: "boolean" },
      description: "Show loading state",
    },
  },
} satisfies StoryDefault;

/**
 * Default button with ripple effect
 */
export const Default: Story = {
  args: {
    children: "Button with Ripple",
  },
  decorators: [storyDecorators.centered],
};

/**
 * All button variants with ripple effect
 */
export const AllVariants: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <Text fontWeight="bold">Button Variants with Ripple Effect</Text>
      <HStack spacing={4} wrap="wrap">
        <ButtonWithRipple variant="solid">Solid</ButtonWithRipple>
        <ButtonWithRipple variant="outline">Outline</ButtonWithRipple>
        <ButtonWithRipple variant="ghost">Ghost</ButtonWithRipple>
        <ButtonWithRipple variant="link">Link</ButtonWithRipple>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Different button sizes
 */
export const Sizes: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <Text fontWeight="bold">Button Sizes</Text>
      <HStack spacing={4} align="center" wrap="wrap">
        <ButtonWithRipple size="xs">Extra Small</ButtonWithRipple>
        <ButtonWithRipple size="sm">Small</ButtonWithRipple>
        <ButtonWithRipple size="md">Medium</ButtonWithRipple>
        <ButtonWithRipple size="lg">Large</ButtonWithRipple>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Interactive button for testing
 */
export const Interactive: Story = {
  args: {
    variant: "solid",
    size: "md",
    colorScheme: "blue",
    disabled: false,
    isLoading: false,
    children: "Interactive Button",
  },
  decorators: [storyDecorators.centered],
};

/**
 * Different button states
 */
export const States: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <Text fontWeight="bold">Button States</Text>
      <HStack spacing={4} wrap="wrap">
        <ButtonWithRipple>Normal</ButtonWithRipple>
        <ButtonWithRipple disabled>Disabled</ButtonWithRipple>
        <ButtonWithRipple isLoading>Loading</ButtonWithRipple>
        <ButtonWithRipple isLoading loadingText="Processing">
          Loading with Text
        </ButtonWithRipple>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Theme color integration
 */
export const WithThemeColors: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <Text fontWeight="bold">Theme Colors</Text>
      <HStack spacing={4} wrap="wrap">
        <ButtonWithRipple colorScheme="brand.primary">
          Primary Brand
        </ButtonWithRipple>
        <ButtonWithRipple colorScheme="brand.secondary">
          Secondary Brand
        </ButtonWithRipple>
        <ButtonWithRipple variant="outline" colorScheme="brand.primary">
          Primary Outline
        </ButtonWithRipple>
        <ButtonWithRipple variant="ghost" colorScheme="brand.secondary">
          Secondary Ghost
        </ButtonWithRipple>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Ripple effect demonstration
 */
export const RippleDemo: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold">Ripple Effect Demo</Text>
      <Text fontSize="sm" color="gray.600">
        Click the buttons below to see the ripple animation effect
      </Text>
      <HStack spacing={4} justify="center" wrap="wrap">
        <ButtonWithRipple size="lg" colorScheme="blue">
          Click for Ripple
        </ButtonWithRipple>
        <ButtonWithRipple size="lg" variant="outline" colorScheme="purple">
          Try This Too
        </ButtonWithRipple>
        <ButtonWithRipple size="lg" variant="solid" colorScheme="brand.primary">
          Brand Ripple
        </ButtonWithRipple>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};
