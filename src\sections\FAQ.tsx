import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Heading,
  Link,
  SimpleGrid,
} from "@chakra-ui/react";
import * as React from "react";

// FAQ data array

const faqs = [
  {
    question:
      "How can I use technology to help my small business reach the next growth milestone?",
    answer:
      "Our IT experts can provide customized solutions like workflow automation, data analytics, and custom software to help your small business hit growth goals and milestones. We partner with you to understand your needs and deploy the right technology.",
  },
  {
    question:
      "What IT services can help me set up a new retail location or office?",
    answer:
      "We install and configure networks, WiFi, POS systems, VoIP phones, security cameras, and any other required business technology.",
  },
  {
    question: "What types of custom software can improve my workflows?",
    answer:
      "Our expert developers can build customized software and web apps tailored to optimize your unique business workflows. From inventory or order management to HR systems, we can improve efficiency with solutions designed specifically for your processes.",
  },
  {
    question:
      "How can I optimize my existing IT infrastructure to improve performance?",
    answer:
      "We provide full managed IT services to help optimize your technology stack. This includes monitoring, updates, troubleshooting, security, help desk support, and more. We'll make sure your infrastructure aligns to business goals.",
  },
  {
    question:
      "How can automating processes improve my efficiency and cut costs?",
    answer:
      "By automating repetitive tasks, our solutions can significantly cut costs while improving quality and efficiency. We identify automation opportunities and implement the right technologies like scripting, RPA, APIs, and more to optimize your processes.",
  },

  {
    question: "How can you refresh my existing website design?",
    answer:
      "Our expert web developers can overhaul your current website with a completely new modern design. We'll carry over and optimize your content while giving your site a visual makeover with the latest design trends and features.",
  },
];

const Faq = () => {
  return (
    <Box px={8}>
      <Heading as={"h1"} textAlign="center" mb={3}>
        FAQs
      </Heading>
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3}>
        {faqs.map((faq, i) => (
          <Accordion allowToggle key={i}>
            <AccordionItem>
              <AccordionButton>
                <Heading as={"h2"} fontSize={16}>
                  {faq.question}
                  <AccordionIcon />
                </Heading>
              </AccordionButton>
              <Heading
                as={"h3"}
                textAlign="justify"
                fontSize={14}
                fontFamily="inherit"
                fontWeight="normal"
              >
                <AccordionPanel>{faq.answer}</AccordionPanel>
              </Heading>
            </AccordionItem>
          </Accordion>
        ))}
      </SimpleGrid>

      <Box textAlign="center" mt={6}>
        <Button as={Link} href="services" title="TenK Solutions Services">
          Learn More
        </Button>
      </Box>
    </Box>
  );
};

export default Faq;

/**
 * Managed IT services for SMBs
Technology solutions for restaurants
Retail tech solutions
Custom software development for SMEs
IT support for emerging businesses
Automation solutions for small business
 */
