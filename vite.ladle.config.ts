import react from "@vitejs/plugin-react";
import { resolve } from "path";
import { defineConfig } from "vite";

const resolvePath = (path: string) => resolve(__dirname, path);

// Vite config specifically for Ladle (without Vike plugin)
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "#components": resolvePath("src/components"),
      "#sections": resolvePath("src/sections"),
      "#pages": resolvePath("src/pages"),
      "#utils": resolvePath("src/utils"),
      "#renderer": resolvePath("src/renderer"),
    },
  },
});
