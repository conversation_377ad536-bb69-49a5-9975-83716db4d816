"use client";

import {
  Box,
  Container,
  HStack,
  Heading,
  Icon,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";
import { FaBuilding, FaMicrochip, FaRegGrinStars } from "react-icons/fa/index.js";
import { TbBrowser } from "react-icons/tb/index.js";

const MotionBox = motion(Box);

const Feature = ({
  title,
  text,
  icon,
}: {
  title: string;
  text: string;
  icon: any;
}) => {
  const bgColor = useColorModeValue("gray.50", "gray.800");
  const gradientColor = useColorModeValue(
    "linear(to-r, colors.brand.primary.100, colors.brand.secondary.100)",
    "linear(to-r, colors.brand.secondary.700, colors.brand.primary.700)"
  );
  const textColor = useColorModeValue("gray.600", "gray.400");
  return (
    <MotionBox whileHover={{ y: -4 }} transition={{ duration: 0.2 }}>
      <Stack
        bg={bgColor}
        p={4}
        rounded="xl"
        align="start"
        pos="relative"
        _hover={{ shadow: "xl" }}
        transition="all 0.3s"
      >
        <Stack spacing={2}>
          <HStack spacing={4}>
            <Box p={2} rounded="lg" bgGradient={gradientColor}>
              <Icon as={icon} w={8} h={8}  />
            </Box>
            <Heading
              as="h3"
              size="md"
              // bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
              // bgClip="text"
            >
              {title}
            </Heading>
          </HStack>
          <Text color={textColor}>{text}</Text>
        </Stack>
      </Stack>
    </MotionBox>
  );
};

const WhatWeDo = () => {
  const WHAT_WE_DO = [
    {
      title: "Customer Service",
      text: "Pride ourselves on providing quality customer service, tailored to your business model, milestones, and culture.",
      icon: () => <FaRegGrinStars name="service" />,
    },
    {
      title: "IT Consultant",
      text: "IT Consultant & Managed Service Provider for small to medium-sized businesses in the local DMV area.",
      icon: () => <FaMicrochip name="consultant" />,
    },
    {
      title: "Systems Integrator",
      text: "Systems Integrator for medium to enterprise-sized businesses across the nation.",
      icon: () => <FaBuilding name="integrator" />,
    },
    {
      title: "Web Application Engineers",
      text: "Web Application Engineers who build internal applications & optimize your web presence.",
      icon: () => <TbBrowser name="engineer" />,
    },
  ];

  return (
    <Stack spacing={4} as={Container} maxW={"container.lg"} p={4}>
      <Heading
        as={"h1"}
        fontSize={{ base: "2xl", lg: "4xl" }}
        fontWeight={"bold"}
        textAlign={"center"}
      >
        What We Do:
      </Heading>
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={10}>
        {WHAT_WE_DO.map((feature, i) => (
          <Feature
            key={`feature-${i}`}
            title={feature.title}
            text={feature.text}
            icon={feature.icon}
          />
        ))}
      </SimpleGrid>
    </Stack>
  );
};

export default WhatWeDo;
