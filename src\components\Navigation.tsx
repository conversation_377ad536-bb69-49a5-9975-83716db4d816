"use client";

import { isNotPrivacyString, normalizeURLPath } from "#utils/utils";
import { ArrowRightIcon, CloseIcon, HamburgerIcon } from "@chakra-ui/icons";

import usePageContext from "#renderer/usePageContext";
import {
  Box,
  Button,
  Flex,
  HStack,
  IconButton,
  Image,
  Link,
  Show,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react";
import React from "react";
import ColorModeToggle from "./ColorModeToggle";
import { MobileNav } from "./MobileNav";
import { HomeLink, Links, NavLink } from "./NavLink";

export default function Navigation() {
  const { isOpen, onToggle } = useDisclosure();

  const boxBgColor = useColorModeValue(
    "colors.brand.background",
    "colors.brand.primary.1000"
  );
  const { urlPathname } = usePageContext();
  return (
    <Box bg={boxBgColor} px={4} boxShadow={"lg"}>
      <Flex h={16} alignItems={"center"} justifyContent={"space-between"}>
        <Show below={"lg"}>
          <IconButton
            icon={isOpen ? <CloseIcon /> : <HamburgerIcon fontSize={22} />}
            aria-label={"Open Menu"}
            // display={{ md: "none" }}
            onClick={onToggle}
            variant={"link"}
          />
        </Show>
        <HStack spacing={8} alignItems={"center"}>
          <Show above="lg">
            <HStack
              as={"nav"}
              spacing={1}
              // display={{ base: "none", md: "flex" }}
            >
              <HomeLink />
              {Links.map(
                (link, i) =>
                  isNotPrivacyString(link) && (
                    <NavLink key={i} path={normalizeURLPath(urlPathname)}>
                      {link}
                    </NavLink>
                  )
              )}
            </HStack>
          </Show>
        </HStack>
        <Flex alignItems={"center"}>
          <ColorModeToggle />
          <Link href={"/contact-us"} title="Contact TenK Solutions, LLC">
            <Button
              colorScheme="colors.brand.primary"
              size={"sm"}
              mr={4}
              p={5}
              leftIcon={<ArrowRightIcon boxSize={2} />}
              title="Free Consultation"
            >
              Free Consultation
            </Button>
          </Link>
          {/* <Link
            isExternal
            href={
              "https://www.gopathfinder.net/apex/LeadForm?formid=a0v0z000008jRve&accountid=001Rl000007eouz&a116000000KNBHaAAP=Kiel%20Byrne&a116000000KNBFLAA5=Kiel%20Byrne&a116000000KNBFMAA5=<EMAIL>&a116000000KNBFKAA5=202-468-6155&a110z00000NaqcVAAR=<EMAIL>&a110z00000NaqcLAAR=Kiel%20Byrne"
            }
            title="Contact TenK Solutions, LLC"
          >
            <Button
              colorScheme="colors.brand.primary"
              size={"sm"}
              mr={4}
              p={5}
              leftIcon={<FaLightbulb />}
              title="Discovery IQA"
            >
              Instant Solution
            </Button>
          </Link> */}

          <Link
            href="/"
            style={{ cursor: "pointer" }}
            title="TenK Solutions Home Page"
          >
            <Box maxHeight={"64px"}>
              <Show above={"sm"}>
                <Image
                  height={10}
                  width={"auto"}
                  src={"/img/logo-no-background.webp"}
                  alt="TenK Solutions, LLC Large Logo"
                  objectFit="contain"
                />
              </Show>
              <Show below={"sm"}>
                <Image
                  height={10}
                  width={10}
                  src={"/img/favicon.webp"}
                  alt="TenK Solutions, LLC Small Logo"
                  objectFit="contain"
                />
              </Show>
            </Box>
          </Link>
        </Flex>
      </Flex>
      {isOpen ? <MobileNav onToggle={onToggle} isOpen={isOpen} /> : null}
    </Box>
  );
}
