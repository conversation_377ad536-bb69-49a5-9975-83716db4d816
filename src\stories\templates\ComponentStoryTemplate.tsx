/**
 * Component Story Template
 * 
 * This template provides a standardized structure for creating component stories.
 * Copy this template and modify it for your specific component.
 * 
 * Usage:
 * 1. Copy this file to your component directory
 * 2. Rename to [ComponentName].stories.tsx
 * 3. Replace all instances of "YourComponent" with your actual component name
 * 4. Import your component and update the stories
 * 5. Add component-specific props and variations
 */

import React from "react";
import type { StoryDefault, Story } from "@ladle/react";
import { StoryThemeProvider, storyDecorators } from "../StoryThemeProvider";
import { Box, VStack, Text } from "@chakra-ui/react";

// TODO: Import your actual component
// import { YourComponent } from "#components/YourComponent";

// Placeholder component - replace with your actual component
const YourComponent = ({ variant = "default", children, ...props }: any) => (
  <Box p={4} border="1px solid" borderColor="gray.200" borderRadius="md" {...props}>
    <Text>Your Component ({variant})</Text>
    {children}
  </Box>
);

// Story configuration
export default {
  title: "Components/YourComponent", // Update category and component name
  component: YourComponent,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
  // Global args that apply to all stories
  args: {
    // Add default props here
  },
  // Global arg types for controls
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "primary", "secondary"],
      description: "Component variant",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disable the component",
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg"],
      description: "Component size",
    },
  },
} satisfies StoryDefault;

/**
 * Default story - shows the component in its most basic state
 */
export const Default: Story = {
  args: {
    children: "Default component",
  },
  decorators: [storyDecorators.centered],
};

/**
 * All Variants - shows different component variants
 */
export const AllVariants: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <YourComponent variant="default">Default Variant</YourComponent>
      <YourComponent variant="primary">Primary Variant</YourComponent>
      <YourComponent variant="secondary">Secondary Variant</YourComponent>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Different Sizes - shows component in different sizes
 */
export const Sizes: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <YourComponent size="sm">Small Size</YourComponent>
      <YourComponent size="md">Medium Size</YourComponent>
      <YourComponent size="lg">Large Size</YourComponent>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Interactive - allows testing with controls
 */
export const Interactive: Story = {
  args: {
    variant: "default",
    size: "md",
    disabled: false,
    children: "Interactive component",
  },
  decorators: [storyDecorators.centered],
};

/**
 * States - shows different component states
 */
export const States: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <YourComponent>Normal State</YourComponent>
      <YourComponent disabled>Disabled State</YourComponent>
      <YourComponent variant="primary">Active State</YourComponent>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * With Theme Colors - demonstrates theme integration
 */
export const WithThemeColors: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <YourComponent bg="brand.primary.50" borderColor="brand.primary.200">
        Primary Theme
      </YourComponent>
      <YourComponent bg="brand.secondary.50" borderColor="brand.secondary.200">
        Secondary Theme
      </YourComponent>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

// Add more stories as needed for your specific component:
// - Error states
// - Loading states  
// - With different content
// - Responsive behavior
// - Accessibility features
