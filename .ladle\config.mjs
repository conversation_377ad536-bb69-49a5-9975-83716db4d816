/** @type {import('@ladle/react').UserConfig} */
export default {
  stories: "src/**/*.stories.{js,jsx,ts,tsx}",
  viteConfig: "./vite.ladle.config.ts",

  // Enhanced configuration for better development experience
  defaultStory: "welcome",

  // Theme configuration
  addons: {
    theme: {
      enabled: true,
      defaultState: "light",
    },
    controls: {
      enabled: true,
      defaultState: {},
    },
    source: {
      enabled: true,
      defaultState: false,
    },
    a11y: {
      enabled: true,
      defaultState: false,
    },
    action: {
      enabled: true,
      defaultState: {},
    },
  },

  // Hot reload configuration
  hmr: true,

  // Build configuration
  outDir: "ladle-build",

  // Base path for deployment
  base: "/",

  // Port configuration
  port: 61000,

  // Open browser automatically
  open: true,
};
