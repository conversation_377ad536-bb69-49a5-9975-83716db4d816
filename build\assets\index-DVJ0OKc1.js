var Og=Object.defineProperty;var Tg=(n,o,l)=>o in n?Og(n,o,{enumerable:!0,configurable:!0,writable:!0,value:l}):n[o]=l;var uo=(n,o,l)=>Tg(n,typeof o!="symbol"?o+"":o,l);(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))a(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&a(f)}).observe(document,{childList:!0,subtree:!0});function l(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function a(c){if(c.ep)return;c.ep=!0;const d=l(c);fetch(c.href,d)}})();var Mi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function kl(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Bi={exports:{}},Qs,mf;function Ag(){if(mf)return Qs;mf=1;var n=1e3,o=n*60,l=o*60,a=l*24,c=a*7,d=a*365.25;Qs=function(S,k){k=k||{};var C=typeof S;if(C==="string"&&S.length>0)return f(S);if(C==="number"&&isFinite(S))return k.long?h(S):y(S);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(S))};function f(S){if(S=String(S),!(S.length>100)){var k=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(S);if(k){var C=parseFloat(k[1]),A=(k[2]||"ms").toLowerCase();switch(A){case"years":case"year":case"yrs":case"yr":case"y":return C*d;case"weeks":case"week":case"w":return C*c;case"days":case"day":case"d":return C*a;case"hours":case"hour":case"hrs":case"hr":case"h":return C*l;case"minutes":case"minute":case"mins":case"min":case"m":return C*o;case"seconds":case"second":case"secs":case"sec":case"s":return C*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return C;default:return}}}}function y(S){var k=Math.abs(S);return k>=a?Math.round(S/a)+"d":k>=l?Math.round(S/l)+"h":k>=o?Math.round(S/o)+"m":k>=n?Math.round(S/n)+"s":S+"ms"}function h(S){var k=Math.abs(S);return k>=a?m(S,k,a,"day"):k>=l?m(S,k,l,"hour"):k>=o?m(S,k,o,"minute"):k>=n?m(S,k,n,"second"):S+" ms"}function m(S,k,C,A){var b=k>=C*1.5;return Math.round(S/C)+" "+A+(b?"s":"")}return Qs}var Ks,vf;function Rg(){if(vf)return Ks;vf=1;function n(o){a.debug=a,a.default=a,a.coerce=m,a.disable=y,a.enable=d,a.enabled=h,a.humanize=Ag(),a.destroy=S,Object.keys(o).forEach(k=>{a[k]=o[k]}),a.names=[],a.skips=[],a.formatters={};function l(k){let C=0;for(let A=0;A<k.length;A++)C=(C<<5)-C+k.charCodeAt(A),C|=0;return a.colors[Math.abs(C)%a.colors.length]}a.selectColor=l;function a(k){let C,A=null,b,v;function w(...I){if(!w.enabled)return;const j=w,B=Number(new Date),q=B-(C||B);j.diff=q,j.prev=C,j.curr=B,C=B,I[0]=a.coerce(I[0]),typeof I[0]!="string"&&I.unshift("%O");let te=0;I[0]=I[0].replace(/%([a-zA-Z%])/g,(Y,J)=>{if(Y==="%%")return"%";te++;const le=a.formatters[J];if(typeof le=="function"){const ge=I[te];Y=le.call(j,ge),I.splice(te,1),te--}return Y}),a.formatArgs.call(j,I),(j.log||a.log).apply(j,I)}return w.namespace=k,w.useColors=a.useColors(),w.color=a.selectColor(k),w.extend=c,w.destroy=a.destroy,Object.defineProperty(w,"enabled",{enumerable:!0,configurable:!1,get:()=>A!==null?A:(b!==a.namespaces&&(b=a.namespaces,v=a.enabled(k)),v),set:I=>{A=I}}),typeof a.init=="function"&&a.init(w),w}function c(k,C){const A=a(this.namespace+(typeof C>"u"?":":C)+k);return A.log=this.log,A}function d(k){a.save(k),a.namespaces=k,a.names=[],a.skips=[];const C=(typeof k=="string"?k:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const A of C)A[0]==="-"?a.skips.push(A.slice(1)):a.names.push(A)}function f(k,C){let A=0,b=0,v=-1,w=0;for(;A<k.length;)if(b<C.length&&(C[b]===k[A]||C[b]==="*"))C[b]==="*"?(v=b,w=A,b++):(A++,b++);else if(v!==-1)b=v+1,w++,A=w;else return!1;for(;b<C.length&&C[b]==="*";)b++;return b===C.length}function y(){const k=[...a.names,...a.skips.map(C=>"-"+C)].join(",");return a.enable(""),k}function h(k){for(const C of a.skips)if(f(k,C))return!1;for(const C of a.names)if(f(k,C))return!0;return!1}function m(k){return k instanceof Error?k.stack||k.message:k}function S(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return a.enable(a.load()),a}return Ks=n,Ks}var wf;function Ng(){return wf||(wf=1,function(n,o){var l={};o.formatArgs=c,o.save=d,o.load=f,o.useColors=a,o.storage=y(),o.destroy=(()=>{let m=!1;return()=>{m||(m=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),o.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function a(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let m;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(m=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(m[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function c(m){if(m[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+m[0]+(this.useColors?"%c ":" ")+"+"+n.exports.humanize(this.diff),!this.useColors)return;const S="color: "+this.color;m.splice(1,0,S,"color: inherit");let k=0,C=0;m[0].replace(/%[a-zA-Z%]/g,A=>{A!=="%%"&&(k++,A==="%c"&&(C=k))}),m.splice(C,0,S)}o.log=console.debug||console.log||(()=>{});function d(m){try{m?o.storage.setItem("debug",m):o.storage.removeItem("debug")}catch{}}function f(){let m;try{m=o.storage.getItem("debug")||o.storage.getItem("DEBUG")}catch{}return!m&&typeof process<"u"&&"env"in process&&(m=l.DEBUG),m}function y(){try{return localStorage}catch{}}n.exports=Rg()(o);const{formatters:h}=n.exports;h.j=function(m){try{return JSON.stringify(m)}catch(S){return"[UnexpectedJSONParseError]: "+S.message}}}(Bi,Bi.exports)),Bi.exports}var Lg=Ng();const Fg=kl(Lg),sn=Fg("ladle"),mp="%[a-f0-9]{2}",Ef=new RegExp("("+mp+")|([^%]+?)","gi"),xf=new RegExp("("+mp+")+","gi");function wu(n,o){try{return[decodeURIComponent(n.join(""))]}catch{}if(n.length===1)return n;o=o||1;const l=n.slice(0,o),a=n.slice(o);return Array.prototype.concat.call([],wu(l),wu(a))}function jg(n){try{return decodeURIComponent(n)}catch{let o=n.match(Ef)||[];for(let l=1;l<o.length;l++)n=wu(o,l).join(""),o=n.match(Ef)||[];return n}}function Ig(n){const o={"%FE%FF":"��","%FF%FE":"��"};let l=xf.exec(n);for(;l;){try{o[l[0]]=decodeURIComponent(l[0])}catch{const c=jg(l[0]);c!==l[0]&&(o[l[0]]=c)}l=xf.exec(n)}o["%C2"]="�";const a=Object.keys(o);for(const c of a)n=n.replace(new RegExp(c,"g"),o[c]);return n}function Pg(n){if(typeof n!="string")throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof n+"`");try{return decodeURIComponent(n)}catch{return Ig(n)}}function Dg(n,o){const l={};if(Array.isArray(o))for(const a of o){const c=Object.getOwnPropertyDescriptor(n,a);c!=null&&c.enumerable&&Object.defineProperty(l,a,c)}else for(const a of Reflect.ownKeys(n)){const c=Object.getOwnPropertyDescriptor(n,a);if(c.enumerable){const d=n[a];o(a,d,n)&&Object.defineProperty(l,a,c)}}return l}function vp(n,o){if(!(typeof n=="string"&&typeof o=="string"))throw new TypeError("Expected the arguments to be of type `string`");if(n===""||o==="")return[];const l=n.indexOf(o);return l===-1?[]:[n.slice(0,l),n.slice(l+o.length)]}const Mg=n=>n==null,Bg=n=>encodeURIComponent(n).replaceAll(/[!'()*]/g,o=>`%${o.charCodeAt(0).toString(16).toUpperCase()}`),Eu=Symbol("encodeFragmentIdentifier");function zg(n){switch(n.arrayFormat){case"index":return o=>(l,a)=>{const c=l.length;return a===void 0||n.skipNull&&a===null||n.skipEmptyString&&a===""?l:a===null?[...l,[Qe(o,n),"[",c,"]"].join("")]:[...l,[Qe(o,n),"[",Qe(c,n),"]=",Qe(a,n)].join("")]};case"bracket":return o=>(l,a)=>a===void 0||n.skipNull&&a===null||n.skipEmptyString&&a===""?l:a===null?[...l,[Qe(o,n),"[]"].join("")]:[...l,[Qe(o,n),"[]=",Qe(a,n)].join("")];case"colon-list-separator":return o=>(l,a)=>a===void 0||n.skipNull&&a===null||n.skipEmptyString&&a===""?l:a===null?[...l,[Qe(o,n),":list="].join("")]:[...l,[Qe(o,n),":list=",Qe(a,n)].join("")];case"comma":case"separator":case"bracket-separator":{const o=n.arrayFormat==="bracket-separator"?"[]=":"=";return l=>(a,c)=>c===void 0||n.skipNull&&c===null||n.skipEmptyString&&c===""?a:(c=c===null?"":c,a.length===0?[[Qe(l,n),o,Qe(c,n)].join("")]:[[a,Qe(c,n)].join(n.arrayFormatSeparator)])}default:return o=>(l,a)=>a===void 0||n.skipNull&&a===null||n.skipEmptyString&&a===""?l:a===null?[...l,Qe(o,n)]:[...l,[Qe(o,n),"=",Qe(a,n)].join("")]}}function Ug(n){let o;switch(n.arrayFormat){case"index":return(l,a,c)=>{if(o=/\[(\d*)]$/.exec(l),l=l.replace(/\[\d*]$/,""),!o){c[l]=a;return}c[l]===void 0&&(c[l]={}),c[l][o[1]]=a};case"bracket":return(l,a,c)=>{if(o=/(\[])$/.exec(l),l=l.replace(/\[]$/,""),!o){c[l]=a;return}if(c[l]===void 0){c[l]=[a];return}c[l]=[...c[l],a]};case"colon-list-separator":return(l,a,c)=>{if(o=/(:list)$/.exec(l),l=l.replace(/:list$/,""),!o){c[l]=a;return}if(c[l]===void 0){c[l]=[a];return}c[l]=[...c[l],a]};case"comma":case"separator":return(l,a,c)=>{const d=typeof a=="string"&&a.includes(n.arrayFormatSeparator),f=typeof a=="string"&&!d&&On(a,n).includes(n.arrayFormatSeparator);a=f?On(a,n):a;const y=d||f?a.split(n.arrayFormatSeparator).map(h=>On(h,n)):a===null?a:On(a,n);c[l]=y};case"bracket-separator":return(l,a,c)=>{const d=/(\[])$/.test(l);if(l=l.replace(/\[]$/,""),!d){c[l]=a&&On(a,n);return}const f=a===null?[]:On(a,n).split(n.arrayFormatSeparator);if(c[l]===void 0){c[l]=f;return}c[l]=[...c[l],...f]};default:return(l,a,c)=>{if(c[l]===void 0){c[l]=a;return}c[l]=[...[c[l]].flat(),a]}}}function wp(n){if(typeof n!="string"||n.length!==1)throw new TypeError("arrayFormatSeparator must be single character string")}function Qe(n,o){return o.encode?o.strict?Bg(n):encodeURIComponent(n):n}function On(n,o){return o.decode?Pg(n):n}function Ep(n){return Array.isArray(n)?n.sort():typeof n=="object"?Ep(Object.keys(n)).sort((o,l)=>Number(o)-Number(l)).map(o=>n[o]):n}function xp(n){const o=n.indexOf("#");return o!==-1&&(n=n.slice(0,o)),n}function $g(n){let o="";const l=n.indexOf("#");return l!==-1&&(o=n.slice(l)),o}function kf(n,o,l){return l==="string"&&typeof n=="string"?n:typeof l=="function"&&typeof n=="string"?l(n):l==="boolean"&&n===null?!0:l==="boolean"&&n!==null&&(n.toLowerCase()==="true"||n.toLowerCase()==="false")?n.toLowerCase()==="true":l==="boolean"&&n!==null&&(n.toLowerCase()==="1"||n.toLowerCase()==="0")?n.toLowerCase()==="1":l==="string[]"&&o.arrayFormat!=="none"&&typeof n=="string"?[n]:l==="number[]"&&o.arrayFormat!=="none"&&!Number.isNaN(Number(n))&&typeof n=="string"&&n.trim()!==""?[Number(n)]:l==="number"&&!Number.isNaN(Number(n))&&typeof n=="string"&&n.trim()!==""?Number(n):o.parseBooleans&&n!==null&&(n.toLowerCase()==="true"||n.toLowerCase()==="false")?n.toLowerCase()==="true":o.parseNumbers&&!Number.isNaN(Number(n))&&typeof n=="string"&&n.trim()!==""?Number(n):n}function Uu(n){n=xp(n);const o=n.indexOf("?");return o===-1?"":n.slice(o+1)}function $u(n,o){o={decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1,types:Object.create(null),...o},wp(o.arrayFormatSeparator);const l=Ug(o),a=Object.create(null);if(typeof n!="string"||(n=n.trim().replace(/^[?#&]/,""),!n))return a;for(const c of n.split("&")){if(c==="")continue;const d=o.decode?c.replaceAll("+"," "):c;let[f,y]=vp(d,"=");f===void 0&&(f=d),y=y===void 0?null:["comma","separator","bracket-separator"].includes(o.arrayFormat)?y:On(y,o),l(On(f,o),y,a)}for(const[c,d]of Object.entries(a))if(typeof d=="object"&&d!==null&&o.types[c]!=="string")for(const[f,y]of Object.entries(d)){const h=o.types[c]?o.types[c].replace("[]",""):void 0;d[f]=kf(y,o,h)}else typeof d=="object"&&d!==null&&o.types[c]==="string"?a[c]=Object.values(d).join(o.arrayFormatSeparator):a[c]=kf(d,o,o.types[c]);return o.sort===!1?a:(o.sort===!0?Object.keys(a).sort():Object.keys(a).sort(o.sort)).reduce((c,d)=>{const f=a[d];return c[d]=f&&typeof f=="object"&&!Array.isArray(f)?Ep(f):f,c},Object.create(null))}function kp(n,o){if(!n)return"";o={encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:",",...o},wp(o.arrayFormatSeparator);const l=f=>o.skipNull&&Mg(n[f])||o.skipEmptyString&&n[f]==="",a=zg(o),c={};for(const[f,y]of Object.entries(n))l(f)||(c[f]=y);const d=Object.keys(c);return o.sort!==!1&&d.sort(o.sort),d.map(f=>{const y=n[f];return y===void 0?"":y===null?Qe(f,o):Array.isArray(y)?y.length===0&&o.arrayFormat==="bracket-separator"?Qe(f,o)+"[]":y.reduce(a(f),[]).join("&"):Qe(f,o)+"="+Qe(y,o)}).filter(f=>f.length>0).join("&")}function Sp(n,o){var c;o={decode:!0,...o};let[l,a]=vp(n,"#");return l===void 0&&(l=n),{url:((c=l==null?void 0:l.split("?"))==null?void 0:c[0])??"",query:$u(Uu(n),o),...o&&o.parseFragmentIdentifier&&a?{fragmentIdentifier:On(a,o)}:{}}}function bp(n,o){o={encode:!0,strict:!0,[Eu]:!0,...o};const l=xp(n.url).split("?")[0]||"",a=Uu(n.url),c={...$u(a,{sort:!1,...o}),...n.query};let d=kp(c,o);d&&(d=`?${d}`);let f=$g(n.url);if(typeof n.fragmentIdentifier=="string"){const y=new URL(l);y.hash=n.fragmentIdentifier,f=o[Eu]?y.hash:`#${n.fragmentIdentifier}`}return`${l}${d}${f}`}function _p(n,o,l){l={parseFragmentIdentifier:!0,[Eu]:!1,...l};const{url:a,query:c,fragmentIdentifier:d}=Sp(n,l);return bp({url:a,query:Dg(c,o),fragmentIdentifier:d},l)}function Hg(n,o,l){const a=Array.isArray(o)?c=>!o.includes(c):(c,d)=>!o(c,d);return _p(n,a,l)}const dn=Object.freeze(Object.defineProperty({__proto__:null,exclude:Hg,extract:Uu,parse:$u,parseUrl:Sp,pick:_p,stringify:kp,stringifyUrl:bp},Symbol.toStringTag,{value:"Module"})),po="-",Cp=(n,o)=>dn.parse(n).story||o,Vg=n=>!!dn.parse(n).story,Op=n=>typeof n!="string"?"":n.charAt(0).toUpperCase()+n.slice(1),Tp=n=>n?n.split(`${po}${po}`).reverse().map(o=>Op(o.replace(/-/g," "))).join(" - "):"",Xs=(n,o,l)=>{const a=[],c=(f,y,h,m)=>{const S=y.shift();let k=!!l,C=[];h[0]===S&&(C=[...h.slice(1)],k=!0);const A=f.findIndex(b=>b.subId===S);S&&(A===-1&&f.push({id:`${m}${S}`,subId:S,name:Op(S.replace(/-/g," ")),isLinkable:y.length===0,isExpanded:k,isFocused:!1,children:[]}),c(f[A>-1?A:f.length-1].children,y,C,`${m}${S}--`))},d=o?o.split(`${po}${po}`):[];return n.forEach(f=>{const y=f.split(`${po}${po}`);c(a,y,d,"")}),a},Wg=(n,o)=>{const l=n.split("--"),a=o.split("--"),c=Math.min(l.length,a.length);for(let d=0;d<c;d++)if(l[d]!==a[d])return!l[d+1]&&a[d+1]?1:l[d+1]&&!a[d+1]||[l[d],a[d]].sort()[0]===l[d]?-1:1;return 0},Ap=(n,o)=>{const l=n.sort(Wg);let a=[...l];Array.isArray(o)?a=o:a=o(l);const c=new Set;return a.forEach(d=>{const f=d.toLowerCase();if(f.includes("*")){const y=f.split("*")[0];l.forEach(h=>{h.startsWith(y)&&c.add(h)})}else{if(!l.includes(f))throw new Error(`Story "${d}" does not exist in your storybook. Please check your storyOrder config.`);c.add(f)}}),[...c]};var gl={exports:{}};gl.exports;var Sf;function Gg(){return Sf||(Sf=1,function(n,o){var l=200,a="__lodash_hash_undefined__",c=800,d=16,f=9007199254740991,y="[object Arguments]",h="[object Array]",m="[object AsyncFunction]",S="[object Boolean]",k="[object Date]",C="[object Error]",A="[object Function]",b="[object GeneratorFunction]",v="[object Map]",w="[object Number]",I="[object Null]",j="[object Object]",B="[object Proxy]",q="[object RegExp]",te="[object Set]",X="[object String]",Y="[object Undefined]",J="[object WeakMap]",le="[object ArrayBuffer]",ge="[object DataView]",ke="[object Float32Array]",U="[object Float64Array]",ne="[object Int8Array]",re="[object Int16Array]",de="[object Int32Array]",se="[object Uint8Array]",ie="[object Uint8ClampedArray]",z="[object Uint16Array]",Q="[object Uint32Array]",V=/[\\^$.*+?()[\]{}|]/g,O=/^\[object .+?Constructor\]$/,M=/^(?:0|[1-9]\d*)$/,ee={};ee[ke]=ee[U]=ee[ne]=ee[re]=ee[de]=ee[se]=ee[ie]=ee[z]=ee[Q]=!0,ee[y]=ee[h]=ee[le]=ee[S]=ee[ge]=ee[k]=ee[C]=ee[A]=ee[v]=ee[w]=ee[j]=ee[q]=ee[te]=ee[X]=ee[J]=!1;var me=typeof Mi=="object"&&Mi&&Mi.Object===Object&&Mi,be=typeof self=="object"&&self&&self.Object===Object&&self,ve=me||be||Function("return this")(),_e=o&&!o.nodeType&&o,Se=_e&&!0&&n&&!n.nodeType&&n,Ne=Se&&Se.exports===_e,Ke=Ne&&me.process,fn=function(){try{var E=Se&&Se.require&&Se.require("util").types;return E||Ke&&Ke.binding&&Ke.binding("util")}catch{}}(),rr=fn&&fn.isTypedArray;function An(E,R,D){switch(D.length){case 0:return E.call(R);case 1:return E.call(R,D[0]);case 2:return E.call(R,D[0],D[1]);case 3:return E.call(R,D[0],D[1],D[2])}return E.apply(R,D)}function ye(E,R){for(var D=-1,ue=Array(E);++D<E;)ue[D]=R(D);return ue}function pn(E){return function(R){return E(R)}}function or(E,R){return E==null?void 0:E[R]}function hn(E,R){return function(D){return E(R(D))}}var bl=Array.prototype,bo=Function.prototype,Xt=Object.prototype,Jt=ve["__core-js_shared__"],Rn=bo.toString,Ht=Xt.hasOwnProperty,_o=function(){var E=/[^.]+$/.exec(Jt&&Jt.keys&&Jt.keys.IE_PROTO||"");return E?"Symbol(src)_1."+E:""}(),Co=Xt.toString,_l=Rn.call(Object),Oo=RegExp("^"+Rn.call(Ht).replace(V,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),yn=Ne?ve.Buffer:void 0,To=ve.Symbol,Nn=ve.Uint8Array;yn&&yn.allocUnsafe;var Ln=hn(Object.getPrototypeOf,Object),Cl=Object.create,Ol=Xt.propertyIsEnumerable,Tl=bl.splice,gn=To?To.toStringTag:void 0,Fn=function(){try{var E=Io(Object,"defineProperty");return E({},"",{}),E}catch{}}(),Ao=yn?yn.isBuffer:void 0,Tr=Math.max,Ro=Date.now,Ar=Io(ve,"Map"),Tt=Io(Object,"create"),jn=function(){function E(){}return function(R){if(!tn(R))return{};if(Cl)return Cl(R);E.prototype=R;var D=new E;return E.prototype=void 0,D}}();function en(E){var R=-1,D=E==null?0:E.length;for(this.clear();++R<D;){var ue=E[R];this.set(ue[0],ue[1])}}function Al(){this.__data__=Tt?Tt(null):{},this.size=0}function Rl(E){var R=this.has(E)&&delete this.__data__[E];return this.size-=R?1:0,R}function Nl(E){var R=this.__data__;if(Tt){var D=R[E];return D===a?void 0:D}return Ht.call(R,E)?R[E]:void 0}function Ll(E){var R=this.__data__;return Tt?R[E]!==void 0:Ht.call(R,E)}function No(E,R){var D=this.__data__;return this.size+=this.has(E)?0:1,D[E]=Tt&&R===void 0?a:R,this}en.prototype.clear=Al,en.prototype.delete=Rl,en.prototype.get=Nl,en.prototype.has=Ll,en.prototype.set=No;function At(E){var R=-1,D=E==null?0:E.length;for(this.clear();++R<D;){var ue=E[R];this.set(ue[0],ue[1])}}function lr(){this.__data__=[],this.size=0}function Lo(E){var R=this.__data__,D=Lr(R,E);if(D<0)return!1;var ue=R.length-1;return D==ue?R.pop():Tl.call(R,D,1),--this.size,!0}function ir(E){var R=this.__data__,D=Lr(R,E);return D<0?void 0:R[D][1]}function ca(E){return Lr(this.__data__,E)>-1}function ar(E,R){var D=this.__data__,ue=Lr(D,E);return ue<0?(++this.size,D.push([E,R])):D[ue][1]=R,this}At.prototype.clear=lr,At.prototype.delete=Lo,At.prototype.get=ir,At.prototype.has=ca,At.prototype.set=ar;function Vt(E){var R=-1,D=E==null?0:E.length;for(this.clear();++R<D;){var ue=E[R];this.set(ue[0],ue[1])}}function Rr(){this.size=0,this.__data__={hash:new en,map:new(Ar||At),string:new en}}function Fo(E){var R=Dr(this,E).delete(E);return this.size-=R?1:0,R}function da(E){return Dr(this,E).get(E)}function fa(E){return Dr(this,E).has(E)}function pa(E,R){var D=Dr(this,E),ue=D.size;return D.set(E,R),this.size+=D.size==ue?0:1,this}Vt.prototype.clear=Rr,Vt.prototype.delete=Fo,Vt.prototype.get=da,Vt.prototype.has=fa,Vt.prototype.set=pa;function yt(E){var R=this.__data__=new At(E);this.size=R.size}function Fl(){this.__data__=new At,this.size=0}function jl(E){var R=this.__data__,D=R.delete(E);return this.size=R.size,D}function ha(E){return this.__data__.get(E)}function Il(E){return this.__data__.has(E)}function Pl(E,R){var D=this.__data__;if(D instanceof At){var ue=D.__data__;if(!Ar||ue.length<l-1)return ue.push([E,R]),this.size=++D.size,this;D=this.__data__=new Vt(ue)}return D.set(E,R),this.size=D.size,this}yt.prototype.clear=Fl,yt.prototype.delete=jl,yt.prototype.get=ha,yt.prototype.has=Il,yt.prototype.set=Pl;function Dl(E,R){var D=Lt(E),ue=!D&&Nt(E),Oe=!D&&!ue&&Pn(E),Pe=!D&&!ue&&!Oe&&zo(E),Me=D||ue||Oe||Pe,Ce=Me?ye(E.length,String):[],De=Ce.length;for(var lt in E)Me&&(lt=="length"||Oe&&(lt=="offset"||lt=="parent")||Pe&&(lt=="buffer"||lt=="byteLength"||lt=="byteOffset")||Mr(lt,De))||Ce.push(lt);return Ce}function Nr(E,R,D){(D!==void 0&&!mn(E[R],D)||D===void 0&&!(R in E))&&ze(E,R,D)}function ya(E,R,D){var ue=E[R];(!(Ht.call(E,R)&&mn(ue,D))||D===void 0&&!(R in E))&&ze(E,R,D)}function Lr(E,R){for(var D=E.length;D--;)if(mn(E[D][0],R))return D;return-1}function ze(E,R,D){R=="__proto__"&&Fn?Fn(E,R,{configurable:!0,enumerable:!0,value:D,writable:!0}):E[R]=D}var ga=Pr();function In(E){return E==null?E===void 0?Y:I:gn&&gn in Object(E)?Po(E):Ul(E)}function jo(E){return Wt(E)&&In(E)==y}function Fr(E){if(!tn(E)||Do(E))return!1;var R=jt(E)?Oo:O;return R.test(Bo(E))}function ma(E){return Wt(E)&&Vl(E.length)&&!!ee[In(E)]}function Ml(E){if(!tn(E))return zl(E);var R=Fe(E),D=[];for(var ue in E)ue=="constructor"&&(R||!Ht.call(E,ue))||D.push(ue);return D}function sr(E,R,D,ue,Oe){E!==R&&ga(R,function(Pe,Me){if(Oe||(Oe=new yt),tn(Pe))Rt(E,R,Me,D,sr,ue,Oe);else{var Ce=ue?ue(Br(E,Me),Pe,Me+"",E,R,Oe):void 0;Ce===void 0&&(Ce=Pe),Nr(E,Me,Ce)}},Uo)}function Rt(E,R,D,ue,Oe,Pe,Me){var Ce=Br(E,D),De=Br(R,D),lt=Me.get(De);if(lt){Nr(E,D,lt);return}var it=Pe?Pe(Ce,De,D+"",E,R,Me):void 0,nn=it===void 0;if(nn){var Bn=Lt(De),Ur=!Bn&&Pn(De),at=!Bn&&!Ur&&zo(De);it=De,Bn||Ur||at?Lt(Ce)?it=Ce:dr(Ce)?it=jr(Ce):Ur?(nn=!1,it=wa(De)):at?(nn=!1,it=xa(De)):it=[]:Sa(De)||Nt(De)?(it=Ce,Nt(Ce)?it=zr(Ce):(!tn(Ce)||jt(Ce))&&(it=Bl(De))):nn=!1}nn&&(Me.set(De,it),Oe(it,De,ue,Pe,Me),Me.delete(De)),Nr(E,D,it)}function va(E,R){return $l(Mo(E,R,Dn),E+"")}var kt=Fn?function(E,R){return Fn(E,"toString",{configurable:!0,enumerable:!1,value:fr(R),writable:!0})}:Dn;function wa(E,R){return E.slice()}function Ea(E){var R=new E.constructor(E.byteLength);return new Nn(R).set(new Nn(E)),R}function xa(E,R){var D=Ea(E.buffer);return new E.constructor(D,E.byteOffset,E.length)}function jr(E,R){var D=-1,ue=E.length;for(R||(R=Array(ue));++D<ue;)R[D]=E[D];return R}function Ir(E,R,D,ue){var Oe=!D;D||(D={});for(var Pe=-1,Me=R.length;++Pe<Me;){var Ce=R[Pe],De=void 0;De===void 0&&(De=E[Ce]),Oe?ze(D,Ce,De):ya(D,Ce,De)}return D}function ur(E){return va(function(R,D){var ue=-1,Oe=D.length,Pe=Oe>1?D[Oe-1]:void 0,Me=Oe>2?D[2]:void 0;for(Pe=E.length>3&&typeof Pe=="function"?(Oe--,Pe):void 0,Me&&cr(D[0],D[1],Me)&&(Pe=Oe<3?void 0:Pe,Oe=1),R=Object(R);++ue<Oe;){var Ce=D[ue];Ce&&E(R,Ce,ue,Pe)}return R})}function Pr(E){return function(R,D,ue){for(var Oe=-1,Pe=Object(R),Me=ue(R),Ce=Me.length;Ce--;){var De=Me[++Oe];if(D(Pe[De],De,Pe)===!1)break}return R}}function Dr(E,R){var D=E.__data__;return ka(R)?D[typeof R=="string"?"string":"hash"]:D.map}function Io(E,R){var D=or(E,R);return Fr(D)?D:void 0}function Po(E){var R=Ht.call(E,gn),D=E[gn];try{E[gn]=void 0;var ue=!0}catch{}var Oe=Co.call(E);return ue&&(R?E[gn]=D:delete E[gn]),Oe}function Bl(E){return typeof E.constructor=="function"&&!Fe(E)?jn(Ln(E)):{}}function Mr(E,R){var D=typeof E;return R=R??f,!!R&&(D=="number"||D!="symbol"&&M.test(E))&&E>-1&&E%1==0&&E<R}function cr(E,R,D){if(!tn(D))return!1;var ue=typeof R;return(ue=="number"?Ft(D)&&Mr(R,D.length):ue=="string"&&R in D)?mn(D[R],E):!1}function ka(E){var R=typeof E;return R=="string"||R=="number"||R=="symbol"||R=="boolean"?E!=="__proto__":E===null}function Do(E){return!!_o&&_o in E}function Fe(E){var R=E&&E.constructor,D=typeof R=="function"&&R.prototype||Xt;return E===D}function zl(E){var R=[];if(E!=null)for(var D in Object(E))R.push(D);return R}function Ul(E){return Co.call(E)}function Mo(E,R,D){return R=Tr(R===void 0?E.length-1:R,0),function(){for(var ue=arguments,Oe=-1,Pe=Tr(ue.length-R,0),Me=Array(Pe);++Oe<Pe;)Me[Oe]=ue[R+Oe];Oe=-1;for(var Ce=Array(R+1);++Oe<R;)Ce[Oe]=ue[Oe];return Ce[R]=D(Me),An(E,this,Ce)}}function Br(E,R){if(!(R==="constructor"&&typeof E[R]=="function")&&R!="__proto__")return E[R]}var $l=Hl(kt);function Hl(E){var R=0,D=0;return function(){var ue=Ro(),Oe=d-(ue-D);if(D=ue,Oe>0){if(++R>=c)return arguments[0]}else R=0;return E.apply(void 0,arguments)}}function Bo(E){if(E!=null){try{return Rn.call(E)}catch{}try{return E+""}catch{}}return""}function mn(E,R){return E===R||E!==E&&R!==R}var Nt=jo(function(){return arguments}())?jo:function(E){return Wt(E)&&Ht.call(E,"callee")&&!Ol.call(E,"callee")},Lt=Array.isArray;function Ft(E){return E!=null&&Vl(E.length)&&!jt(E)}function dr(E){return Wt(E)&&Ft(E)}var Pn=Ao||Mn;function jt(E){if(!tn(E))return!1;var R=In(E);return R==A||R==b||R==m||R==B}function Vl(E){return typeof E=="number"&&E>-1&&E%1==0&&E<=f}function tn(E){var R=typeof E;return E!=null&&(R=="object"||R=="function")}function Wt(E){return E!=null&&typeof E=="object"}function Sa(E){if(!Wt(E)||In(E)!=j)return!1;var R=Ln(E);if(R===null)return!0;var D=Ht.call(R,"constructor")&&R.constructor;return typeof D=="function"&&D instanceof D&&Rn.call(D)==_l}var zo=rr?pn(rr):ma;function zr(E){return Ir(E,Uo(E))}function Uo(E){return Ft(E)?Dl(E):Ml(E)}var ba=ur(function(E,R,D){sr(E,R,D)});function fr(E){return function(){return E}}function Dn(E){return E}function Mn(){return!1}n.exports=ba}(gl,gl.exports)),gl.exports}var Yg=Gg();const Zg=kl(Yg);var Js={exports:{}},Ae={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bf;function qg(){if(bf)return Ae;bf=1;var n=Symbol.for("react.element"),o=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),y=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),k=Symbol.iterator;function C(O){return O===null||typeof O!="object"?null:(O=k&&O[k]||O["@@iterator"],typeof O=="function"?O:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},b=Object.assign,v={};function w(O,M,ee){this.props=O,this.context=M,this.refs=v,this.updater=ee||A}w.prototype.isReactComponent={},w.prototype.setState=function(O,M){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,M,"setState")},w.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function I(){}I.prototype=w.prototype;function j(O,M,ee){this.props=O,this.context=M,this.refs=v,this.updater=ee||A}var B=j.prototype=new I;B.constructor=j,b(B,w.prototype),B.isPureReactComponent=!0;var q=Array.isArray,te=Object.prototype.hasOwnProperty,X={current:null},Y={key:!0,ref:!0,__self:!0,__source:!0};function J(O,M,ee){var me,be={},ve=null,_e=null;if(M!=null)for(me in M.ref!==void 0&&(_e=M.ref),M.key!==void 0&&(ve=""+M.key),M)te.call(M,me)&&!Y.hasOwnProperty(me)&&(be[me]=M[me]);var Se=arguments.length-2;if(Se===1)be.children=ee;else if(1<Se){for(var Ne=Array(Se),Ke=0;Ke<Se;Ke++)Ne[Ke]=arguments[Ke+2];be.children=Ne}if(O&&O.defaultProps)for(me in Se=O.defaultProps,Se)be[me]===void 0&&(be[me]=Se[me]);return{$$typeof:n,type:O,key:ve,ref:_e,props:be,_owner:X.current}}function le(O,M){return{$$typeof:n,type:O.type,key:M,ref:O.ref,props:O.props,_owner:O._owner}}function ge(O){return typeof O=="object"&&O!==null&&O.$$typeof===n}function ke(O){var M={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(ee){return M[ee]})}var U=/\/+/g;function ne(O,M){return typeof O=="object"&&O!==null&&O.key!=null?ke(""+O.key):M.toString(36)}function re(O,M,ee,me,be){var ve=typeof O;(ve==="undefined"||ve==="boolean")&&(O=null);var _e=!1;if(O===null)_e=!0;else switch(ve){case"string":case"number":_e=!0;break;case"object":switch(O.$$typeof){case n:case o:_e=!0}}if(_e)return _e=O,be=be(_e),O=me===""?"."+ne(_e,0):me,q(be)?(ee="",O!=null&&(ee=O.replace(U,"$&/")+"/"),re(be,M,ee,"",function(Ke){return Ke})):be!=null&&(ge(be)&&(be=le(be,ee+(!be.key||_e&&_e.key===be.key?"":(""+be.key).replace(U,"$&/")+"/")+O)),M.push(be)),1;if(_e=0,me=me===""?".":me+":",q(O))for(var Se=0;Se<O.length;Se++){ve=O[Se];var Ne=me+ne(ve,Se);_e+=re(ve,M,ee,Ne,be)}else if(Ne=C(O),typeof Ne=="function")for(O=Ne.call(O),Se=0;!(ve=O.next()).done;)ve=ve.value,Ne=me+ne(ve,Se++),_e+=re(ve,M,ee,Ne,be);else if(ve==="object")throw M=String(O),Error("Objects are not valid as a React child (found: "+(M==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":M)+"). If you meant to render a collection of children, use an array instead.");return _e}function de(O,M,ee){if(O==null)return O;var me=[],be=0;return re(O,me,"","",function(ve){return M.call(ee,ve,be++)}),me}function se(O){if(O._status===-1){var M=O._result;M=M(),M.then(function(ee){(O._status===0||O._status===-1)&&(O._status=1,O._result=ee)},function(ee){(O._status===0||O._status===-1)&&(O._status=2,O._result=ee)}),O._status===-1&&(O._status=0,O._result=M)}if(O._status===1)return O._result.default;throw O._result}var ie={current:null},z={transition:null},Q={ReactCurrentDispatcher:ie,ReactCurrentBatchConfig:z,ReactCurrentOwner:X};function V(){throw Error("act(...) is not supported in production builds of React.")}return Ae.Children={map:de,forEach:function(O,M,ee){de(O,function(){M.apply(this,arguments)},ee)},count:function(O){var M=0;return de(O,function(){M++}),M},toArray:function(O){return de(O,function(M){return M})||[]},only:function(O){if(!ge(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},Ae.Component=w,Ae.Fragment=l,Ae.Profiler=c,Ae.PureComponent=j,Ae.StrictMode=a,Ae.Suspense=h,Ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Q,Ae.act=V,Ae.cloneElement=function(O,M,ee){if(O==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+O+".");var me=b({},O.props),be=O.key,ve=O.ref,_e=O._owner;if(M!=null){if(M.ref!==void 0&&(ve=M.ref,_e=X.current),M.key!==void 0&&(be=""+M.key),O.type&&O.type.defaultProps)var Se=O.type.defaultProps;for(Ne in M)te.call(M,Ne)&&!Y.hasOwnProperty(Ne)&&(me[Ne]=M[Ne]===void 0&&Se!==void 0?Se[Ne]:M[Ne])}var Ne=arguments.length-2;if(Ne===1)me.children=ee;else if(1<Ne){Se=Array(Ne);for(var Ke=0;Ke<Ne;Ke++)Se[Ke]=arguments[Ke+2];me.children=Se}return{$$typeof:n,type:O.type,key:be,ref:ve,props:me,_owner:_e}},Ae.createContext=function(O){return O={$$typeof:f,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},O.Provider={$$typeof:d,_context:O},O.Consumer=O},Ae.createElement=J,Ae.createFactory=function(O){var M=J.bind(null,O);return M.type=O,M},Ae.createRef=function(){return{current:null}},Ae.forwardRef=function(O){return{$$typeof:y,render:O}},Ae.isValidElement=ge,Ae.lazy=function(O){return{$$typeof:S,_payload:{_status:-1,_result:O},_init:se}},Ae.memo=function(O,M){return{$$typeof:m,type:O,compare:M===void 0?null:M}},Ae.startTransition=function(O){var M=z.transition;z.transition={};try{O()}finally{z.transition=M}},Ae.unstable_act=V,Ae.useCallback=function(O,M){return ie.current.useCallback(O,M)},Ae.useContext=function(O){return ie.current.useContext(O)},Ae.useDebugValue=function(){},Ae.useDeferredValue=function(O){return ie.current.useDeferredValue(O)},Ae.useEffect=function(O,M){return ie.current.useEffect(O,M)},Ae.useId=function(){return ie.current.useId()},Ae.useImperativeHandle=function(O,M,ee){return ie.current.useImperativeHandle(O,M,ee)},Ae.useInsertionEffect=function(O,M){return ie.current.useInsertionEffect(O,M)},Ae.useLayoutEffect=function(O,M){return ie.current.useLayoutEffect(O,M)},Ae.useMemo=function(O,M){return ie.current.useMemo(O,M)},Ae.useReducer=function(O,M,ee){return ie.current.useReducer(O,M,ee)},Ae.useRef=function(O){return ie.current.useRef(O)},Ae.useState=function(O){return ie.current.useState(O)},Ae.useSyncExternalStore=function(O,M,ee){return ie.current.useSyncExternalStore(O,M,ee)},Ae.useTransition=function(){return ie.current.useTransition()},Ae.version="18.3.1",Ae}var _f;function Hu(){return _f||(_f=1,Js.exports=qg()),Js.exports}var N=Hu();const $=kl(N);var eu={exports:{}},hl={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cf;function Qg(){if(Cf)return hl;Cf=1;var n=Hu(),o=Symbol.for("react.element"),l=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,c=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(y,h,m){var S,k={},C=null,A=null;m!==void 0&&(C=""+m),h.key!==void 0&&(C=""+h.key),h.ref!==void 0&&(A=h.ref);for(S in h)a.call(h,S)&&!d.hasOwnProperty(S)&&(k[S]=h[S]);if(y&&y.defaultProps)for(S in h=y.defaultProps,h)k[S]===void 0&&(k[S]=h[S]);return{$$typeof:o,type:y,key:C,ref:A,props:k,_owner:c.current}}return hl.Fragment=l,hl.jsx=f,hl.jsxs=f,hl}var Of;function Kg(){return Of||(Of=1,eu.exports=Qg()),eu.exports}var g=Kg();const Xg=N.createContext(void 0),Tf=Xg;function xu(){return xu=Object.assign?Object.assign.bind():function(n){for(var o=1;o<arguments.length;o++){var l=arguments[o];for(var a in l)({}).hasOwnProperty.call(l,a)&&(n[a]=l[a])}return n},xu.apply(null,arguments)}var Rp=["shift","alt","meta","mod","ctrl"],Jg={esc:"escape",return:"enter",".":"period",",":"comma","-":"slash"," ":"space","`":"backquote","#":"backslash","+":"bracketright",ShiftLeft:"shift",ShiftRight:"shift",AltLeft:"alt",AltRight:"alt",MetaLeft:"meta",MetaRight:"meta",OSLeft:"meta",OSRight:"meta",ControlLeft:"ctrl",ControlRight:"ctrl"};function er(n){return(n&&Jg[n]||n||"").trim().toLowerCase().replace(/key|digit|numpad|arrow/,"")}function em(n){return Rp.includes(n)}function tu(n,o){return o===void 0&&(o=","),n.split(o)}function nu(n,o,l){o===void 0&&(o="+");var a=n.toLocaleLowerCase().split(o).map(function(f){return er(f)}),c={alt:a.includes("alt"),ctrl:a.includes("ctrl")||a.includes("control"),shift:a.includes("shift"),meta:a.includes("meta"),mod:a.includes("mod")},d=a.filter(function(f){return!Rp.includes(f)});return xu({},c,{keys:d,description:l,hotkey:n})}(function(){typeof document<"u"&&(document.addEventListener("keydown",function(n){n.key!==void 0&&Np([er(n.key),er(n.code)])}),document.addEventListener("keyup",function(n){n.key!==void 0&&Lp([er(n.key),er(n.code)])})),typeof window<"u"&&window.addEventListener("blur",function(){tr.clear()})})();var tr=new Set;function Vu(n){return Array.isArray(n)}function tm(n,o){o===void 0&&(o=",");var l=Vu(n)?n:n.split(o);return l.every(function(a){return tr.has(a.trim().toLowerCase())})}function Np(n){var o=Array.isArray(n)?n:[n];tr.has("meta")&&tr.forEach(function(l){return!em(l)&&tr.delete(l.toLowerCase())}),o.forEach(function(l){return tr.add(l.toLowerCase())})}function Lp(n){var o=Array.isArray(n)?n:[n];n==="meta"?tr.clear():o.forEach(function(l){return tr.delete(l.toLowerCase())})}function nm(n,o,l){(typeof l=="function"&&l(n,o)||l===!0)&&n.preventDefault()}function rm(n,o,l){return typeof l=="function"?l(n,o):l===!0||l===void 0}function om(n){return Fp(n,["input","textarea","select"])}function Fp(n,o){o===void 0&&(o=!1);var l=n.target,a=n.composed,c=null;return lm(l)&&a?c=n.composedPath()[0]&&n.composedPath()[0].tagName:c=l&&l.tagName,Vu(o)?!!(c&&o&&o.some(function(d){var f;return d.toLowerCase()===((f=c)==null?void 0:f.toLowerCase())})):!!(c&&o&&o)}function lm(n){return!!n.tagName&&!n.tagName.startsWith("-")&&n.tagName.includes("-")}function im(n,o){return n.length===0&&o?(console.warn('A hotkey has the "scopes" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'),!0):o?n.some(function(l){return o.includes(l)})||n.includes("*"):!0}var am=function(o,l,a){a===void 0&&(a=!1);var c=l.alt,d=l.meta,f=l.mod,y=l.shift,h=l.ctrl,m=l.keys,S=o.key,k=o.code,C=o.ctrlKey,A=o.metaKey,b=o.shiftKey,v=o.altKey,w=er(k),I=S.toLowerCase();if(!(m!=null&&m.includes(w))&&!(m!=null&&m.includes(I))&&!["ctrl","control","unknown","meta","alt","shift","os"].includes(w))return!1;if(!a){if(c===!v&&I!=="alt"||y===!b&&I!=="shift")return!1;if(f){if(!A&&!C)return!1}else if(d===!A&&I!=="meta"&&I!=="os"||h===!C&&I!=="ctrl"&&I!=="control")return!1}return m&&m.length===1&&(m.includes(I)||m.includes(w))?!0:m?tm(m):!m},sm=N.createContext(void 0),um=function(){return N.useContext(sm)};function jp(n,o){return n&&o&&typeof n=="object"&&typeof o=="object"?Object.keys(n).length===Object.keys(o).length&&Object.keys(n).reduce(function(l,a){return l&&jp(n[a],o[a])},!0):n===o}var cm=N.createContext({hotkeys:[],enabledScopes:[],toggleScope:function(){},enableScope:function(){},disableScope:function(){}}),dm=function(){return N.useContext(cm)};function fm(n){var o=N.useRef(void 0);return jp(o.current,n)||(o.current=n),o.current}var Af=function(o){o.stopPropagation(),o.preventDefault(),o.stopImmediatePropagation()},pm=typeof window<"u"?N.useLayoutEffect:N.useEffect;function $t(n,o,l,a){var c=N.useState(null),d=c[0],f=c[1],y=N.useRef(!1),h=l instanceof Array?a instanceof Array?void 0:a:l,m=Vu(n)?n.join(h==null?void 0:h.splitKey):n,S=l instanceof Array?l:a instanceof Array?a:void 0,k=N.useCallback(o,S??[]),C=N.useRef(k);S?C.current=k:C.current=o;var A=fm(h),b=dm(),v=b.enabledScopes,w=um();return pm(function(){if(!((A==null?void 0:A.enabled)===!1||!im(v,A==null?void 0:A.scopes))){var I=function(X,Y){var J;if(Y===void 0&&(Y=!1),!(om(X)&&!Fp(X,A==null?void 0:A.enableOnFormTags))){if(d!==null){var le=d.getRootNode();if((le instanceof Document||le instanceof ShadowRoot)&&le.activeElement!==d&&!d.contains(le.activeElement)){Af(X);return}}(J=X.target)!=null&&J.isContentEditable&&!(A!=null&&A.enableOnContentEditable)||tu(m,A==null?void 0:A.splitKey).forEach(function(ge){var ke,U=nu(ge,A==null?void 0:A.combinationKey);if(am(X,U,A==null?void 0:A.ignoreModifiers)||(ke=U.keys)!=null&&ke.includes("*")){if(A!=null&&A.ignoreEventWhen!=null&&A.ignoreEventWhen(X)||Y&&y.current)return;if(nm(X,U,A==null?void 0:A.preventDefault),!rm(X,U,A==null?void 0:A.enabled)){Af(X);return}C.current(X,U),Y||(y.current=!0)}})}},j=function(X){X.key!==void 0&&(Np(er(X.code)),((A==null?void 0:A.keydown)===void 0&&(A==null?void 0:A.keyup)!==!0||A!=null&&A.keydown)&&I(X))},B=function(X){X.key!==void 0&&(Lp(er(X.code)),y.current=!1,A!=null&&A.keyup&&I(X,!0))},q=d||(h==null?void 0:h.document)||document;return q.addEventListener("keyup",B,h==null?void 0:h.eventListenerOptions),q.addEventListener("keydown",j,h==null?void 0:h.eventListenerOptions),w&&tu(m,A==null?void 0:A.splitKey).forEach(function(te){return w.addHotkey(nu(te,A==null?void 0:A.combinationKey,A==null?void 0:A.description))}),function(){q.removeEventListener("keyup",B,h==null?void 0:h.eventListenerOptions),q.removeEventListener("keydown",j,h==null?void 0:h.eventListenerOptions),w&&tu(m,A==null?void 0:A.splitKey).forEach(function(te){return w.removeHotkey(nu(te,A==null?void 0:A.combinationKey,A==null?void 0:A.description))})}}},[d,m,A,v]),f}const hm=()=>g.jsxs("svg",{width:18,height:18,viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{d:"M0 0h24v24H0z",stroke:"none"}),g.jsx("path",{d:"M18 6L6 18M6 6l12 12"})]}),ym=()=>g.jsx("svg",{viewBox:"0 0 24 24",strokeWidth:.5,stroke:"currentColor",fill:"currentColor",width:24,height:24,children:g.jsx("path",{d:"M22 14H9V5a4 4 0 00-8 0v3a1 1 0 002 0V5a2 2 0 014 0v10a8 8 0 0016 0 1 1 0 00-1-1zm-7 7a6.01 6.01 0 01-5.917-5h11.834A6.01 6.01 0 0115 21z"})}),gm=()=>g.jsxs("svg",{width:24,height:24,viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{d:"M0 0h24v24H0z",stroke:"none"}),g.jsx("path",{d:"M16 4H9.5a3.5 3.5 0 000 7h.5M14 15V4M10 15V4M5 19h14M7 21l-2-2 2-2"})]}),mm=()=>(N.useEffect(()=>(document.documentElement.removeAttribute("data-storyloaded"),()=>document.documentElement.setAttribute("data-storyloaded","")),[]),g.jsx("div",{className:"ladle-ring-wrapper",children:g.jsxs("div",{className:"ladle-ring",children:[g.jsx("div",{}),g.jsx("div",{}),g.jsx("div",{}),g.jsx("div",{})]})})),vm=()=>g.jsxs("svg",{width:24,height:24,viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{d:"M0 0h24v24H0z",stroke:"none"}),g.jsx("path",{d:"M16 4h4v4M14 10l6-6M8 20H4v-4M4 20l6-6"})]}),wm=()=>g.jsxs("svg",{width:24,height:24,viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),g.jsx("path",{d:"M3 12h1m8 -9v1m8 8h1m-15.4 -6.4l.7 .7m12.1 -.7l-.7 .7"}),g.jsx("path",{d:"M9 16a5 5 0 1 1 6 0a3.5 3.5 0 0 0 -1 3a2 2 0 0 1 -4 0a3.5 3.5 0 0 0 -1 -3"}),g.jsx("line",{x1:9.7,y1:17,x2:14.3,y2:17})]}),Em=()=>g.jsx("div",{style:{width:"10px",marginInlineEnd:"0.5em",flexShrink:0},children:g.jsx("svg",{fill:"currentColor",viewBox:"0 0 768 1024",children:g.jsx("path",{d:"M509 64l195 218v669q0 3-4 6t-9 3H77q-5 0-9-3t-4-6V73q0-3 4-6t9-3h432zm29-64H77Q45 0 22.5 21.5T0 73v878q0 30 22.5 51.5T77 1024h614q32 0 54.5-21.5T768 951V257zm-26 256V0h-64v256q0 26 19 45t45 19h253v-64H512z"})})}),xm=({rotate:n})=>{const o="16px",l="16px";return g.jsx("div",{"aria-hidden":!0,style:{width:o,height:l,marginInlineEnd:"0.1em"},children:n?g.jsxs("svg",{style:{width:o,height:l},viewBox:"0 0 24 24",stroke:"currentColor",fill:"none",children:[g.jsx("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),g.jsx("path",{d:"M9 6l6 6l-6 6"})]}):g.jsxs("svg",{style:{width:o,height:l},viewBox:"0 0 24 24",stroke:"currentColor",fill:"none",children:[g.jsx("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),g.jsx("path",{d:"M6 9l6 6l6 -6"})]})})},km=()=>g.jsxs("svg",{width:24,height:24,viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",fill:"none",children:[g.jsx("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),g.jsx("circle",{cx:14,cy:6,r:2}),g.jsx("line",{x1:4,y1:6,x2:12,y2:6}),g.jsx("line",{x1:16,y1:6,x2:20,y2:6}),g.jsx("circle",{cx:8,cy:12,r:2}),g.jsx("line",{x1:4,y1:12,x2:6,y2:12}),g.jsx("line",{x1:10,y1:12,x2:20,y2:12}),g.jsx("circle",{cx:17,cy:18,r:2}),g.jsx("line",{x1:4,y1:18,x2:15,y2:18}),g.jsx("line",{x1:19,y1:18,x2:20,y2:18})]}),Sm=()=>g.jsxs("svg",{width:24,height:24,viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{d:"M0 0h24v24H0z",stroke:"none"}),g.jsx("path",{d:"m7 8-4 4 4 4M17 8l4 4-4 4M14 4l-4 16"})]}),bm=()=>g.jsxs("svg",{width:24,height:24,strokeWidth:2,viewBox:"0 0 24 24",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{d:"M0 0h24v24H0z",stroke:"none"}),g.jsx("circle",{cx:12,cy:12,r:9}),g.jsx("path",{d:"m10 16.5 2-3 2 3m-2-3v-2l3-1m-6 0 3 1"}),g.jsx("circle",{cx:12,cy:7.5,r:.5,fill:"currentColor"})]}),_m=()=>g.jsxs("svg",{width:24,height:24,strokeWidth:2,viewBox:"0 0 24 24",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{d:"M0 0h24v24H0z",stroke:"none"}),g.jsx("rect",{x:13,y:8,width:8,height:12,rx:1}),g.jsx("path",{d:"M18 8V5a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h9M16 9h2"})]}),Cm=()=>g.jsxs("svg",{width:24,height:24,strokeWidth:2,viewBox:"0 0 24 24",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[g.jsx("path",{d:"M0 0h24v24H0z",stroke:"none"}),g.jsx("path",{d:"M18 8a3 3 0 0 1 0 6M10 8v11a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1v-5"}),g.jsx("path",{d:"M12 8h0l4.524-3.77A.9.9 0 0 1 18 4.922v12.156a.9.9 0 0 1-1.476.692L12 14H4a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h8"})]});var ru={exports:{}},xt={},ou={exports:{}},lu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rf;function Om(){return Rf||(Rf=1,function(n){function o(z,Q){var V=z.length;z.push(Q);e:for(;0<V;){var O=V-1>>>1,M=z[O];if(0<c(M,Q))z[O]=Q,z[V]=M,V=O;else break e}}function l(z){return z.length===0?null:z[0]}function a(z){if(z.length===0)return null;var Q=z[0],V=z.pop();if(V!==Q){z[0]=V;e:for(var O=0,M=z.length,ee=M>>>1;O<ee;){var me=2*(O+1)-1,be=z[me],ve=me+1,_e=z[ve];if(0>c(be,V))ve<M&&0>c(_e,be)?(z[O]=_e,z[ve]=V,O=ve):(z[O]=be,z[me]=V,O=me);else if(ve<M&&0>c(_e,V))z[O]=_e,z[ve]=V,O=ve;else break e}}return Q}function c(z,Q){var V=z.sortIndex-Q.sortIndex;return V!==0?V:z.id-Q.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,y=f.now();n.unstable_now=function(){return f.now()-y}}var h=[],m=[],S=1,k=null,C=3,A=!1,b=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,I=typeof clearTimeout=="function"?clearTimeout:null,j=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function B(z){for(var Q=l(m);Q!==null;){if(Q.callback===null)a(m);else if(Q.startTime<=z)a(m),Q.sortIndex=Q.expirationTime,o(h,Q);else break;Q=l(m)}}function q(z){if(v=!1,B(z),!b)if(l(h)!==null)b=!0,se(te);else{var Q=l(m);Q!==null&&ie(q,Q.startTime-z)}}function te(z,Q){b=!1,v&&(v=!1,I(J),J=-1),A=!0;var V=C;try{for(B(Q),k=l(h);k!==null&&(!(k.expirationTime>Q)||z&&!ke());){var O=k.callback;if(typeof O=="function"){k.callback=null,C=k.priorityLevel;var M=O(k.expirationTime<=Q);Q=n.unstable_now(),typeof M=="function"?k.callback=M:k===l(h)&&a(h),B(Q)}else a(h);k=l(h)}if(k!==null)var ee=!0;else{var me=l(m);me!==null&&ie(q,me.startTime-Q),ee=!1}return ee}finally{k=null,C=V,A=!1}}var X=!1,Y=null,J=-1,le=5,ge=-1;function ke(){return!(n.unstable_now()-ge<le)}function U(){if(Y!==null){var z=n.unstable_now();ge=z;var Q=!0;try{Q=Y(!0,z)}finally{Q?ne():(X=!1,Y=null)}}else X=!1}var ne;if(typeof j=="function")ne=function(){j(U)};else if(typeof MessageChannel<"u"){var re=new MessageChannel,de=re.port2;re.port1.onmessage=U,ne=function(){de.postMessage(null)}}else ne=function(){w(U,0)};function se(z){Y=z,X||(X=!0,ne())}function ie(z,Q){J=w(function(){z(n.unstable_now())},Q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(z){z.callback=null},n.unstable_continueExecution=function(){b||A||(b=!0,se(te))},n.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):le=0<z?Math.floor(1e3/z):5},n.unstable_getCurrentPriorityLevel=function(){return C},n.unstable_getFirstCallbackNode=function(){return l(h)},n.unstable_next=function(z){switch(C){case 1:case 2:case 3:var Q=3;break;default:Q=C}var V=C;C=Q;try{return z()}finally{C=V}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(z,Q){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var V=C;C=z;try{return Q()}finally{C=V}},n.unstable_scheduleCallback=function(z,Q,V){var O=n.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?O+V:O):V=O,z){case 1:var M=-1;break;case 2:M=250;break;case 5:M=**********;break;case 4:M=1e4;break;default:M=5e3}return M=V+M,z={id:S++,callback:Q,priorityLevel:z,startTime:V,expirationTime:M,sortIndex:-1},V>O?(z.sortIndex=V,o(m,z),l(h)===null&&z===l(m)&&(v?(I(J),J=-1):v=!0,ie(q,V-O))):(z.sortIndex=M,o(h,z),b||A||(b=!0,se(te))),z},n.unstable_shouldYield=ke,n.unstable_wrapCallback=function(z){var Q=C;return function(){var V=C;C=Q;try{return z.apply(this,arguments)}finally{C=V}}}}(lu)),lu}var Nf;function Tm(){return Nf||(Nf=1,ou.exports=Om()),ou.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lf;function Am(){if(Lf)return xt;Lf=1;var n=Hu(),o=Tm();function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,c={};function d(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(c[e]=t,e=0;e<t.length;e++)a.add(t[e])}var y=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),h=Object.prototype.hasOwnProperty,m=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,S={},k={};function C(e){return h.call(k,e)?!0:h.call(S,e)?!1:m.test(e)?k[e]=!0:(S[e]=!0,!1)}function A(e,t,r,i){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function b(e,t,r,i){if(t===null||typeof t>"u"||A(e,t,r,i))return!0;if(i)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function v(e,t,r,i,s,u,p){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=s,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=u,this.removeEmptyString=p}var w={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){w[e]=new v(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];w[t]=new v(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){w[e]=new v(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){w[e]=new v(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){w[e]=new v(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){w[e]=new v(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){w[e]=new v(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){w[e]=new v(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){w[e]=new v(e,5,!1,e.toLowerCase(),null,!1,!1)});var I=/[\-:]([a-z])/g;function j(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(I,j);w[t]=new v(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(I,j);w[t]=new v(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(I,j);w[t]=new v(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){w[e]=new v(e,1,!1,e.toLowerCase(),null,!1,!1)}),w.xlinkHref=new v("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){w[e]=new v(e,1,!1,e.toLowerCase(),null,!0,!0)});function B(e,t,r,i){var s=w.hasOwnProperty(t)?w[t]:null;(s!==null?s.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(b(t,r,s,i)&&(r=null),i||s===null?C(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):s.mustUseProperty?e[s.propertyName]=r===null?s.type===3?!1:"":r:(t=s.attributeName,i=s.attributeNamespace,r===null?e.removeAttribute(t):(s=s.type,r=s===3||s===4&&r===!0?"":""+r,i?e.setAttributeNS(i,t,r):e.setAttribute(t,r))))}var q=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,te=Symbol.for("react.element"),X=Symbol.for("react.portal"),Y=Symbol.for("react.fragment"),J=Symbol.for("react.strict_mode"),le=Symbol.for("react.profiler"),ge=Symbol.for("react.provider"),ke=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),ne=Symbol.for("react.suspense"),re=Symbol.for("react.suspense_list"),de=Symbol.for("react.memo"),se=Symbol.for("react.lazy"),ie=Symbol.for("react.offscreen"),z=Symbol.iterator;function Q(e){return e===null||typeof e!="object"?null:(e=z&&e[z]||e["@@iterator"],typeof e=="function"?e:null)}var V=Object.assign,O;function M(e){if(O===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);O=t&&t[1]||""}return`
`+O+e}var ee=!1;function me(e,t){if(!e||ee)return"";ee=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(P){var i=P}Reflect.construct(e,[],t)}else{try{t.call()}catch(P){i=P}e.call(t.prototype)}else{try{throw Error()}catch(P){i=P}e()}}catch(P){if(P&&i&&typeof P.stack=="string"){for(var s=P.stack.split(`
`),u=i.stack.split(`
`),p=s.length-1,x=u.length-1;1<=p&&0<=x&&s[p]!==u[x];)x--;for(;1<=p&&0<=x;p--,x--)if(s[p]!==u[x]){if(p!==1||x!==1)do if(p--,x--,0>x||s[p]!==u[x]){var _=`
`+s[p].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=p&&0<=x);break}}}finally{ee=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?M(e):""}function be(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return e=me(e.type,!1),e;case 11:return e=me(e.type.render,!1),e;case 1:return e=me(e.type,!0),e;default:return""}}function ve(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Y:return"Fragment";case X:return"Portal";case le:return"Profiler";case J:return"StrictMode";case ne:return"Suspense";case re:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ke:return(e.displayName||"Context")+".Consumer";case ge:return(e._context.displayName||"Context")+".Provider";case U:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case de:return t=e.displayName||null,t!==null?t:ve(e.type)||"Memo";case se:t=e._payload,e=e._init;try{return ve(e(t))}catch{}}return null}function _e(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ve(t);case 8:return t===J?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Se(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ne(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ke(e){var t=Ne(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var s=r.get,u=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(p){i=""+p,u.call(this,p)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return i},setValue:function(p){i=""+p},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function fn(e){e._valueTracker||(e._valueTracker=Ke(e))}function rr(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),i="";return e&&(i=Ne(e)?e.checked?"true":"false":e.value),e=i,e!==r?(t.setValue(e),!0):!1}function An(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ye(e,t){var r=t.checked;return V({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function pn(e,t){var r=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;r=Se(t.value!=null?t.value:r),e._wrapperState={initialChecked:i,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function or(e,t){t=t.checked,t!=null&&B(e,"checked",t,!1)}function hn(e,t){or(e,t);var r=Se(t.value),i=t.type;if(r!=null)i==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?bo(e,t.type,r):t.hasOwnProperty("defaultValue")&&bo(e,t.type,Se(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function bl(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function bo(e,t,r){(t!=="number"||An(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Xt=Array.isArray;function Jt(e,t,r,i){if(e=e.options,t){t={};for(var s=0;s<r.length;s++)t["$"+r[s]]=!0;for(r=0;r<e.length;r++)s=t.hasOwnProperty("$"+e[r].value),e[r].selected!==s&&(e[r].selected=s),s&&i&&(e[r].defaultSelected=!0)}else{for(r=""+Se(r),t=null,s=0;s<e.length;s++){if(e[s].value===r){e[s].selected=!0,i&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Rn(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(l(91));return V({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ht(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(l(92));if(Xt(r)){if(1<r.length)throw Error(l(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Se(r)}}function _o(e,t){var r=Se(t.value),i=Se(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),i!=null&&(e.defaultValue=""+i)}function Co(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function _l(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Oo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?_l(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var yn,To=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,i,s){MSApp.execUnsafeLocalFunction(function(){return e(t,r,i,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(yn=yn||document.createElement("div"),yn.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=yn.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Nn(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Ln={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Cl=["Webkit","ms","Moz","O"];Object.keys(Ln).forEach(function(e){Cl.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ln[t]=Ln[e]})});function Ol(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Ln.hasOwnProperty(e)&&Ln[e]?(""+t).trim():t+"px"}function Tl(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var i=r.indexOf("--")===0,s=Ol(r,t[r],i);r==="float"&&(r="cssFloat"),i?e.setProperty(r,s):e[r]=s}}var gn=V({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Fn(e,t){if(t){if(gn[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(l(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(l(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(t.style!=null&&typeof t.style!="object")throw Error(l(62))}}function Ao(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tr=null;function Ro(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ar=null,Tt=null,jn=null;function en(e){if(e=Jo(e)){if(typeof Ar!="function")throw Error(l(280));var t=e.stateNode;t&&(t=Jl(t),Ar(e.stateNode,e.type,t))}}function Al(e){Tt?jn?jn.push(e):jn=[e]:Tt=e}function Rl(){if(Tt){var e=Tt,t=jn;if(jn=Tt=null,en(e),t)for(e=0;e<t.length;e++)en(t[e])}}function Nl(e,t){return e(t)}function Ll(){}var No=!1;function At(e,t,r){if(No)return e(t,r);No=!0;try{return Nl(e,t,r)}finally{No=!1,(Tt!==null||jn!==null)&&(Ll(),Rl())}}function lr(e,t){var r=e.stateNode;if(r===null)return null;var i=Jl(r);if(i===null)return null;r=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(l(231,t,typeof r));return r}var Lo=!1;if(y)try{var ir={};Object.defineProperty(ir,"passive",{get:function(){Lo=!0}}),window.addEventListener("test",ir,ir),window.removeEventListener("test",ir,ir)}catch{Lo=!1}function ca(e,t,r,i,s,u,p,x,_){var P=Array.prototype.slice.call(arguments,3);try{t.apply(r,P)}catch(G){this.onError(G)}}var ar=!1,Vt=null,Rr=!1,Fo=null,da={onError:function(e){ar=!0,Vt=e}};function fa(e,t,r,i,s,u,p,x,_){ar=!1,Vt=null,ca.apply(da,arguments)}function pa(e,t,r,i,s,u,p,x,_){if(fa.apply(this,arguments),ar){if(ar){var P=Vt;ar=!1,Vt=null}else throw Error(l(198));Rr||(Rr=!0,Fo=P)}}function yt(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function Fl(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function jl(e){if(yt(e)!==e)throw Error(l(188))}function ha(e){var t=e.alternate;if(!t){if(t=yt(e),t===null)throw Error(l(188));return t!==e?null:e}for(var r=e,i=t;;){var s=r.return;if(s===null)break;var u=s.alternate;if(u===null){if(i=s.return,i!==null){r=i;continue}break}if(s.child===u.child){for(u=s.child;u;){if(u===r)return jl(s),e;if(u===i)return jl(s),t;u=u.sibling}throw Error(l(188))}if(r.return!==i.return)r=s,i=u;else{for(var p=!1,x=s.child;x;){if(x===r){p=!0,r=s,i=u;break}if(x===i){p=!0,i=s,r=u;break}x=x.sibling}if(!p){for(x=u.child;x;){if(x===r){p=!0,r=u,i=s;break}if(x===i){p=!0,i=u,r=s;break}x=x.sibling}if(!p)throw Error(l(189))}}if(r.alternate!==i)throw Error(l(190))}if(r.tag!==3)throw Error(l(188));return r.stateNode.current===r?e:t}function Il(e){return e=ha(e),e!==null?Pl(e):null}function Pl(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Pl(e);if(t!==null)return t;e=e.sibling}return null}var Dl=o.unstable_scheduleCallback,Nr=o.unstable_cancelCallback,ya=o.unstable_shouldYield,Lr=o.unstable_requestPaint,ze=o.unstable_now,ga=o.unstable_getCurrentPriorityLevel,In=o.unstable_ImmediatePriority,jo=o.unstable_UserBlockingPriority,Fr=o.unstable_NormalPriority,ma=o.unstable_LowPriority,Ml=o.unstable_IdlePriority,sr=null,Rt=null;function va(e){if(Rt&&typeof Rt.onCommitFiberRoot=="function")try{Rt.onCommitFiberRoot(sr,e,void 0,(e.current.flags&128)===128)}catch{}}var kt=Math.clz32?Math.clz32:xa,wa=Math.log,Ea=Math.LN2;function xa(e){return e>>>=0,e===0?32:31-(wa(e)/Ea|0)|0}var jr=64,Ir=4194304;function ur(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Pr(e,t){var r=e.pendingLanes;if(r===0)return 0;var i=0,s=e.suspendedLanes,u=e.pingedLanes,p=r&268435455;if(p!==0){var x=p&~s;x!==0?i=ur(x):(u&=p,u!==0&&(i=ur(u)))}else p=r&~s,p!==0?i=ur(p):u!==0&&(i=ur(u));if(i===0)return 0;if(t!==0&&t!==i&&(t&s)===0&&(s=i&-i,u=t&-t,s>=u||s===16&&(u&4194240)!==0))return t;if((i&4)!==0&&(i|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)r=31-kt(t),s=1<<r,i|=e[r],t&=~s;return i}function Dr(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Io(e,t){for(var r=e.suspendedLanes,i=e.pingedLanes,s=e.expirationTimes,u=e.pendingLanes;0<u;){var p=31-kt(u),x=1<<p,_=s[p];_===-1?((x&r)===0||(x&i)!==0)&&(s[p]=Dr(x,t)):_<=t&&(e.expiredLanes|=x),u&=~x}}function Po(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Bl(){var e=jr;return jr<<=1,(jr&4194240)===0&&(jr=64),e}function Mr(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function cr(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-kt(t),e[t]=r}function ka(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<r;){var s=31-kt(r),u=1<<s;t[s]=0,i[s]=-1,e[s]=-1,r&=~u}}function Do(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var i=31-kt(r),s=1<<i;s&t|e[i]&t&&(e[i]|=t),r&=~s}}var Fe=0;function zl(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Ul,Mo,Br,$l,Hl,Bo=!1,mn=[],Nt=null,Lt=null,Ft=null,dr=new Map,Pn=new Map,jt=[],Vl="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function tn(e,t){switch(e){case"focusin":case"focusout":Nt=null;break;case"dragenter":case"dragleave":Lt=null;break;case"mouseover":case"mouseout":Ft=null;break;case"pointerover":case"pointerout":dr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pn.delete(t.pointerId)}}function Wt(e,t,r,i,s,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:r,eventSystemFlags:i,nativeEvent:u,targetContainers:[s]},t!==null&&(t=Jo(t),t!==null&&Mo(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Sa(e,t,r,i,s){switch(t){case"focusin":return Nt=Wt(Nt,e,t,r,i,s),!0;case"dragenter":return Lt=Wt(Lt,e,t,r,i,s),!0;case"mouseover":return Ft=Wt(Ft,e,t,r,i,s),!0;case"pointerover":var u=s.pointerId;return dr.set(u,Wt(dr.get(u)||null,e,t,r,i,s)),!0;case"gotpointercapture":return u=s.pointerId,Pn.set(u,Wt(Pn.get(u)||null,e,t,r,i,s)),!0}return!1}function zo(e){var t=pr(e.target);if(t!==null){var r=yt(t);if(r!==null){if(t=r.tag,t===13){if(t=Fl(r),t!==null){e.blockedOn=t,Hl(e.priority,function(){Br(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function zr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Pe(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var i=new r.constructor(r.type,r);Tr=i,r.target.dispatchEvent(i),Tr=null}else return t=Jo(r),t!==null&&Mo(t),e.blockedOn=r,!1;t.shift()}return!0}function Uo(e,t,r){zr(e)&&r.delete(t)}function ba(){Bo=!1,Nt!==null&&zr(Nt)&&(Nt=null),Lt!==null&&zr(Lt)&&(Lt=null),Ft!==null&&zr(Ft)&&(Ft=null),dr.forEach(Uo),Pn.forEach(Uo)}function fr(e,t){e.blockedOn===t&&(e.blockedOn=null,Bo||(Bo=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,ba)))}function Dn(e){function t(s){return fr(s,e)}if(0<mn.length){fr(mn[0],e);for(var r=1;r<mn.length;r++){var i=mn[r];i.blockedOn===e&&(i.blockedOn=null)}}for(Nt!==null&&fr(Nt,e),Lt!==null&&fr(Lt,e),Ft!==null&&fr(Ft,e),dr.forEach(t),Pn.forEach(t),r=0;r<jt.length;r++)i=jt[r],i.blockedOn===e&&(i.blockedOn=null);for(;0<jt.length&&(r=jt[0],r.blockedOn===null);)zo(r),r.blockedOn===null&&jt.shift()}var Mn=q.ReactCurrentBatchConfig,E=!0;function R(e,t,r,i){var s=Fe,u=Mn.transition;Mn.transition=null;try{Fe=1,ue(e,t,r,i)}finally{Fe=s,Mn.transition=u}}function D(e,t,r,i){var s=Fe,u=Mn.transition;Mn.transition=null;try{Fe=4,ue(e,t,r,i)}finally{Fe=s,Mn.transition=u}}function ue(e,t,r,i){if(E){var s=Pe(e,t,r,i);if(s===null)za(e,t,i,Oe,r),tn(e,i);else if(Sa(s,e,t,r,i))i.stopPropagation();else if(tn(e,i),t&4&&-1<Vl.indexOf(e)){for(;s!==null;){var u=Jo(s);if(u!==null&&Ul(u),u=Pe(e,t,r,i),u===null&&za(e,t,i,Oe,r),u===s)break;s=u}s!==null&&i.stopPropagation()}else za(e,t,i,null,r)}}var Oe=null;function Pe(e,t,r,i){if(Oe=null,e=Ro(i),e=pr(e),e!==null)if(t=yt(e),t===null)e=null;else if(r=t.tag,r===13){if(e=Fl(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Oe=e,null}function Me(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ga()){case In:return 1;case jo:return 4;case Fr:case ma:return 16;case Ml:return 536870912;default:return 16}default:return 16}}var Ce=null,De=null,lt=null;function it(){if(lt)return lt;var e,t=De,r=t.length,i,s="value"in Ce?Ce.value:Ce.textContent,u=s.length;for(e=0;e<r&&t[e]===s[e];e++);var p=r-e;for(i=1;i<=p&&t[r-i]===s[u-i];i++);return lt=s.slice(e,1<i?1-i:void 0)}function nn(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Bn(){return!0}function Ur(){return!1}function at(e){function t(r,i,s,u,p){this._reactName=r,this._targetInst=s,this.type=i,this.nativeEvent=u,this.target=p,this.currentTarget=null;for(var x in e)e.hasOwnProperty(x)&&(r=e[x],this[x]=r?r(u):u[x]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Bn:Ur,this.isPropagationStopped=Ur,this}return V(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Bn)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Bn)},persist:function(){},isPersistent:Bn}),t}var $r={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_a=at($r),$o=V({},$r,{view:0,detail:0}),ey=at($o),Ca,Oa,Ho,Wl=V({},$o,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Aa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ho&&(Ho&&e.type==="mousemove"?(Ca=e.screenX-Ho.screenX,Oa=e.screenY-Ho.screenY):Oa=Ca=0,Ho=e),Ca)},movementY:function(e){return"movementY"in e?e.movementY:Oa}}),lc=at(Wl),ty=V({},Wl,{dataTransfer:0}),ny=at(ty),ry=V({},$o,{relatedTarget:0}),Ta=at(ry),oy=V({},$r,{animationName:0,elapsedTime:0,pseudoElement:0}),ly=at(oy),iy=V({},$r,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ay=at(iy),sy=V({},$r,{data:0}),ic=at(sy),uy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},cy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=dy[e])?!!t[e]:!1}function Aa(){return fy}var py=V({},$o,{key:function(e){if(e.key){var t=uy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=nn(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?cy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Aa,charCode:function(e){return e.type==="keypress"?nn(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?nn(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),hy=at(py),yy=V({},Wl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ac=at(yy),gy=V({},$o,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Aa}),my=at(gy),vy=V({},$r,{propertyName:0,elapsedTime:0,pseudoElement:0}),wy=at(vy),Ey=V({},Wl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),xy=at(Ey),ky=[9,13,27,32],Ra=y&&"CompositionEvent"in window,Vo=null;y&&"documentMode"in document&&(Vo=document.documentMode);var Sy=y&&"TextEvent"in window&&!Vo,sc=y&&(!Ra||Vo&&8<Vo&&11>=Vo),uc=" ",cc=!1;function dc(e,t){switch(e){case"keyup":return ky.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function fc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Hr=!1;function by(e,t){switch(e){case"compositionend":return fc(t);case"keypress":return t.which!==32?null:(cc=!0,uc);case"textInput":return e=t.data,e===uc&&cc?null:e;default:return null}}function _y(e,t){if(Hr)return e==="compositionend"||!Ra&&dc(e,t)?(e=it(),lt=De=Ce=null,Hr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return sc&&t.locale!=="ko"?null:t.data;default:return null}}var Cy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function pc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Cy[e.type]:t==="textarea"}function hc(e,t,r,i){Al(i),t=Ql(t,"onChange"),0<t.length&&(r=new _a("onChange","change",null,r,i),e.push({event:r,listeners:t}))}var Wo=null,Go=null;function Oy(e){Lc(e,0)}function Gl(e){var t=Zr(e);if(rr(t))return e}function Ty(e,t){if(e==="change")return t}var yc=!1;if(y){var Na;if(y){var La="oninput"in document;if(!La){var gc=document.createElement("div");gc.setAttribute("oninput","return;"),La=typeof gc.oninput=="function"}Na=La}else Na=!1;yc=Na&&(!document.documentMode||9<document.documentMode)}function mc(){Wo&&(Wo.detachEvent("onpropertychange",vc),Go=Wo=null)}function vc(e){if(e.propertyName==="value"&&Gl(Go)){var t=[];hc(t,Go,e,Ro(e)),At(Oy,t)}}function Ay(e,t,r){e==="focusin"?(mc(),Wo=t,Go=r,Wo.attachEvent("onpropertychange",vc)):e==="focusout"&&mc()}function Ry(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Gl(Go)}function Ny(e,t){if(e==="click")return Gl(t)}function Ly(e,t){if(e==="input"||e==="change")return Gl(t)}function Fy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Gt=typeof Object.is=="function"?Object.is:Fy;function Yo(e,t){if(Gt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),i=Object.keys(t);if(r.length!==i.length)return!1;for(i=0;i<r.length;i++){var s=r[i];if(!h.call(t,s)||!Gt(e[s],t[s]))return!1}return!0}function wc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ec(e,t){var r=wc(e);e=0;for(var i;r;){if(r.nodeType===3){if(i=e+r.textContent.length,e<=t&&i>=t)return{node:r,offset:t-e};e=i}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=wc(r)}}function xc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?xc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function kc(){for(var e=window,t=An();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=An(e.document)}return t}function Fa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function jy(e){var t=kc(),r=e.focusedElem,i=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&xc(r.ownerDocument.documentElement,r)){if(i!==null&&Fa(r)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=r.textContent.length,u=Math.min(i.start,s);i=i.end===void 0?u:Math.min(i.end,s),!e.extend&&u>i&&(s=i,i=u,u=s),s=Ec(r,u);var p=Ec(r,i);s&&p&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==p.node||e.focusOffset!==p.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),u>i?(e.addRange(t),e.extend(p.node,p.offset)):(t.setEnd(p.node,p.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Iy=y&&"documentMode"in document&&11>=document.documentMode,Vr=null,ja=null,Zo=null,Ia=!1;function Sc(e,t,r){var i=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Ia||Vr==null||Vr!==An(i)||(i=Vr,"selectionStart"in i&&Fa(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Zo&&Yo(Zo,i)||(Zo=i,i=Ql(ja,"onSelect"),0<i.length&&(t=new _a("onSelect","select",null,t,r),e.push({event:t,listeners:i}),t.target=Vr)))}function Yl(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Wr={animationend:Yl("Animation","AnimationEnd"),animationiteration:Yl("Animation","AnimationIteration"),animationstart:Yl("Animation","AnimationStart"),transitionend:Yl("Transition","TransitionEnd")},Pa={},bc={};y&&(bc=document.createElement("div").style,"AnimationEvent"in window||(delete Wr.animationend.animation,delete Wr.animationiteration.animation,delete Wr.animationstart.animation),"TransitionEvent"in window||delete Wr.transitionend.transition);function Zl(e){if(Pa[e])return Pa[e];if(!Wr[e])return e;var t=Wr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in bc)return Pa[e]=t[r];return e}var _c=Zl("animationend"),Cc=Zl("animationiteration"),Oc=Zl("animationstart"),Tc=Zl("transitionend"),Ac=new Map,Rc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function zn(e,t){Ac.set(e,t),d(t,[e])}for(var Da=0;Da<Rc.length;Da++){var Ma=Rc[Da],Py=Ma.toLowerCase(),Dy=Ma[0].toUpperCase()+Ma.slice(1);zn(Py,"on"+Dy)}zn(_c,"onAnimationEnd"),zn(Cc,"onAnimationIteration"),zn(Oc,"onAnimationStart"),zn("dblclick","onDoubleClick"),zn("focusin","onFocus"),zn("focusout","onBlur"),zn(Tc,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),d("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),d("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),d("onBeforeInput",["compositionend","keypress","textInput","paste"]),d("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var qo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),My=new Set("cancel close invalid load scroll toggle".split(" ").concat(qo));function Nc(e,t,r){var i=e.type||"unknown-event";e.currentTarget=r,pa(i,t,void 0,e),e.currentTarget=null}function Lc(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var i=e[r],s=i.event;i=i.listeners;e:{var u=void 0;if(t)for(var p=i.length-1;0<=p;p--){var x=i[p],_=x.instance,P=x.currentTarget;if(x=x.listener,_!==u&&s.isPropagationStopped())break e;Nc(s,x,P),u=_}else for(p=0;p<i.length;p++){if(x=i[p],_=x.instance,P=x.currentTarget,x=x.listener,_!==u&&s.isPropagationStopped())break e;Nc(s,x,P),u=_}}}if(Rr)throw e=Fo,Rr=!1,Fo=null,e}function Ue(e,t){var r=t[Ga];r===void 0&&(r=t[Ga]=new Set);var i=e+"__bubble";r.has(i)||(Fc(t,e,2,!1),r.add(i))}function Ba(e,t,r){var i=0;t&&(i|=4),Fc(r,e,i,t)}var ql="_reactListening"+Math.random().toString(36).slice(2);function Qo(e){if(!e[ql]){e[ql]=!0,a.forEach(function(r){r!=="selectionchange"&&(My.has(r)||Ba(r,!1,e),Ba(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ql]||(t[ql]=!0,Ba("selectionchange",!1,t))}}function Fc(e,t,r,i){switch(Me(t)){case 1:var s=R;break;case 4:s=D;break;default:s=ue}r=s.bind(null,t,r,e),s=void 0,!Lo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),i?s!==void 0?e.addEventListener(t,r,{capture:!0,passive:s}):e.addEventListener(t,r,!0):s!==void 0?e.addEventListener(t,r,{passive:s}):e.addEventListener(t,r,!1)}function za(e,t,r,i,s){var u=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var p=i.tag;if(p===3||p===4){var x=i.stateNode.containerInfo;if(x===s||x.nodeType===8&&x.parentNode===s)break;if(p===4)for(p=i.return;p!==null;){var _=p.tag;if((_===3||_===4)&&(_=p.stateNode.containerInfo,_===s||_.nodeType===8&&_.parentNode===s))return;p=p.return}for(;x!==null;){if(p=pr(x),p===null)return;if(_=p.tag,_===5||_===6){i=u=p;continue e}x=x.parentNode}}i=i.return}At(function(){var P=u,G=Ro(r),Z=[];e:{var W=Ac.get(e);if(W!==void 0){var oe=_a,ce=e;switch(e){case"keypress":if(nn(r)===0)break e;case"keydown":case"keyup":oe=hy;break;case"focusin":ce="focus",oe=Ta;break;case"focusout":ce="blur",oe=Ta;break;case"beforeblur":case"afterblur":oe=Ta;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":oe=lc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":oe=ny;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":oe=my;break;case _c:case Cc:case Oc:oe=ly;break;case Tc:oe=wy;break;case"scroll":oe=ey;break;case"wheel":oe=xy;break;case"copy":case"cut":case"paste":oe=ay;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":oe=ac}var fe=(t&4)!==0,Ye=!fe&&e==="scroll",L=fe?W!==null?W+"Capture":null:W;fe=[];for(var T=P,F;T!==null;){F=T;var K=F.stateNode;if(F.tag===5&&K!==null&&(F=K,L!==null&&(K=lr(T,L),K!=null&&fe.push(Ko(T,K,F)))),Ye)break;T=T.return}0<fe.length&&(W=new oe(W,ce,null,r,G),Z.push({event:W,listeners:fe}))}}if((t&7)===0){e:{if(W=e==="mouseover"||e==="pointerover",oe=e==="mouseout"||e==="pointerout",W&&r!==Tr&&(ce=r.relatedTarget||r.fromElement)&&(pr(ce)||ce[vn]))break e;if((oe||W)&&(W=G.window===G?G:(W=G.ownerDocument)?W.defaultView||W.parentWindow:window,oe?(ce=r.relatedTarget||r.toElement,oe=P,ce=ce?pr(ce):null,ce!==null&&(Ye=yt(ce),ce!==Ye||ce.tag!==5&&ce.tag!==6)&&(ce=null)):(oe=null,ce=P),oe!==ce)){if(fe=lc,K="onMouseLeave",L="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(fe=ac,K="onPointerLeave",L="onPointerEnter",T="pointer"),Ye=oe==null?W:Zr(oe),F=ce==null?W:Zr(ce),W=new fe(K,T+"leave",oe,r,G),W.target=Ye,W.relatedTarget=F,K=null,pr(G)===P&&(fe=new fe(L,T+"enter",ce,r,G),fe.target=F,fe.relatedTarget=Ye,K=fe),Ye=K,oe&&ce)t:{for(fe=oe,L=ce,T=0,F=fe;F;F=Gr(F))T++;for(F=0,K=L;K;K=Gr(K))F++;for(;0<T-F;)fe=Gr(fe),T--;for(;0<F-T;)L=Gr(L),F--;for(;T--;){if(fe===L||L!==null&&fe===L.alternate)break t;fe=Gr(fe),L=Gr(L)}fe=null}else fe=null;oe!==null&&jc(Z,W,oe,fe,!1),ce!==null&&Ye!==null&&jc(Z,Ye,ce,fe,!0)}}e:{if(W=P?Zr(P):window,oe=W.nodeName&&W.nodeName.toLowerCase(),oe==="select"||oe==="input"&&W.type==="file")var pe=Ty;else if(pc(W))if(yc)pe=Ly;else{pe=Ry;var we=Ay}else(oe=W.nodeName)&&oe.toLowerCase()==="input"&&(W.type==="checkbox"||W.type==="radio")&&(pe=Ny);if(pe&&(pe=pe(e,P))){hc(Z,pe,r,G);break e}we&&we(e,W,P),e==="focusout"&&(we=W._wrapperState)&&we.controlled&&W.type==="number"&&bo(W,"number",W.value)}switch(we=P?Zr(P):window,e){case"focusin":(pc(we)||we.contentEditable==="true")&&(Vr=we,ja=P,Zo=null);break;case"focusout":Zo=ja=Vr=null;break;case"mousedown":Ia=!0;break;case"contextmenu":case"mouseup":case"dragend":Ia=!1,Sc(Z,r,G);break;case"selectionchange":if(Iy)break;case"keydown":case"keyup":Sc(Z,r,G)}var Ee;if(Ra)e:{switch(e){case"compositionstart":var xe="onCompositionStart";break e;case"compositionend":xe="onCompositionEnd";break e;case"compositionupdate":xe="onCompositionUpdate";break e}xe=void 0}else Hr?dc(e,r)&&(xe="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(xe="onCompositionStart");xe&&(sc&&r.locale!=="ko"&&(Hr||xe!=="onCompositionStart"?xe==="onCompositionEnd"&&Hr&&(Ee=it()):(Ce=G,De="value"in Ce?Ce.value:Ce.textContent,Hr=!0)),we=Ql(P,xe),0<we.length&&(xe=new ic(xe,e,null,r,G),Z.push({event:xe,listeners:we}),Ee?xe.data=Ee:(Ee=fc(r),Ee!==null&&(xe.data=Ee)))),(Ee=Sy?by(e,r):_y(e,r))&&(P=Ql(P,"onBeforeInput"),0<P.length&&(G=new ic("onBeforeInput","beforeinput",null,r,G),Z.push({event:G,listeners:P}),G.data=Ee))}Lc(Z,t)})}function Ko(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Ql(e,t){for(var r=t+"Capture",i=[];e!==null;){var s=e,u=s.stateNode;s.tag===5&&u!==null&&(s=u,u=lr(e,r),u!=null&&i.unshift(Ko(e,u,s)),u=lr(e,t),u!=null&&i.push(Ko(e,u,s))),e=e.return}return i}function Gr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function jc(e,t,r,i,s){for(var u=t._reactName,p=[];r!==null&&r!==i;){var x=r,_=x.alternate,P=x.stateNode;if(_!==null&&_===i)break;x.tag===5&&P!==null&&(x=P,s?(_=lr(r,u),_!=null&&p.unshift(Ko(r,_,x))):s||(_=lr(r,u),_!=null&&p.push(Ko(r,_,x)))),r=r.return}p.length!==0&&e.push({event:t,listeners:p})}var By=/\r\n?/g,zy=/\u0000|\uFFFD/g;function Ic(e){return(typeof e=="string"?e:""+e).replace(By,`
`).replace(zy,"")}function Kl(e,t,r){if(t=Ic(t),Ic(e)!==t&&r)throw Error(l(425))}function Xl(){}var Ua=null,$a=null;function Ha(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Va=typeof setTimeout=="function"?setTimeout:void 0,Uy=typeof clearTimeout=="function"?clearTimeout:void 0,Pc=typeof Promise=="function"?Promise:void 0,$y=typeof queueMicrotask=="function"?queueMicrotask:typeof Pc<"u"?function(e){return Pc.resolve(null).then(e).catch(Hy)}:Va;function Hy(e){setTimeout(function(){throw e})}function Wa(e,t){var r=t,i=0;do{var s=r.nextSibling;if(e.removeChild(r),s&&s.nodeType===8)if(r=s.data,r==="/$"){if(i===0){e.removeChild(s),Dn(t);return}i--}else r!=="$"&&r!=="$?"&&r!=="$!"||i++;r=s}while(r);Dn(t)}function Un(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Dc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Yr=Math.random().toString(36).slice(2),rn="__reactFiber$"+Yr,Xo="__reactProps$"+Yr,vn="__reactContainer$"+Yr,Ga="__reactEvents$"+Yr,Vy="__reactListeners$"+Yr,Wy="__reactHandles$"+Yr;function pr(e){var t=e[rn];if(t)return t;for(var r=e.parentNode;r;){if(t=r[vn]||r[rn]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=Dc(e);e!==null;){if(r=e[rn])return r;e=Dc(e)}return t}e=r,r=e.parentNode}return null}function Jo(e){return e=e[rn]||e[vn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Zr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(l(33))}function Jl(e){return e[Xo]||null}var Ya=[],qr=-1;function $n(e){return{current:e}}function $e(e){0>qr||(e.current=Ya[qr],Ya[qr]=null,qr--)}function Be(e,t){qr++,Ya[qr]=e.current,e.current=t}var Hn={},st=$n(Hn),gt=$n(!1),hr=Hn;function Qr(e,t){var r=e.type.contextTypes;if(!r)return Hn;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var s={},u;for(u in r)s[u]=t[u];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function mt(e){return e=e.childContextTypes,e!=null}function ei(){$e(gt),$e(st)}function Mc(e,t,r){if(st.current!==Hn)throw Error(l(168));Be(st,t),Be(gt,r)}function Bc(e,t,r){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return r;i=i.getChildContext();for(var s in i)if(!(s in t))throw Error(l(108,_e(e)||"Unknown",s));return V({},r,i)}function ti(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Hn,hr=st.current,Be(st,e),Be(gt,gt.current),!0}function zc(e,t,r){var i=e.stateNode;if(!i)throw Error(l(169));r?(e=Bc(e,t,hr),i.__reactInternalMemoizedMergedChildContext=e,$e(gt),$e(st),Be(st,e)):$e(gt),Be(gt,r)}var wn=null,ni=!1,Za=!1;function Uc(e){wn===null?wn=[e]:wn.push(e)}function Gy(e){ni=!0,Uc(e)}function Vn(){if(!Za&&wn!==null){Za=!0;var e=0,t=Fe;try{var r=wn;for(Fe=1;e<r.length;e++){var i=r[e];do i=i(!0);while(i!==null)}wn=null,ni=!1}catch(s){throw wn!==null&&(wn=wn.slice(e+1)),Dl(In,Vn),s}finally{Fe=t,Za=!1}}return null}var Kr=[],Xr=0,ri=null,oi=0,It=[],Pt=0,yr=null,En=1,xn="";function gr(e,t){Kr[Xr++]=oi,Kr[Xr++]=ri,ri=e,oi=t}function $c(e,t,r){It[Pt++]=En,It[Pt++]=xn,It[Pt++]=yr,yr=e;var i=En;e=xn;var s=32-kt(i)-1;i&=~(1<<s),r+=1;var u=32-kt(t)+s;if(30<u){var p=s-s%5;u=(i&(1<<p)-1).toString(32),i>>=p,s-=p,En=1<<32-kt(t)+s|r<<s|i,xn=u+e}else En=1<<u|r<<s|i,xn=e}function qa(e){e.return!==null&&(gr(e,1),$c(e,1,0))}function Qa(e){for(;e===ri;)ri=Kr[--Xr],Kr[Xr]=null,oi=Kr[--Xr],Kr[Xr]=null;for(;e===yr;)yr=It[--Pt],It[Pt]=null,xn=It[--Pt],It[Pt]=null,En=It[--Pt],It[Pt]=null}var St=null,bt=null,He=!1,Yt=null;function Hc(e,t){var r=zt(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function Vc(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,St=e,bt=Un(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,St=e,bt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=yr!==null?{id:En,overflow:xn}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=zt(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,St=e,bt=null,!0):!1;default:return!1}}function Ka(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Xa(e){if(He){var t=bt;if(t){var r=t;if(!Vc(e,t)){if(Ka(e))throw Error(l(418));t=Un(r.nextSibling);var i=St;t&&Vc(e,t)?Hc(i,r):(e.flags=e.flags&-4097|2,He=!1,St=e)}}else{if(Ka(e))throw Error(l(418));e.flags=e.flags&-4097|2,He=!1,St=e}}}function Wc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;St=e}function li(e){if(e!==St)return!1;if(!He)return Wc(e),He=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ha(e.type,e.memoizedProps)),t&&(t=bt)){if(Ka(e))throw Gc(),Error(l(418));for(;t;)Hc(e,t),t=Un(t.nextSibling)}if(Wc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){bt=Un(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}bt=null}}else bt=St?Un(e.stateNode.nextSibling):null;return!0}function Gc(){for(var e=bt;e;)e=Un(e.nextSibling)}function Jr(){bt=St=null,He=!1}function Ja(e){Yt===null?Yt=[e]:Yt.push(e)}var Yy=q.ReactCurrentBatchConfig;function el(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(l(309));var i=r.stateNode}if(!i)throw Error(l(147,e));var s=i,u=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===u?t.ref:(t=function(p){var x=s.refs;p===null?delete x[u]:x[u]=p},t._stringRef=u,t)}if(typeof e!="string")throw Error(l(284));if(!r._owner)throw Error(l(290,e))}return e}function ii(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Yc(e){var t=e._init;return t(e._payload)}function Zc(e){function t(L,T){if(e){var F=L.deletions;F===null?(L.deletions=[T],L.flags|=16):F.push(T)}}function r(L,T){if(!e)return null;for(;T!==null;)t(L,T),T=T.sibling;return null}function i(L,T){for(L=new Map;T!==null;)T.key!==null?L.set(T.key,T):L.set(T.index,T),T=T.sibling;return L}function s(L,T){return L=Xn(L,T),L.index=0,L.sibling=null,L}function u(L,T,F){return L.index=F,e?(F=L.alternate,F!==null?(F=F.index,F<T?(L.flags|=2,T):F):(L.flags|=2,T)):(L.flags|=1048576,T)}function p(L){return e&&L.alternate===null&&(L.flags|=2),L}function x(L,T,F,K){return T===null||T.tag!==6?(T=Vs(F,L.mode,K),T.return=L,T):(T=s(T,F),T.return=L,T)}function _(L,T,F,K){var pe=F.type;return pe===Y?G(L,T,F.props.children,K,F.key):T!==null&&(T.elementType===pe||typeof pe=="object"&&pe!==null&&pe.$$typeof===se&&Yc(pe)===T.type)?(K=s(T,F.props),K.ref=el(L,T,F),K.return=L,K):(K=Ri(F.type,F.key,F.props,null,L.mode,K),K.ref=el(L,T,F),K.return=L,K)}function P(L,T,F,K){return T===null||T.tag!==4||T.stateNode.containerInfo!==F.containerInfo||T.stateNode.implementation!==F.implementation?(T=Ws(F,L.mode,K),T.return=L,T):(T=s(T,F.children||[]),T.return=L,T)}function G(L,T,F,K,pe){return T===null||T.tag!==7?(T=br(F,L.mode,K,pe),T.return=L,T):(T=s(T,F),T.return=L,T)}function Z(L,T,F){if(typeof T=="string"&&T!==""||typeof T=="number")return T=Vs(""+T,L.mode,F),T.return=L,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case te:return F=Ri(T.type,T.key,T.props,null,L.mode,F),F.ref=el(L,null,T),F.return=L,F;case X:return T=Ws(T,L.mode,F),T.return=L,T;case se:var K=T._init;return Z(L,K(T._payload),F)}if(Xt(T)||Q(T))return T=br(T,L.mode,F,null),T.return=L,T;ii(L,T)}return null}function W(L,T,F,K){var pe=T!==null?T.key:null;if(typeof F=="string"&&F!==""||typeof F=="number")return pe!==null?null:x(L,T,""+F,K);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case te:return F.key===pe?_(L,T,F,K):null;case X:return F.key===pe?P(L,T,F,K):null;case se:return pe=F._init,W(L,T,pe(F._payload),K)}if(Xt(F)||Q(F))return pe!==null?null:G(L,T,F,K,null);ii(L,F)}return null}function oe(L,T,F,K,pe){if(typeof K=="string"&&K!==""||typeof K=="number")return L=L.get(F)||null,x(T,L,""+K,pe);if(typeof K=="object"&&K!==null){switch(K.$$typeof){case te:return L=L.get(K.key===null?F:K.key)||null,_(T,L,K,pe);case X:return L=L.get(K.key===null?F:K.key)||null,P(T,L,K,pe);case se:var we=K._init;return oe(L,T,F,we(K._payload),pe)}if(Xt(K)||Q(K))return L=L.get(F)||null,G(T,L,K,pe,null);ii(T,K)}return null}function ce(L,T,F,K){for(var pe=null,we=null,Ee=T,xe=T=0,nt=null;Ee!==null&&xe<F.length;xe++){Ee.index>xe?(nt=Ee,Ee=null):nt=Ee.sibling;var je=W(L,Ee,F[xe],K);if(je===null){Ee===null&&(Ee=nt);break}e&&Ee&&je.alternate===null&&t(L,Ee),T=u(je,T,xe),we===null?pe=je:we.sibling=je,we=je,Ee=nt}if(xe===F.length)return r(L,Ee),He&&gr(L,xe),pe;if(Ee===null){for(;xe<F.length;xe++)Ee=Z(L,F[xe],K),Ee!==null&&(T=u(Ee,T,xe),we===null?pe=Ee:we.sibling=Ee,we=Ee);return He&&gr(L,xe),pe}for(Ee=i(L,Ee);xe<F.length;xe++)nt=oe(Ee,L,xe,F[xe],K),nt!==null&&(e&&nt.alternate!==null&&Ee.delete(nt.key===null?xe:nt.key),T=u(nt,T,xe),we===null?pe=nt:we.sibling=nt,we=nt);return e&&Ee.forEach(function(Jn){return t(L,Jn)}),He&&gr(L,xe),pe}function fe(L,T,F,K){var pe=Q(F);if(typeof pe!="function")throw Error(l(150));if(F=pe.call(F),F==null)throw Error(l(151));for(var we=pe=null,Ee=T,xe=T=0,nt=null,je=F.next();Ee!==null&&!je.done;xe++,je=F.next()){Ee.index>xe?(nt=Ee,Ee=null):nt=Ee.sibling;var Jn=W(L,Ee,je.value,K);if(Jn===null){Ee===null&&(Ee=nt);break}e&&Ee&&Jn.alternate===null&&t(L,Ee),T=u(Jn,T,xe),we===null?pe=Jn:we.sibling=Jn,we=Jn,Ee=nt}if(je.done)return r(L,Ee),He&&gr(L,xe),pe;if(Ee===null){for(;!je.done;xe++,je=F.next())je=Z(L,je.value,K),je!==null&&(T=u(je,T,xe),we===null?pe=je:we.sibling=je,we=je);return He&&gr(L,xe),pe}for(Ee=i(L,Ee);!je.done;xe++,je=F.next())je=oe(Ee,L,xe,je.value,K),je!==null&&(e&&je.alternate!==null&&Ee.delete(je.key===null?xe:je.key),T=u(je,T,xe),we===null?pe=je:we.sibling=je,we=je);return e&&Ee.forEach(function(Cg){return t(L,Cg)}),He&&gr(L,xe),pe}function Ye(L,T,F,K){if(typeof F=="object"&&F!==null&&F.type===Y&&F.key===null&&(F=F.props.children),typeof F=="object"&&F!==null){switch(F.$$typeof){case te:e:{for(var pe=F.key,we=T;we!==null;){if(we.key===pe){if(pe=F.type,pe===Y){if(we.tag===7){r(L,we.sibling),T=s(we,F.props.children),T.return=L,L=T;break e}}else if(we.elementType===pe||typeof pe=="object"&&pe!==null&&pe.$$typeof===se&&Yc(pe)===we.type){r(L,we.sibling),T=s(we,F.props),T.ref=el(L,we,F),T.return=L,L=T;break e}r(L,we);break}else t(L,we);we=we.sibling}F.type===Y?(T=br(F.props.children,L.mode,K,F.key),T.return=L,L=T):(K=Ri(F.type,F.key,F.props,null,L.mode,K),K.ref=el(L,T,F),K.return=L,L=K)}return p(L);case X:e:{for(we=F.key;T!==null;){if(T.key===we)if(T.tag===4&&T.stateNode.containerInfo===F.containerInfo&&T.stateNode.implementation===F.implementation){r(L,T.sibling),T=s(T,F.children||[]),T.return=L,L=T;break e}else{r(L,T);break}else t(L,T);T=T.sibling}T=Ws(F,L.mode,K),T.return=L,L=T}return p(L);case se:return we=F._init,Ye(L,T,we(F._payload),K)}if(Xt(F))return ce(L,T,F,K);if(Q(F))return fe(L,T,F,K);ii(L,F)}return typeof F=="string"&&F!==""||typeof F=="number"?(F=""+F,T!==null&&T.tag===6?(r(L,T.sibling),T=s(T,F),T.return=L,L=T):(r(L,T),T=Vs(F,L.mode,K),T.return=L,L=T),p(L)):r(L,T)}return Ye}var eo=Zc(!0),qc=Zc(!1),ai=$n(null),si=null,to=null,es=null;function ts(){es=to=si=null}function ns(e){var t=ai.current;$e(ai),e._currentValue=t}function rs(e,t,r){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===r)break;e=e.return}}function no(e,t){si=e,es=to=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(vt=!0),e.firstContext=null)}function Dt(e){var t=e._currentValue;if(es!==e)if(e={context:e,memoizedValue:t,next:null},to===null){if(si===null)throw Error(l(308));to=e,si.dependencies={lanes:0,firstContext:e}}else to=to.next=e;return t}var mr=null;function os(e){mr===null?mr=[e]:mr.push(e)}function Qc(e,t,r,i){var s=t.interleaved;return s===null?(r.next=r,os(t)):(r.next=s.next,s.next=r),t.interleaved=r,kn(e,i)}function kn(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Wn=!1;function ls(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Kc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Sn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Gn(e,t,r){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Le&2)!==0){var s=i.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),i.pending=t,kn(e,r)}return s=i.interleaved,s===null?(t.next=t,os(i)):(t.next=s.next,s.next=t),i.interleaved=t,kn(e,r)}function ui(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,Do(e,r)}}function Xc(e,t){var r=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,r===i)){var s=null,u=null;if(r=r.firstBaseUpdate,r!==null){do{var p={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};u===null?s=u=p:u=u.next=p,r=r.next}while(r!==null);u===null?s=u=t:u=u.next=t}else s=u=t;r={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:u,shared:i.shared,effects:i.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function ci(e,t,r,i){var s=e.updateQueue;Wn=!1;var u=s.firstBaseUpdate,p=s.lastBaseUpdate,x=s.shared.pending;if(x!==null){s.shared.pending=null;var _=x,P=_.next;_.next=null,p===null?u=P:p.next=P,p=_;var G=e.alternate;G!==null&&(G=G.updateQueue,x=G.lastBaseUpdate,x!==p&&(x===null?G.firstBaseUpdate=P:x.next=P,G.lastBaseUpdate=_))}if(u!==null){var Z=s.baseState;p=0,G=P=_=null,x=u;do{var W=x.lane,oe=x.eventTime;if((i&W)===W){G!==null&&(G=G.next={eventTime:oe,lane:0,tag:x.tag,payload:x.payload,callback:x.callback,next:null});e:{var ce=e,fe=x;switch(W=t,oe=r,fe.tag){case 1:if(ce=fe.payload,typeof ce=="function"){Z=ce.call(oe,Z,W);break e}Z=ce;break e;case 3:ce.flags=ce.flags&-65537|128;case 0:if(ce=fe.payload,W=typeof ce=="function"?ce.call(oe,Z,W):ce,W==null)break e;Z=V({},Z,W);break e;case 2:Wn=!0}}x.callback!==null&&x.lane!==0&&(e.flags|=64,W=s.effects,W===null?s.effects=[x]:W.push(x))}else oe={eventTime:oe,lane:W,tag:x.tag,payload:x.payload,callback:x.callback,next:null},G===null?(P=G=oe,_=Z):G=G.next=oe,p|=W;if(x=x.next,x===null){if(x=s.shared.pending,x===null)break;W=x,x=W.next,W.next=null,s.lastBaseUpdate=W,s.shared.pending=null}}while(!0);if(G===null&&(_=Z),s.baseState=_,s.firstBaseUpdate=P,s.lastBaseUpdate=G,t=s.shared.interleaved,t!==null){s=t;do p|=s.lane,s=s.next;while(s!==t)}else u===null&&(s.shared.lanes=0);Er|=p,e.lanes=p,e.memoizedState=Z}}function Jc(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],s=i.callback;if(s!==null){if(i.callback=null,i=r,typeof s!="function")throw Error(l(191,s));s.call(i)}}}var tl={},on=$n(tl),nl=$n(tl),rl=$n(tl);function vr(e){if(e===tl)throw Error(l(174));return e}function is(e,t){switch(Be(rl,t),Be(nl,e),Be(on,tl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Oo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Oo(t,e)}$e(on),Be(on,t)}function ro(){$e(on),$e(nl),$e(rl)}function ed(e){vr(rl.current);var t=vr(on.current),r=Oo(t,e.type);t!==r&&(Be(nl,e),Be(on,r))}function as(e){nl.current===e&&($e(on),$e(nl))}var Ve=$n(0);function di(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ss=[];function us(){for(var e=0;e<ss.length;e++)ss[e]._workInProgressVersionPrimary=null;ss.length=0}var fi=q.ReactCurrentDispatcher,cs=q.ReactCurrentBatchConfig,wr=0,We=null,Xe=null,et=null,pi=!1,ol=!1,ll=0,Zy=0;function ut(){throw Error(l(321))}function ds(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Gt(e[r],t[r]))return!1;return!0}function fs(e,t,r,i,s,u){if(wr=u,We=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fi.current=e===null||e.memoizedState===null?Xy:Jy,e=r(i,s),ol){u=0;do{if(ol=!1,ll=0,25<=u)throw Error(l(301));u+=1,et=Xe=null,t.updateQueue=null,fi.current=eg,e=r(i,s)}while(ol)}if(fi.current=gi,t=Xe!==null&&Xe.next!==null,wr=0,et=Xe=We=null,pi=!1,t)throw Error(l(300));return e}function ps(){var e=ll!==0;return ll=0,e}function ln(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return et===null?We.memoizedState=et=e:et=et.next=e,et}function Mt(){if(Xe===null){var e=We.alternate;e=e!==null?e.memoizedState:null}else e=Xe.next;var t=et===null?We.memoizedState:et.next;if(t!==null)et=t,Xe=e;else{if(e===null)throw Error(l(310));Xe=e,e={memoizedState:Xe.memoizedState,baseState:Xe.baseState,baseQueue:Xe.baseQueue,queue:Xe.queue,next:null},et===null?We.memoizedState=et=e:et=et.next=e}return et}function il(e,t){return typeof t=="function"?t(e):t}function hs(e){var t=Mt(),r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=e;var i=Xe,s=i.baseQueue,u=r.pending;if(u!==null){if(s!==null){var p=s.next;s.next=u.next,u.next=p}i.baseQueue=s=u,r.pending=null}if(s!==null){u=s.next,i=i.baseState;var x=p=null,_=null,P=u;do{var G=P.lane;if((wr&G)===G)_!==null&&(_=_.next={lane:0,action:P.action,hasEagerState:P.hasEagerState,eagerState:P.eagerState,next:null}),i=P.hasEagerState?P.eagerState:e(i,P.action);else{var Z={lane:G,action:P.action,hasEagerState:P.hasEagerState,eagerState:P.eagerState,next:null};_===null?(x=_=Z,p=i):_=_.next=Z,We.lanes|=G,Er|=G}P=P.next}while(P!==null&&P!==u);_===null?p=i:_.next=x,Gt(i,t.memoizedState)||(vt=!0),t.memoizedState=i,t.baseState=p,t.baseQueue=_,r.lastRenderedState=i}if(e=r.interleaved,e!==null){s=e;do u=s.lane,We.lanes|=u,Er|=u,s=s.next;while(s!==e)}else s===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function ys(e){var t=Mt(),r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=e;var i=r.dispatch,s=r.pending,u=t.memoizedState;if(s!==null){r.pending=null;var p=s=s.next;do u=e(u,p.action),p=p.next;while(p!==s);Gt(u,t.memoizedState)||(vt=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),r.lastRenderedState=u}return[u,i]}function td(){}function nd(e,t){var r=We,i=Mt(),s=t(),u=!Gt(i.memoizedState,s);if(u&&(i.memoizedState=s,vt=!0),i=i.queue,gs(ld.bind(null,r,i,e),[e]),i.getSnapshot!==t||u||et!==null&&et.memoizedState.tag&1){if(r.flags|=2048,al(9,od.bind(null,r,i,s,t),void 0,null),tt===null)throw Error(l(349));(wr&30)!==0||rd(r,t,s)}return s}function rd(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=We.updateQueue,t===null?(t={lastEffect:null,stores:null},We.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function od(e,t,r,i){t.value=r,t.getSnapshot=i,id(t)&&ad(e)}function ld(e,t,r){return r(function(){id(t)&&ad(e)})}function id(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Gt(e,r)}catch{return!0}}function ad(e){var t=kn(e,1);t!==null&&Kt(t,e,1,-1)}function sd(e){var t=ln();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:e},t.queue=e,e=e.dispatch=Ky.bind(null,We,e),[t.memoizedState,e]}function al(e,t,r,i){return e={tag:e,create:t,destroy:r,deps:i,next:null},t=We.updateQueue,t===null?(t={lastEffect:null,stores:null},We.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(i=r.next,r.next=e,e.next=i,t.lastEffect=e)),e}function ud(){return Mt().memoizedState}function hi(e,t,r,i){var s=ln();We.flags|=e,s.memoizedState=al(1|t,r,void 0,i===void 0?null:i)}function yi(e,t,r,i){var s=Mt();i=i===void 0?null:i;var u=void 0;if(Xe!==null){var p=Xe.memoizedState;if(u=p.destroy,i!==null&&ds(i,p.deps)){s.memoizedState=al(t,r,u,i);return}}We.flags|=e,s.memoizedState=al(1|t,r,u,i)}function cd(e,t){return hi(8390656,8,e,t)}function gs(e,t){return yi(2048,8,e,t)}function dd(e,t){return yi(4,2,e,t)}function fd(e,t){return yi(4,4,e,t)}function pd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hd(e,t,r){return r=r!=null?r.concat([e]):null,yi(4,4,pd.bind(null,t,e),r)}function ms(){}function yd(e,t){var r=Mt();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ds(t,i[1])?i[0]:(r.memoizedState=[e,t],e)}function gd(e,t){var r=Mt();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ds(t,i[1])?i[0]:(e=e(),r.memoizedState=[e,t],e)}function md(e,t,r){return(wr&21)===0?(e.baseState&&(e.baseState=!1,vt=!0),e.memoizedState=r):(Gt(r,t)||(r=Bl(),We.lanes|=r,Er|=r,e.baseState=!0),t)}function qy(e,t){var r=Fe;Fe=r!==0&&4>r?r:4,e(!0);var i=cs.transition;cs.transition={};try{e(!1),t()}finally{Fe=r,cs.transition=i}}function vd(){return Mt().memoizedState}function Qy(e,t,r){var i=Qn(e);if(r={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null},wd(e))Ed(t,r);else if(r=Qc(e,t,r,i),r!==null){var s=pt();Kt(r,e,i,s),xd(r,t,i)}}function Ky(e,t,r){var i=Qn(e),s={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null};if(wd(e))Ed(t,s);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var p=t.lastRenderedState,x=u(p,r);if(s.hasEagerState=!0,s.eagerState=x,Gt(x,p)){var _=t.interleaved;_===null?(s.next=s,os(t)):(s.next=_.next,_.next=s),t.interleaved=s;return}}catch{}finally{}r=Qc(e,t,s,i),r!==null&&(s=pt(),Kt(r,e,i,s),xd(r,t,i))}}function wd(e){var t=e.alternate;return e===We||t!==null&&t===We}function Ed(e,t){ol=pi=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function xd(e,t,r){if((r&4194240)!==0){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,Do(e,r)}}var gi={readContext:Dt,useCallback:ut,useContext:ut,useEffect:ut,useImperativeHandle:ut,useInsertionEffect:ut,useLayoutEffect:ut,useMemo:ut,useReducer:ut,useRef:ut,useState:ut,useDebugValue:ut,useDeferredValue:ut,useTransition:ut,useMutableSource:ut,useSyncExternalStore:ut,useId:ut,unstable_isNewReconciler:!1},Xy={readContext:Dt,useCallback:function(e,t){return ln().memoizedState=[e,t===void 0?null:t],e},useContext:Dt,useEffect:cd,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,hi(4194308,4,pd.bind(null,t,e),r)},useLayoutEffect:function(e,t){return hi(4194308,4,e,t)},useInsertionEffect:function(e,t){return hi(4,2,e,t)},useMemo:function(e,t){var r=ln();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var i=ln();return t=r!==void 0?r(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=Qy.bind(null,We,e),[i.memoizedState,e]},useRef:function(e){var t=ln();return e={current:e},t.memoizedState=e},useState:sd,useDebugValue:ms,useDeferredValue:function(e){return ln().memoizedState=e},useTransition:function(){var e=sd(!1),t=e[0];return e=qy.bind(null,e[1]),ln().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var i=We,s=ln();if(He){if(r===void 0)throw Error(l(407));r=r()}else{if(r=t(),tt===null)throw Error(l(349));(wr&30)!==0||rd(i,t,r)}s.memoizedState=r;var u={value:r,getSnapshot:t};return s.queue=u,cd(ld.bind(null,i,u,e),[e]),i.flags|=2048,al(9,od.bind(null,i,u,r,t),void 0,null),r},useId:function(){var e=ln(),t=tt.identifierPrefix;if(He){var r=xn,i=En;r=(i&~(1<<32-kt(i)-1)).toString(32)+r,t=":"+t+"R"+r,r=ll++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Zy++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Jy={readContext:Dt,useCallback:yd,useContext:Dt,useEffect:gs,useImperativeHandle:hd,useInsertionEffect:dd,useLayoutEffect:fd,useMemo:gd,useReducer:hs,useRef:ud,useState:function(){return hs(il)},useDebugValue:ms,useDeferredValue:function(e){var t=Mt();return md(t,Xe.memoizedState,e)},useTransition:function(){var e=hs(il)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:td,useSyncExternalStore:nd,useId:vd,unstable_isNewReconciler:!1},eg={readContext:Dt,useCallback:yd,useContext:Dt,useEffect:gs,useImperativeHandle:hd,useInsertionEffect:dd,useLayoutEffect:fd,useMemo:gd,useReducer:ys,useRef:ud,useState:function(){return ys(il)},useDebugValue:ms,useDeferredValue:function(e){var t=Mt();return Xe===null?t.memoizedState=e:md(t,Xe.memoizedState,e)},useTransition:function(){var e=ys(il)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:td,useSyncExternalStore:nd,useId:vd,unstable_isNewReconciler:!1};function Zt(e,t){if(e&&e.defaultProps){t=V({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function vs(e,t,r,i){t=e.memoizedState,r=r(i,t),r=r==null?t:V({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var mi={isMounted:function(e){return(e=e._reactInternals)?yt(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var i=pt(),s=Qn(e),u=Sn(i,s);u.payload=t,r!=null&&(u.callback=r),t=Gn(e,u,s),t!==null&&(Kt(t,e,s,i),ui(t,e,s))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var i=pt(),s=Qn(e),u=Sn(i,s);u.tag=1,u.payload=t,r!=null&&(u.callback=r),t=Gn(e,u,s),t!==null&&(Kt(t,e,s,i),ui(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=pt(),i=Qn(e),s=Sn(r,i);s.tag=2,t!=null&&(s.callback=t),t=Gn(e,s,i),t!==null&&(Kt(t,e,i,r),ui(t,e,i))}};function kd(e,t,r,i,s,u,p){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,u,p):t.prototype&&t.prototype.isPureReactComponent?!Yo(r,i)||!Yo(s,u):!0}function Sd(e,t,r){var i=!1,s=Hn,u=t.contextType;return typeof u=="object"&&u!==null?u=Dt(u):(s=mt(t)?hr:st.current,i=t.contextTypes,u=(i=i!=null)?Qr(e,s):Hn),t=new t(r,u),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=mi,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=u),t}function bd(e,t,r,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,i),t.state!==e&&mi.enqueueReplaceState(t,t.state,null)}function ws(e,t,r,i){var s=e.stateNode;s.props=r,s.state=e.memoizedState,s.refs={},ls(e);var u=t.contextType;typeof u=="object"&&u!==null?s.context=Dt(u):(u=mt(t)?hr:st.current,s.context=Qr(e,u)),s.state=e.memoizedState,u=t.getDerivedStateFromProps,typeof u=="function"&&(vs(e,t,u,r),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&mi.enqueueReplaceState(s,s.state,null),ci(e,r,s,i),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function oo(e,t){try{var r="",i=t;do r+=be(i),i=i.return;while(i);var s=r}catch(u){s=`
Error generating stack: `+u.message+`
`+u.stack}return{value:e,source:t,stack:s,digest:null}}function Es(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function xs(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var tg=typeof WeakMap=="function"?WeakMap:Map;function _d(e,t,r){r=Sn(-1,r),r.tag=3,r.payload={element:null};var i=t.value;return r.callback=function(){bi||(bi=!0,Ps=i),xs(e,t)},r}function Cd(e,t,r){r=Sn(-1,r),r.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var s=t.value;r.payload=function(){return i(s)},r.callback=function(){xs(e,t)}}var u=e.stateNode;return u!==null&&typeof u.componentDidCatch=="function"&&(r.callback=function(){xs(e,t),typeof i!="function"&&(Zn===null?Zn=new Set([this]):Zn.add(this));var p=t.stack;this.componentDidCatch(t.value,{componentStack:p!==null?p:""})}),r}function Od(e,t,r){var i=e.pingCache;if(i===null){i=e.pingCache=new tg;var s=new Set;i.set(t,s)}else s=i.get(t),s===void 0&&(s=new Set,i.set(t,s));s.has(r)||(s.add(r),e=yg.bind(null,e,t,r),t.then(e,e))}function Td(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ad(e,t,r,i,s){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=Sn(-1,1),t.tag=2,Gn(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=s,e)}var ng=q.ReactCurrentOwner,vt=!1;function ft(e,t,r,i){t.child=e===null?qc(t,null,r,i):eo(t,e.child,r,i)}function Rd(e,t,r,i,s){r=r.render;var u=t.ref;return no(t,s),i=fs(e,t,r,i,u,s),r=ps(),e!==null&&!vt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,bn(e,t,s)):(He&&r&&qa(t),t.flags|=1,ft(e,t,i,s),t.child)}function Nd(e,t,r,i,s){if(e===null){var u=r.type;return typeof u=="function"&&!Hs(u)&&u.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=u,Ld(e,t,u,i,s)):(e=Ri(r.type,null,i,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,(e.lanes&s)===0){var p=u.memoizedProps;if(r=r.compare,r=r!==null?r:Yo,r(p,i)&&e.ref===t.ref)return bn(e,t,s)}return t.flags|=1,e=Xn(u,i),e.ref=t.ref,e.return=t,t.child=e}function Ld(e,t,r,i,s){if(e!==null){var u=e.memoizedProps;if(Yo(u,i)&&e.ref===t.ref)if(vt=!1,t.pendingProps=i=u,(e.lanes&s)!==0)(e.flags&131072)!==0&&(vt=!0);else return t.lanes=e.lanes,bn(e,t,s)}return ks(e,t,r,i,s)}function Fd(e,t,r){var i=t.pendingProps,s=i.children,u=e!==null?e.memoizedState:null;if(i.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Be(io,_t),_t|=r;else{if((r&1073741824)===0)return e=u!==null?u.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Be(io,_t),_t|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=u!==null?u.baseLanes:r,Be(io,_t),_t|=i}else u!==null?(i=u.baseLanes|r,t.memoizedState=null):i=r,Be(io,_t),_t|=i;return ft(e,t,s,r),t.child}function jd(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function ks(e,t,r,i,s){var u=mt(r)?hr:st.current;return u=Qr(t,u),no(t,s),r=fs(e,t,r,i,u,s),i=ps(),e!==null&&!vt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,bn(e,t,s)):(He&&i&&qa(t),t.flags|=1,ft(e,t,r,s),t.child)}function Id(e,t,r,i,s){if(mt(r)){var u=!0;ti(t)}else u=!1;if(no(t,s),t.stateNode===null)wi(e,t),Sd(t,r,i),ws(t,r,i,s),i=!0;else if(e===null){var p=t.stateNode,x=t.memoizedProps;p.props=x;var _=p.context,P=r.contextType;typeof P=="object"&&P!==null?P=Dt(P):(P=mt(r)?hr:st.current,P=Qr(t,P));var G=r.getDerivedStateFromProps,Z=typeof G=="function"||typeof p.getSnapshotBeforeUpdate=="function";Z||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(x!==i||_!==P)&&bd(t,p,i,P),Wn=!1;var W=t.memoizedState;p.state=W,ci(t,i,p,s),_=t.memoizedState,x!==i||W!==_||gt.current||Wn?(typeof G=="function"&&(vs(t,r,G,i),_=t.memoizedState),(x=Wn||kd(t,r,x,i,W,_,P))?(Z||typeof p.UNSAFE_componentWillMount!="function"&&typeof p.componentWillMount!="function"||(typeof p.componentWillMount=="function"&&p.componentWillMount(),typeof p.UNSAFE_componentWillMount=="function"&&p.UNSAFE_componentWillMount()),typeof p.componentDidMount=="function"&&(t.flags|=4194308)):(typeof p.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=_),p.props=i,p.state=_,p.context=P,i=x):(typeof p.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{p=t.stateNode,Kc(e,t),x=t.memoizedProps,P=t.type===t.elementType?x:Zt(t.type,x),p.props=P,Z=t.pendingProps,W=p.context,_=r.contextType,typeof _=="object"&&_!==null?_=Dt(_):(_=mt(r)?hr:st.current,_=Qr(t,_));var oe=r.getDerivedStateFromProps;(G=typeof oe=="function"||typeof p.getSnapshotBeforeUpdate=="function")||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(x!==Z||W!==_)&&bd(t,p,i,_),Wn=!1,W=t.memoizedState,p.state=W,ci(t,i,p,s);var ce=t.memoizedState;x!==Z||W!==ce||gt.current||Wn?(typeof oe=="function"&&(vs(t,r,oe,i),ce=t.memoizedState),(P=Wn||kd(t,r,P,i,W,ce,_)||!1)?(G||typeof p.UNSAFE_componentWillUpdate!="function"&&typeof p.componentWillUpdate!="function"||(typeof p.componentWillUpdate=="function"&&p.componentWillUpdate(i,ce,_),typeof p.UNSAFE_componentWillUpdate=="function"&&p.UNSAFE_componentWillUpdate(i,ce,_)),typeof p.componentDidUpdate=="function"&&(t.flags|=4),typeof p.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof p.componentDidUpdate!="function"||x===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||x===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=ce),p.props=i,p.state=ce,p.context=_,i=P):(typeof p.componentDidUpdate!="function"||x===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||x===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),i=!1)}return Ss(e,t,r,i,u,s)}function Ss(e,t,r,i,s,u){jd(e,t);var p=(t.flags&128)!==0;if(!i&&!p)return s&&zc(t,r,!1),bn(e,t,u);i=t.stateNode,ng.current=t;var x=p&&typeof r.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&p?(t.child=eo(t,e.child,null,u),t.child=eo(t,null,x,u)):ft(e,t,x,u),t.memoizedState=i.state,s&&zc(t,r,!0),t.child}function Pd(e){var t=e.stateNode;t.pendingContext?Mc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Mc(e,t.context,!1),is(e,t.containerInfo)}function Dd(e,t,r,i,s){return Jr(),Ja(s),t.flags|=256,ft(e,t,r,i),t.child}var bs={dehydrated:null,treeContext:null,retryLane:0};function _s(e){return{baseLanes:e,cachePool:null,transitions:null}}function Md(e,t,r){var i=t.pendingProps,s=Ve.current,u=!1,p=(t.flags&128)!==0,x;if((x=p)||(x=e!==null&&e.memoizedState===null?!1:(s&2)!==0),x?(u=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),Be(Ve,s&1),e===null)return Xa(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(p=i.children,e=i.fallback,u?(i=t.mode,u=t.child,p={mode:"hidden",children:p},(i&1)===0&&u!==null?(u.childLanes=0,u.pendingProps=p):u=Ni(p,i,0,null),e=br(e,i,r,null),u.return=t,e.return=t,u.sibling=e,t.child=u,t.child.memoizedState=_s(r),t.memoizedState=bs,e):Cs(t,p));if(s=e.memoizedState,s!==null&&(x=s.dehydrated,x!==null))return rg(e,t,p,i,x,s,r);if(u){u=i.fallback,p=t.mode,s=e.child,x=s.sibling;var _={mode:"hidden",children:i.children};return(p&1)===0&&t.child!==s?(i=t.child,i.childLanes=0,i.pendingProps=_,t.deletions=null):(i=Xn(s,_),i.subtreeFlags=s.subtreeFlags&14680064),x!==null?u=Xn(x,u):(u=br(u,p,r,null),u.flags|=2),u.return=t,i.return=t,i.sibling=u,t.child=i,i=u,u=t.child,p=e.child.memoizedState,p=p===null?_s(r):{baseLanes:p.baseLanes|r,cachePool:null,transitions:p.transitions},u.memoizedState=p,u.childLanes=e.childLanes&~r,t.memoizedState=bs,i}return u=e.child,e=u.sibling,i=Xn(u,{mode:"visible",children:i.children}),(t.mode&1)===0&&(i.lanes=r),i.return=t,i.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=i,t.memoizedState=null,i}function Cs(e,t){return t=Ni({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function vi(e,t,r,i){return i!==null&&Ja(i),eo(t,e.child,null,r),e=Cs(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function rg(e,t,r,i,s,u,p){if(r)return t.flags&256?(t.flags&=-257,i=Es(Error(l(422))),vi(e,t,p,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(u=i.fallback,s=t.mode,i=Ni({mode:"visible",children:i.children},s,0,null),u=br(u,s,p,null),u.flags|=2,i.return=t,u.return=t,i.sibling=u,t.child=i,(t.mode&1)!==0&&eo(t,e.child,null,p),t.child.memoizedState=_s(p),t.memoizedState=bs,u);if((t.mode&1)===0)return vi(e,t,p,null);if(s.data==="$!"){if(i=s.nextSibling&&s.nextSibling.dataset,i)var x=i.dgst;return i=x,u=Error(l(419)),i=Es(u,i,void 0),vi(e,t,p,i)}if(x=(p&e.childLanes)!==0,vt||x){if(i=tt,i!==null){switch(p&-p){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=(s&(i.suspendedLanes|p))!==0?0:s,s!==0&&s!==u.retryLane&&(u.retryLane=s,kn(e,s),Kt(i,e,s,-1))}return $s(),i=Es(Error(l(421))),vi(e,t,p,i)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=gg.bind(null,e),s._reactRetry=t,null):(e=u.treeContext,bt=Un(s.nextSibling),St=t,He=!0,Yt=null,e!==null&&(It[Pt++]=En,It[Pt++]=xn,It[Pt++]=yr,En=e.id,xn=e.overflow,yr=t),t=Cs(t,i.children),t.flags|=4096,t)}function Bd(e,t,r){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),rs(e.return,t,r)}function Os(e,t,r,i,s){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:r,tailMode:s}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=i,u.tail=r,u.tailMode=s)}function zd(e,t,r){var i=t.pendingProps,s=i.revealOrder,u=i.tail;if(ft(e,t,i.children,r),i=Ve.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Bd(e,r,t);else if(e.tag===19)Bd(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(Be(Ve,i),(t.mode&1)===0)t.memoizedState=null;else switch(s){case"forwards":for(r=t.child,s=null;r!==null;)e=r.alternate,e!==null&&di(e)===null&&(s=r),r=r.sibling;r=s,r===null?(s=t.child,t.child=null):(s=r.sibling,r.sibling=null),Os(t,!1,s,r,u);break;case"backwards":for(r=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&di(e)===null){t.child=s;break}e=s.sibling,s.sibling=r,r=s,s=e}Os(t,!0,r,null,u);break;case"together":Os(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function wi(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function bn(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),Er|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(l(153));if(t.child!==null){for(e=t.child,r=Xn(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Xn(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function og(e,t,r){switch(t.tag){case 3:Pd(t),Jr();break;case 5:ed(t);break;case 1:mt(t.type)&&ti(t);break;case 4:is(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,s=t.memoizedProps.value;Be(ai,i._currentValue),i._currentValue=s;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(Be(Ve,Ve.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?Md(e,t,r):(Be(Ve,Ve.current&1),e=bn(e,t,r),e!==null?e.sibling:null);Be(Ve,Ve.current&1);break;case 19:if(i=(r&t.childLanes)!==0,(e.flags&128)!==0){if(i)return zd(e,t,r);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),Be(Ve,Ve.current),i)break;return null;case 22:case 23:return t.lanes=0,Fd(e,t,r)}return bn(e,t,r)}var Ud,Ts,$d,Hd;Ud=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Ts=function(){},$d=function(e,t,r,i){var s=e.memoizedProps;if(s!==i){e=t.stateNode,vr(on.current);var u=null;switch(r){case"input":s=ye(e,s),i=ye(e,i),u=[];break;case"select":s=V({},s,{value:void 0}),i=V({},i,{value:void 0}),u=[];break;case"textarea":s=Rn(e,s),i=Rn(e,i),u=[];break;default:typeof s.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=Xl)}Fn(r,i);var p;r=null;for(P in s)if(!i.hasOwnProperty(P)&&s.hasOwnProperty(P)&&s[P]!=null)if(P==="style"){var x=s[P];for(p in x)x.hasOwnProperty(p)&&(r||(r={}),r[p]="")}else P!=="dangerouslySetInnerHTML"&&P!=="children"&&P!=="suppressContentEditableWarning"&&P!=="suppressHydrationWarning"&&P!=="autoFocus"&&(c.hasOwnProperty(P)?u||(u=[]):(u=u||[]).push(P,null));for(P in i){var _=i[P];if(x=s!=null?s[P]:void 0,i.hasOwnProperty(P)&&_!==x&&(_!=null||x!=null))if(P==="style")if(x){for(p in x)!x.hasOwnProperty(p)||_&&_.hasOwnProperty(p)||(r||(r={}),r[p]="");for(p in _)_.hasOwnProperty(p)&&x[p]!==_[p]&&(r||(r={}),r[p]=_[p])}else r||(u||(u=[]),u.push(P,r)),r=_;else P==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,x=x?x.__html:void 0,_!=null&&x!==_&&(u=u||[]).push(P,_)):P==="children"?typeof _!="string"&&typeof _!="number"||(u=u||[]).push(P,""+_):P!=="suppressContentEditableWarning"&&P!=="suppressHydrationWarning"&&(c.hasOwnProperty(P)?(_!=null&&P==="onScroll"&&Ue("scroll",e),u||x===_||(u=[])):(u=u||[]).push(P,_))}r&&(u=u||[]).push("style",r);var P=u;(t.updateQueue=P)&&(t.flags|=4)}},Hd=function(e,t,r,i){r!==i&&(t.flags|=4)};function sl(e,t){if(!He)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function ct(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,i=0;if(t)for(var s=e.child;s!==null;)r|=s.lanes|s.childLanes,i|=s.subtreeFlags&14680064,i|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)r|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=i,e.childLanes=r,t}function lg(e,t,r){var i=t.pendingProps;switch(Qa(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ct(t),null;case 1:return mt(t.type)&&ei(),ct(t),null;case 3:return i=t.stateNode,ro(),$e(gt),$e(st),us(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(li(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Yt!==null&&(Bs(Yt),Yt=null))),Ts(e,t),ct(t),null;case 5:as(t);var s=vr(rl.current);if(r=t.type,e!==null&&t.stateNode!=null)$d(e,t,r,i,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(l(166));return ct(t),null}if(e=vr(on.current),li(t)){i=t.stateNode,r=t.type;var u=t.memoizedProps;switch(i[rn]=t,i[Xo]=u,e=(t.mode&1)!==0,r){case"dialog":Ue("cancel",i),Ue("close",i);break;case"iframe":case"object":case"embed":Ue("load",i);break;case"video":case"audio":for(s=0;s<qo.length;s++)Ue(qo[s],i);break;case"source":Ue("error",i);break;case"img":case"image":case"link":Ue("error",i),Ue("load",i);break;case"details":Ue("toggle",i);break;case"input":pn(i,u),Ue("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!u.multiple},Ue("invalid",i);break;case"textarea":Ht(i,u),Ue("invalid",i)}Fn(r,u),s=null;for(var p in u)if(u.hasOwnProperty(p)){var x=u[p];p==="children"?typeof x=="string"?i.textContent!==x&&(u.suppressHydrationWarning!==!0&&Kl(i.textContent,x,e),s=["children",x]):typeof x=="number"&&i.textContent!==""+x&&(u.suppressHydrationWarning!==!0&&Kl(i.textContent,x,e),s=["children",""+x]):c.hasOwnProperty(p)&&x!=null&&p==="onScroll"&&Ue("scroll",i)}switch(r){case"input":fn(i),bl(i,u,!0);break;case"textarea":fn(i),Co(i);break;case"select":case"option":break;default:typeof u.onClick=="function"&&(i.onclick=Xl)}i=s,t.updateQueue=i,i!==null&&(t.flags|=4)}else{p=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=_l(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=p.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=p.createElement(r,{is:i.is}):(e=p.createElement(r),r==="select"&&(p=e,i.multiple?p.multiple=!0:i.size&&(p.size=i.size))):e=p.createElementNS(e,r),e[rn]=t,e[Xo]=i,Ud(e,t,!1,!1),t.stateNode=e;e:{switch(p=Ao(r,i),r){case"dialog":Ue("cancel",e),Ue("close",e),s=i;break;case"iframe":case"object":case"embed":Ue("load",e),s=i;break;case"video":case"audio":for(s=0;s<qo.length;s++)Ue(qo[s],e);s=i;break;case"source":Ue("error",e),s=i;break;case"img":case"image":case"link":Ue("error",e),Ue("load",e),s=i;break;case"details":Ue("toggle",e),s=i;break;case"input":pn(e,i),s=ye(e,i),Ue("invalid",e);break;case"option":s=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},s=V({},i,{value:void 0}),Ue("invalid",e);break;case"textarea":Ht(e,i),s=Rn(e,i),Ue("invalid",e);break;default:s=i}Fn(r,s),x=s;for(u in x)if(x.hasOwnProperty(u)){var _=x[u];u==="style"?Tl(e,_):u==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,_!=null&&To(e,_)):u==="children"?typeof _=="string"?(r!=="textarea"||_!=="")&&Nn(e,_):typeof _=="number"&&Nn(e,""+_):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(c.hasOwnProperty(u)?_!=null&&u==="onScroll"&&Ue("scroll",e):_!=null&&B(e,u,_,p))}switch(r){case"input":fn(e),bl(e,i,!1);break;case"textarea":fn(e),Co(e);break;case"option":i.value!=null&&e.setAttribute("value",""+Se(i.value));break;case"select":e.multiple=!!i.multiple,u=i.value,u!=null?Jt(e,!!i.multiple,u,!1):i.defaultValue!=null&&Jt(e,!!i.multiple,i.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Xl)}switch(r){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ct(t),null;case 6:if(e&&t.stateNode!=null)Hd(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(l(166));if(r=vr(rl.current),vr(on.current),li(t)){if(i=t.stateNode,r=t.memoizedProps,i[rn]=t,(u=i.nodeValue!==r)&&(e=St,e!==null))switch(e.tag){case 3:Kl(i.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Kl(i.nodeValue,r,(e.mode&1)!==0)}u&&(t.flags|=4)}else i=(r.nodeType===9?r:r.ownerDocument).createTextNode(i),i[rn]=t,t.stateNode=i}return ct(t),null;case 13:if($e(Ve),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(He&&bt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Gc(),Jr(),t.flags|=98560,u=!1;else if(u=li(t),i!==null&&i.dehydrated!==null){if(e===null){if(!u)throw Error(l(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(l(317));u[rn]=t}else Jr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ct(t),u=!1}else Yt!==null&&(Bs(Yt),Yt=null),u=!0;if(!u)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Ve.current&1)!==0?Je===0&&(Je=3):$s())),t.updateQueue!==null&&(t.flags|=4),ct(t),null);case 4:return ro(),Ts(e,t),e===null&&Qo(t.stateNode.containerInfo),ct(t),null;case 10:return ns(t.type._context),ct(t),null;case 17:return mt(t.type)&&ei(),ct(t),null;case 19:if($e(Ve),u=t.memoizedState,u===null)return ct(t),null;if(i=(t.flags&128)!==0,p=u.rendering,p===null)if(i)sl(u,!1);else{if(Je!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(p=di(e),p!==null){for(t.flags|=128,sl(u,!1),i=p.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=r,r=t.child;r!==null;)u=r,e=i,u.flags&=14680066,p=u.alternate,p===null?(u.childLanes=0,u.lanes=e,u.child=null,u.subtreeFlags=0,u.memoizedProps=null,u.memoizedState=null,u.updateQueue=null,u.dependencies=null,u.stateNode=null):(u.childLanes=p.childLanes,u.lanes=p.lanes,u.child=p.child,u.subtreeFlags=0,u.deletions=null,u.memoizedProps=p.memoizedProps,u.memoizedState=p.memoizedState,u.updateQueue=p.updateQueue,u.type=p.type,e=p.dependencies,u.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return Be(Ve,Ve.current&1|2),t.child}e=e.sibling}u.tail!==null&&ze()>ao&&(t.flags|=128,i=!0,sl(u,!1),t.lanes=4194304)}else{if(!i)if(e=di(p),e!==null){if(t.flags|=128,i=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),sl(u,!0),u.tail===null&&u.tailMode==="hidden"&&!p.alternate&&!He)return ct(t),null}else 2*ze()-u.renderingStartTime>ao&&r!==1073741824&&(t.flags|=128,i=!0,sl(u,!1),t.lanes=4194304);u.isBackwards?(p.sibling=t.child,t.child=p):(r=u.last,r!==null?r.sibling=p:t.child=p,u.last=p)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=ze(),t.sibling=null,r=Ve.current,Be(Ve,i?r&1|2:r&1),t):(ct(t),null);case 22:case 23:return Us(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&(t.mode&1)!==0?(_t&1073741824)!==0&&(ct(t),t.subtreeFlags&6&&(t.flags|=8192)):ct(t),null;case 24:return null;case 25:return null}throw Error(l(156,t.tag))}function ig(e,t){switch(Qa(t),t.tag){case 1:return mt(t.type)&&ei(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ro(),$e(gt),$e(st),us(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return as(t),null;case 13:if($e(Ve),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(l(340));Jr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $e(Ve),null;case 4:return ro(),null;case 10:return ns(t.type._context),null;case 22:case 23:return Us(),null;case 24:return null;default:return null}}var Ei=!1,dt=!1,ag=typeof WeakSet=="function"?WeakSet:Set,ae=null;function lo(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(i){Ge(e,t,i)}else r.current=null}function As(e,t,r){try{r()}catch(i){Ge(e,t,i)}}var Vd=!1;function sg(e,t){if(Ua=E,e=kc(),Fa(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var i=r.getSelection&&r.getSelection();if(i&&i.rangeCount!==0){r=i.anchorNode;var s=i.anchorOffset,u=i.focusNode;i=i.focusOffset;try{r.nodeType,u.nodeType}catch{r=null;break e}var p=0,x=-1,_=-1,P=0,G=0,Z=e,W=null;t:for(;;){for(var oe;Z!==r||s!==0&&Z.nodeType!==3||(x=p+s),Z!==u||i!==0&&Z.nodeType!==3||(_=p+i),Z.nodeType===3&&(p+=Z.nodeValue.length),(oe=Z.firstChild)!==null;)W=Z,Z=oe;for(;;){if(Z===e)break t;if(W===r&&++P===s&&(x=p),W===u&&++G===i&&(_=p),(oe=Z.nextSibling)!==null)break;Z=W,W=Z.parentNode}Z=oe}r=x===-1||_===-1?null:{start:x,end:_}}else r=null}r=r||{start:0,end:0}}else r=null;for($a={focusedElem:e,selectionRange:r},E=!1,ae=t;ae!==null;)if(t=ae,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,ae=e;else for(;ae!==null;){t=ae;try{var ce=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(ce!==null){var fe=ce.memoizedProps,Ye=ce.memoizedState,L=t.stateNode,T=L.getSnapshotBeforeUpdate(t.elementType===t.type?fe:Zt(t.type,fe),Ye);L.__reactInternalSnapshotBeforeUpdate=T}break;case 3:var F=t.stateNode.containerInfo;F.nodeType===1?F.textContent="":F.nodeType===9&&F.documentElement&&F.removeChild(F.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(l(163))}}catch(K){Ge(t,t.return,K)}if(e=t.sibling,e!==null){e.return=t.return,ae=e;break}ae=t.return}return ce=Vd,Vd=!1,ce}function ul(e,t,r){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var s=i=i.next;do{if((s.tag&e)===e){var u=s.destroy;s.destroy=void 0,u!==void 0&&As(t,r,u)}s=s.next}while(s!==i)}}function xi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var i=r.create;r.destroy=i()}r=r.next}while(r!==t)}}function Rs(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Wd(e){var t=e.alternate;t!==null&&(e.alternate=null,Wd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[rn],delete t[Xo],delete t[Ga],delete t[Vy],delete t[Wy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Gd(e){return e.tag===5||e.tag===3||e.tag===4}function Yd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Gd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ns(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Xl));else if(i!==4&&(e=e.child,e!==null))for(Ns(e,t,r),e=e.sibling;e!==null;)Ns(e,t,r),e=e.sibling}function Ls(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(Ls(e,t,r),e=e.sibling;e!==null;)Ls(e,t,r),e=e.sibling}var rt=null,qt=!1;function Yn(e,t,r){for(r=r.child;r!==null;)Zd(e,t,r),r=r.sibling}function Zd(e,t,r){if(Rt&&typeof Rt.onCommitFiberUnmount=="function")try{Rt.onCommitFiberUnmount(sr,r)}catch{}switch(r.tag){case 5:dt||lo(r,t);case 6:var i=rt,s=qt;rt=null,Yn(e,t,r),rt=i,qt=s,rt!==null&&(qt?(e=rt,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):rt.removeChild(r.stateNode));break;case 18:rt!==null&&(qt?(e=rt,r=r.stateNode,e.nodeType===8?Wa(e.parentNode,r):e.nodeType===1&&Wa(e,r),Dn(e)):Wa(rt,r.stateNode));break;case 4:i=rt,s=qt,rt=r.stateNode.containerInfo,qt=!0,Yn(e,t,r),rt=i,qt=s;break;case 0:case 11:case 14:case 15:if(!dt&&(i=r.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){s=i=i.next;do{var u=s,p=u.destroy;u=u.tag,p!==void 0&&((u&2)!==0||(u&4)!==0)&&As(r,t,p),s=s.next}while(s!==i)}Yn(e,t,r);break;case 1:if(!dt&&(lo(r,t),i=r.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=r.memoizedProps,i.state=r.memoizedState,i.componentWillUnmount()}catch(x){Ge(r,t,x)}Yn(e,t,r);break;case 21:Yn(e,t,r);break;case 22:r.mode&1?(dt=(i=dt)||r.memoizedState!==null,Yn(e,t,r),dt=i):Yn(e,t,r);break;default:Yn(e,t,r)}}function qd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new ag),t.forEach(function(i){var s=mg.bind(null,e,i);r.has(i)||(r.add(i),i.then(s,s))})}}function Qt(e,t){var r=t.deletions;if(r!==null)for(var i=0;i<r.length;i++){var s=r[i];try{var u=e,p=t,x=p;e:for(;x!==null;){switch(x.tag){case 5:rt=x.stateNode,qt=!1;break e;case 3:rt=x.stateNode.containerInfo,qt=!0;break e;case 4:rt=x.stateNode.containerInfo,qt=!0;break e}x=x.return}if(rt===null)throw Error(l(160));Zd(u,p,s),rt=null,qt=!1;var _=s.alternate;_!==null&&(_.return=null),s.return=null}catch(P){Ge(s,t,P)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Qd(t,e),t=t.sibling}function Qd(e,t){var r=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Qt(t,e),an(e),i&4){try{ul(3,e,e.return),xi(3,e)}catch(fe){Ge(e,e.return,fe)}try{ul(5,e,e.return)}catch(fe){Ge(e,e.return,fe)}}break;case 1:Qt(t,e),an(e),i&512&&r!==null&&lo(r,r.return);break;case 5:if(Qt(t,e),an(e),i&512&&r!==null&&lo(r,r.return),e.flags&32){var s=e.stateNode;try{Nn(s,"")}catch(fe){Ge(e,e.return,fe)}}if(i&4&&(s=e.stateNode,s!=null)){var u=e.memoizedProps,p=r!==null?r.memoizedProps:u,x=e.type,_=e.updateQueue;if(e.updateQueue=null,_!==null)try{x==="input"&&u.type==="radio"&&u.name!=null&&or(s,u),Ao(x,p);var P=Ao(x,u);for(p=0;p<_.length;p+=2){var G=_[p],Z=_[p+1];G==="style"?Tl(s,Z):G==="dangerouslySetInnerHTML"?To(s,Z):G==="children"?Nn(s,Z):B(s,G,Z,P)}switch(x){case"input":hn(s,u);break;case"textarea":_o(s,u);break;case"select":var W=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!u.multiple;var oe=u.value;oe!=null?Jt(s,!!u.multiple,oe,!1):W!==!!u.multiple&&(u.defaultValue!=null?Jt(s,!!u.multiple,u.defaultValue,!0):Jt(s,!!u.multiple,u.multiple?[]:"",!1))}s[Xo]=u}catch(fe){Ge(e,e.return,fe)}}break;case 6:if(Qt(t,e),an(e),i&4){if(e.stateNode===null)throw Error(l(162));s=e.stateNode,u=e.memoizedProps;try{s.nodeValue=u}catch(fe){Ge(e,e.return,fe)}}break;case 3:if(Qt(t,e),an(e),i&4&&r!==null&&r.memoizedState.isDehydrated)try{Dn(t.containerInfo)}catch(fe){Ge(e,e.return,fe)}break;case 4:Qt(t,e),an(e);break;case 13:Qt(t,e),an(e),s=e.child,s.flags&8192&&(u=s.memoizedState!==null,s.stateNode.isHidden=u,!u||s.alternate!==null&&s.alternate.memoizedState!==null||(Is=ze())),i&4&&qd(e);break;case 22:if(G=r!==null&&r.memoizedState!==null,e.mode&1?(dt=(P=dt)||G,Qt(t,e),dt=P):Qt(t,e),an(e),i&8192){if(P=e.memoizedState!==null,(e.stateNode.isHidden=P)&&!G&&(e.mode&1)!==0)for(ae=e,G=e.child;G!==null;){for(Z=ae=G;ae!==null;){switch(W=ae,oe=W.child,W.tag){case 0:case 11:case 14:case 15:ul(4,W,W.return);break;case 1:lo(W,W.return);var ce=W.stateNode;if(typeof ce.componentWillUnmount=="function"){i=W,r=W.return;try{t=i,ce.props=t.memoizedProps,ce.state=t.memoizedState,ce.componentWillUnmount()}catch(fe){Ge(i,r,fe)}}break;case 5:lo(W,W.return);break;case 22:if(W.memoizedState!==null){Jd(Z);continue}}oe!==null?(oe.return=W,ae=oe):Jd(Z)}G=G.sibling}e:for(G=null,Z=e;;){if(Z.tag===5){if(G===null){G=Z;try{s=Z.stateNode,P?(u=s.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none"):(x=Z.stateNode,_=Z.memoizedProps.style,p=_!=null&&_.hasOwnProperty("display")?_.display:null,x.style.display=Ol("display",p))}catch(fe){Ge(e,e.return,fe)}}}else if(Z.tag===6){if(G===null)try{Z.stateNode.nodeValue=P?"":Z.memoizedProps}catch(fe){Ge(e,e.return,fe)}}else if((Z.tag!==22&&Z.tag!==23||Z.memoizedState===null||Z===e)&&Z.child!==null){Z.child.return=Z,Z=Z.child;continue}if(Z===e)break e;for(;Z.sibling===null;){if(Z.return===null||Z.return===e)break e;G===Z&&(G=null),Z=Z.return}G===Z&&(G=null),Z.sibling.return=Z.return,Z=Z.sibling}}break;case 19:Qt(t,e),an(e),i&4&&qd(e);break;case 21:break;default:Qt(t,e),an(e)}}function an(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Gd(r)){var i=r;break e}r=r.return}throw Error(l(160))}switch(i.tag){case 5:var s=i.stateNode;i.flags&32&&(Nn(s,""),i.flags&=-33);var u=Yd(e);Ls(e,u,s);break;case 3:case 4:var p=i.stateNode.containerInfo,x=Yd(e);Ns(e,x,p);break;default:throw Error(l(161))}}catch(_){Ge(e,e.return,_)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ug(e,t,r){ae=e,Kd(e)}function Kd(e,t,r){for(var i=(e.mode&1)!==0;ae!==null;){var s=ae,u=s.child;if(s.tag===22&&i){var p=s.memoizedState!==null||Ei;if(!p){var x=s.alternate,_=x!==null&&x.memoizedState!==null||dt;x=Ei;var P=dt;if(Ei=p,(dt=_)&&!P)for(ae=s;ae!==null;)p=ae,_=p.child,p.tag===22&&p.memoizedState!==null?ef(s):_!==null?(_.return=p,ae=_):ef(s);for(;u!==null;)ae=u,Kd(u),u=u.sibling;ae=s,Ei=x,dt=P}Xd(e)}else(s.subtreeFlags&8772)!==0&&u!==null?(u.return=s,ae=u):Xd(e)}}function Xd(e){for(;ae!==null;){var t=ae;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:dt||xi(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!dt)if(r===null)i.componentDidMount();else{var s=t.elementType===t.type?r.memoizedProps:Zt(t.type,r.memoizedProps);i.componentDidUpdate(s,r.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var u=t.updateQueue;u!==null&&Jc(t,u,i);break;case 3:var p=t.updateQueue;if(p!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}Jc(t,p,r)}break;case 5:var x=t.stateNode;if(r===null&&t.flags&4){r=x;var _=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":_.autoFocus&&r.focus();break;case"img":_.src&&(r.src=_.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var P=t.alternate;if(P!==null){var G=P.memoizedState;if(G!==null){var Z=G.dehydrated;Z!==null&&Dn(Z)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(l(163))}dt||t.flags&512&&Rs(t)}catch(W){Ge(t,t.return,W)}}if(t===e){ae=null;break}if(r=t.sibling,r!==null){r.return=t.return,ae=r;break}ae=t.return}}function Jd(e){for(;ae!==null;){var t=ae;if(t===e){ae=null;break}var r=t.sibling;if(r!==null){r.return=t.return,ae=r;break}ae=t.return}}function ef(e){for(;ae!==null;){var t=ae;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{xi(4,t)}catch(_){Ge(t,r,_)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var s=t.return;try{i.componentDidMount()}catch(_){Ge(t,s,_)}}var u=t.return;try{Rs(t)}catch(_){Ge(t,u,_)}break;case 5:var p=t.return;try{Rs(t)}catch(_){Ge(t,p,_)}}}catch(_){Ge(t,t.return,_)}if(t===e){ae=null;break}var x=t.sibling;if(x!==null){x.return=t.return,ae=x;break}ae=t.return}}var cg=Math.ceil,ki=q.ReactCurrentDispatcher,Fs=q.ReactCurrentOwner,Bt=q.ReactCurrentBatchConfig,Le=0,tt=null,qe=null,ot=0,_t=0,io=$n(0),Je=0,cl=null,Er=0,Si=0,js=0,dl=null,wt=null,Is=0,ao=1/0,_n=null,bi=!1,Ps=null,Zn=null,_i=!1,qn=null,Ci=0,fl=0,Ds=null,Oi=-1,Ti=0;function pt(){return(Le&6)!==0?ze():Oi!==-1?Oi:Oi=ze()}function Qn(e){return(e.mode&1)===0?1:(Le&2)!==0&&ot!==0?ot&-ot:Yy.transition!==null?(Ti===0&&(Ti=Bl()),Ti):(e=Fe,e!==0||(e=window.event,e=e===void 0?16:Me(e.type)),e)}function Kt(e,t,r,i){if(50<fl)throw fl=0,Ds=null,Error(l(185));cr(e,r,i),((Le&2)===0||e!==tt)&&(e===tt&&((Le&2)===0&&(Si|=r),Je===4&&Kn(e,ot)),Et(e,i),r===1&&Le===0&&(t.mode&1)===0&&(ao=ze()+500,ni&&Vn()))}function Et(e,t){var r=e.callbackNode;Io(e,t);var i=Pr(e,e===tt?ot:0);if(i===0)r!==null&&Nr(r),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(r!=null&&Nr(r),t===1)e.tag===0?Gy(nf.bind(null,e)):Uc(nf.bind(null,e)),$y(function(){(Le&6)===0&&Vn()}),r=null;else{switch(zl(i)){case 1:r=In;break;case 4:r=jo;break;case 16:r=Fr;break;case 536870912:r=Ml;break;default:r=Fr}r=df(r,tf.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function tf(e,t){if(Oi=-1,Ti=0,(Le&6)!==0)throw Error(l(327));var r=e.callbackNode;if(so()&&e.callbackNode!==r)return null;var i=Pr(e,e===tt?ot:0);if(i===0)return null;if((i&30)!==0||(i&e.expiredLanes)!==0||t)t=Ai(e,i);else{t=i;var s=Le;Le|=2;var u=of();(tt!==e||ot!==t)&&(_n=null,ao=ze()+500,kr(e,t));do try{pg();break}catch(x){rf(e,x)}while(!0);ts(),ki.current=u,Le=s,qe!==null?t=0:(tt=null,ot=0,t=Je)}if(t!==0){if(t===2&&(s=Po(e),s!==0&&(i=s,t=Ms(e,s))),t===1)throw r=cl,kr(e,0),Kn(e,i),Et(e,ze()),r;if(t===6)Kn(e,i);else{if(s=e.current.alternate,(i&30)===0&&!dg(s)&&(t=Ai(e,i),t===2&&(u=Po(e),u!==0&&(i=u,t=Ms(e,u))),t===1))throw r=cl,kr(e,0),Kn(e,i),Et(e,ze()),r;switch(e.finishedWork=s,e.finishedLanes=i,t){case 0:case 1:throw Error(l(345));case 2:Sr(e,wt,_n);break;case 3:if(Kn(e,i),(i&130023424)===i&&(t=Is+500-ze(),10<t)){if(Pr(e,0)!==0)break;if(s=e.suspendedLanes,(s&i)!==i){pt(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Va(Sr.bind(null,e,wt,_n),t);break}Sr(e,wt,_n);break;case 4:if(Kn(e,i),(i&4194240)===i)break;for(t=e.eventTimes,s=-1;0<i;){var p=31-kt(i);u=1<<p,p=t[p],p>s&&(s=p),i&=~u}if(i=s,i=ze()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*cg(i/1960))-i,10<i){e.timeoutHandle=Va(Sr.bind(null,e,wt,_n),i);break}Sr(e,wt,_n);break;case 5:Sr(e,wt,_n);break;default:throw Error(l(329))}}}return Et(e,ze()),e.callbackNode===r?tf.bind(null,e):null}function Ms(e,t){var r=dl;return e.current.memoizedState.isDehydrated&&(kr(e,t).flags|=256),e=Ai(e,t),e!==2&&(t=wt,wt=r,t!==null&&Bs(t)),e}function Bs(e){wt===null?wt=e:wt.push.apply(wt,e)}function dg(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var i=0;i<r.length;i++){var s=r[i],u=s.getSnapshot;s=s.value;try{if(!Gt(u(),s))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Kn(e,t){for(t&=~js,t&=~Si,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-kt(t),i=1<<r;e[r]=-1,t&=~i}}function nf(e){if((Le&6)!==0)throw Error(l(327));so();var t=Pr(e,0);if((t&1)===0)return Et(e,ze()),null;var r=Ai(e,t);if(e.tag!==0&&r===2){var i=Po(e);i!==0&&(t=i,r=Ms(e,i))}if(r===1)throw r=cl,kr(e,0),Kn(e,t),Et(e,ze()),r;if(r===6)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sr(e,wt,_n),Et(e,ze()),null}function zs(e,t){var r=Le;Le|=1;try{return e(t)}finally{Le=r,Le===0&&(ao=ze()+500,ni&&Vn())}}function xr(e){qn!==null&&qn.tag===0&&(Le&6)===0&&so();var t=Le;Le|=1;var r=Bt.transition,i=Fe;try{if(Bt.transition=null,Fe=1,e)return e()}finally{Fe=i,Bt.transition=r,Le=t,(Le&6)===0&&Vn()}}function Us(){_t=io.current,$e(io)}function kr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Uy(r)),qe!==null)for(r=qe.return;r!==null;){var i=r;switch(Qa(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&ei();break;case 3:ro(),$e(gt),$e(st),us();break;case 5:as(i);break;case 4:ro();break;case 13:$e(Ve);break;case 19:$e(Ve);break;case 10:ns(i.type._context);break;case 22:case 23:Us()}r=r.return}if(tt=e,qe=e=Xn(e.current,null),ot=_t=t,Je=0,cl=null,js=Si=Er=0,wt=dl=null,mr!==null){for(t=0;t<mr.length;t++)if(r=mr[t],i=r.interleaved,i!==null){r.interleaved=null;var s=i.next,u=r.pending;if(u!==null){var p=u.next;u.next=s,i.next=p}r.pending=i}mr=null}return e}function rf(e,t){do{var r=qe;try{if(ts(),fi.current=gi,pi){for(var i=We.memoizedState;i!==null;){var s=i.queue;s!==null&&(s.pending=null),i=i.next}pi=!1}if(wr=0,et=Xe=We=null,ol=!1,ll=0,Fs.current=null,r===null||r.return===null){Je=1,cl=t,qe=null;break}e:{var u=e,p=r.return,x=r,_=t;if(t=ot,x.flags|=32768,_!==null&&typeof _=="object"&&typeof _.then=="function"){var P=_,G=x,Z=G.tag;if((G.mode&1)===0&&(Z===0||Z===11||Z===15)){var W=G.alternate;W?(G.updateQueue=W.updateQueue,G.memoizedState=W.memoizedState,G.lanes=W.lanes):(G.updateQueue=null,G.memoizedState=null)}var oe=Td(p);if(oe!==null){oe.flags&=-257,Ad(oe,p,x,u,t),oe.mode&1&&Od(u,P,t),t=oe,_=P;var ce=t.updateQueue;if(ce===null){var fe=new Set;fe.add(_),t.updateQueue=fe}else ce.add(_);break e}else{if((t&1)===0){Od(u,P,t),$s();break e}_=Error(l(426))}}else if(He&&x.mode&1){var Ye=Td(p);if(Ye!==null){(Ye.flags&65536)===0&&(Ye.flags|=256),Ad(Ye,p,x,u,t),Ja(oo(_,x));break e}}u=_=oo(_,x),Je!==4&&(Je=2),dl===null?dl=[u]:dl.push(u),u=p;do{switch(u.tag){case 3:u.flags|=65536,t&=-t,u.lanes|=t;var L=_d(u,_,t);Xc(u,L);break e;case 1:x=_;var T=u.type,F=u.stateNode;if((u.flags&128)===0&&(typeof T.getDerivedStateFromError=="function"||F!==null&&typeof F.componentDidCatch=="function"&&(Zn===null||!Zn.has(F)))){u.flags|=65536,t&=-t,u.lanes|=t;var K=Cd(u,x,t);Xc(u,K);break e}}u=u.return}while(u!==null)}af(r)}catch(pe){t=pe,qe===r&&r!==null&&(qe=r=r.return);continue}break}while(!0)}function of(){var e=ki.current;return ki.current=gi,e===null?gi:e}function $s(){(Je===0||Je===3||Je===2)&&(Je=4),tt===null||(Er&268435455)===0&&(Si&268435455)===0||Kn(tt,ot)}function Ai(e,t){var r=Le;Le|=2;var i=of();(tt!==e||ot!==t)&&(_n=null,kr(e,t));do try{fg();break}catch(s){rf(e,s)}while(!0);if(ts(),Le=r,ki.current=i,qe!==null)throw Error(l(261));return tt=null,ot=0,Je}function fg(){for(;qe!==null;)lf(qe)}function pg(){for(;qe!==null&&!ya();)lf(qe)}function lf(e){var t=cf(e.alternate,e,_t);e.memoizedProps=e.pendingProps,t===null?af(e):qe=t,Fs.current=null}function af(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=lg(r,t,_t),r!==null){qe=r;return}}else{if(r=ig(r,t),r!==null){r.flags&=32767,qe=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Je=6,qe=null;return}}if(t=t.sibling,t!==null){qe=t;return}qe=t=e}while(t!==null);Je===0&&(Je=5)}function Sr(e,t,r){var i=Fe,s=Bt.transition;try{Bt.transition=null,Fe=1,hg(e,t,r,i)}finally{Bt.transition=s,Fe=i}return null}function hg(e,t,r,i){do so();while(qn!==null);if((Le&6)!==0)throw Error(l(327));r=e.finishedWork;var s=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var u=r.lanes|r.childLanes;if(ka(e,u),e===tt&&(qe=tt=null,ot=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||_i||(_i=!0,df(Fr,function(){return so(),null})),u=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||u){u=Bt.transition,Bt.transition=null;var p=Fe;Fe=1;var x=Le;Le|=4,Fs.current=null,sg(e,r),Qd(r,e),jy($a),E=!!Ua,$a=Ua=null,e.current=r,ug(r),Lr(),Le=x,Fe=p,Bt.transition=u}else e.current=r;if(_i&&(_i=!1,qn=e,Ci=s),u=e.pendingLanes,u===0&&(Zn=null),va(r.stateNode),Et(e,ze()),t!==null)for(i=e.onRecoverableError,r=0;r<t.length;r++)s=t[r],i(s.value,{componentStack:s.stack,digest:s.digest});if(bi)throw bi=!1,e=Ps,Ps=null,e;return(Ci&1)!==0&&e.tag!==0&&so(),u=e.pendingLanes,(u&1)!==0?e===Ds?fl++:(fl=0,Ds=e):fl=0,Vn(),null}function so(){if(qn!==null){var e=zl(Ci),t=Bt.transition,r=Fe;try{if(Bt.transition=null,Fe=16>e?16:e,qn===null)var i=!1;else{if(e=qn,qn=null,Ci=0,(Le&6)!==0)throw Error(l(331));var s=Le;for(Le|=4,ae=e.current;ae!==null;){var u=ae,p=u.child;if((ae.flags&16)!==0){var x=u.deletions;if(x!==null){for(var _=0;_<x.length;_++){var P=x[_];for(ae=P;ae!==null;){var G=ae;switch(G.tag){case 0:case 11:case 15:ul(8,G,u)}var Z=G.child;if(Z!==null)Z.return=G,ae=Z;else for(;ae!==null;){G=ae;var W=G.sibling,oe=G.return;if(Wd(G),G===P){ae=null;break}if(W!==null){W.return=oe,ae=W;break}ae=oe}}}var ce=u.alternate;if(ce!==null){var fe=ce.child;if(fe!==null){ce.child=null;do{var Ye=fe.sibling;fe.sibling=null,fe=Ye}while(fe!==null)}}ae=u}}if((u.subtreeFlags&2064)!==0&&p!==null)p.return=u,ae=p;else e:for(;ae!==null;){if(u=ae,(u.flags&2048)!==0)switch(u.tag){case 0:case 11:case 15:ul(9,u,u.return)}var L=u.sibling;if(L!==null){L.return=u.return,ae=L;break e}ae=u.return}}var T=e.current;for(ae=T;ae!==null;){p=ae;var F=p.child;if((p.subtreeFlags&2064)!==0&&F!==null)F.return=p,ae=F;else e:for(p=T;ae!==null;){if(x=ae,(x.flags&2048)!==0)try{switch(x.tag){case 0:case 11:case 15:xi(9,x)}}catch(pe){Ge(x,x.return,pe)}if(x===p){ae=null;break e}var K=x.sibling;if(K!==null){K.return=x.return,ae=K;break e}ae=x.return}}if(Le=s,Vn(),Rt&&typeof Rt.onPostCommitFiberRoot=="function")try{Rt.onPostCommitFiberRoot(sr,e)}catch{}i=!0}return i}finally{Fe=r,Bt.transition=t}}return!1}function sf(e,t,r){t=oo(r,t),t=_d(e,t,1),e=Gn(e,t,1),t=pt(),e!==null&&(cr(e,1,t),Et(e,t))}function Ge(e,t,r){if(e.tag===3)sf(e,e,r);else for(;t!==null;){if(t.tag===3){sf(t,e,r);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Zn===null||!Zn.has(i))){e=oo(r,e),e=Cd(t,e,1),t=Gn(t,e,1),e=pt(),t!==null&&(cr(t,1,e),Et(t,e));break}}t=t.return}}function yg(e,t,r){var i=e.pingCache;i!==null&&i.delete(t),t=pt(),e.pingedLanes|=e.suspendedLanes&r,tt===e&&(ot&r)===r&&(Je===4||Je===3&&(ot&130023424)===ot&&500>ze()-Is?kr(e,0):js|=r),Et(e,t)}function uf(e,t){t===0&&((e.mode&1)===0?t=1:(t=Ir,Ir<<=1,(Ir&130023424)===0&&(Ir=4194304)));var r=pt();e=kn(e,t),e!==null&&(cr(e,t,r),Et(e,r))}function gg(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),uf(e,r)}function mg(e,t){var r=0;switch(e.tag){case 13:var i=e.stateNode,s=e.memoizedState;s!==null&&(r=s.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(l(314))}i!==null&&i.delete(t),uf(e,r)}var cf;cf=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||gt.current)vt=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return vt=!1,og(e,t,r);vt=(e.flags&131072)!==0}else vt=!1,He&&(t.flags&1048576)!==0&&$c(t,oi,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;wi(e,t),e=t.pendingProps;var s=Qr(t,st.current);no(t,r),s=fs(null,t,i,e,s,r);var u=ps();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,mt(i)?(u=!0,ti(t)):u=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,ls(t),s.updater=mi,t.stateNode=s,s._reactInternals=t,ws(t,i,e,r),t=Ss(null,t,i,!0,u,r)):(t.tag=0,He&&u&&qa(t),ft(null,t,s,r),t=t.child),t;case 16:i=t.elementType;e:{switch(wi(e,t),e=t.pendingProps,s=i._init,i=s(i._payload),t.type=i,s=t.tag=wg(i),e=Zt(i,e),s){case 0:t=ks(null,t,i,e,r);break e;case 1:t=Id(null,t,i,e,r);break e;case 11:t=Rd(null,t,i,e,r);break e;case 14:t=Nd(null,t,i,Zt(i.type,e),r);break e}throw Error(l(306,i,""))}return t;case 0:return i=t.type,s=t.pendingProps,s=t.elementType===i?s:Zt(i,s),ks(e,t,i,s,r);case 1:return i=t.type,s=t.pendingProps,s=t.elementType===i?s:Zt(i,s),Id(e,t,i,s,r);case 3:e:{if(Pd(t),e===null)throw Error(l(387));i=t.pendingProps,u=t.memoizedState,s=u.element,Kc(e,t),ci(t,i,null,r);var p=t.memoizedState;if(i=p.element,u.isDehydrated)if(u={element:i,isDehydrated:!1,cache:p.cache,pendingSuspenseBoundaries:p.pendingSuspenseBoundaries,transitions:p.transitions},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){s=oo(Error(l(423)),t),t=Dd(e,t,i,r,s);break e}else if(i!==s){s=oo(Error(l(424)),t),t=Dd(e,t,i,r,s);break e}else for(bt=Un(t.stateNode.containerInfo.firstChild),St=t,He=!0,Yt=null,r=qc(t,null,i,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Jr(),i===s){t=bn(e,t,r);break e}ft(e,t,i,r)}t=t.child}return t;case 5:return ed(t),e===null&&Xa(t),i=t.type,s=t.pendingProps,u=e!==null?e.memoizedProps:null,p=s.children,Ha(i,s)?p=null:u!==null&&Ha(i,u)&&(t.flags|=32),jd(e,t),ft(e,t,p,r),t.child;case 6:return e===null&&Xa(t),null;case 13:return Md(e,t,r);case 4:return is(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=eo(t,null,i,r):ft(e,t,i,r),t.child;case 11:return i=t.type,s=t.pendingProps,s=t.elementType===i?s:Zt(i,s),Rd(e,t,i,s,r);case 7:return ft(e,t,t.pendingProps,r),t.child;case 8:return ft(e,t,t.pendingProps.children,r),t.child;case 12:return ft(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(i=t.type._context,s=t.pendingProps,u=t.memoizedProps,p=s.value,Be(ai,i._currentValue),i._currentValue=p,u!==null)if(Gt(u.value,p)){if(u.children===s.children&&!gt.current){t=bn(e,t,r);break e}}else for(u=t.child,u!==null&&(u.return=t);u!==null;){var x=u.dependencies;if(x!==null){p=u.child;for(var _=x.firstContext;_!==null;){if(_.context===i){if(u.tag===1){_=Sn(-1,r&-r),_.tag=2;var P=u.updateQueue;if(P!==null){P=P.shared;var G=P.pending;G===null?_.next=_:(_.next=G.next,G.next=_),P.pending=_}}u.lanes|=r,_=u.alternate,_!==null&&(_.lanes|=r),rs(u.return,r,t),x.lanes|=r;break}_=_.next}}else if(u.tag===10)p=u.type===t.type?null:u.child;else if(u.tag===18){if(p=u.return,p===null)throw Error(l(341));p.lanes|=r,x=p.alternate,x!==null&&(x.lanes|=r),rs(p,r,t),p=u.sibling}else p=u.child;if(p!==null)p.return=u;else for(p=u;p!==null;){if(p===t){p=null;break}if(u=p.sibling,u!==null){u.return=p.return,p=u;break}p=p.return}u=p}ft(e,t,s.children,r),t=t.child}return t;case 9:return s=t.type,i=t.pendingProps.children,no(t,r),s=Dt(s),i=i(s),t.flags|=1,ft(e,t,i,r),t.child;case 14:return i=t.type,s=Zt(i,t.pendingProps),s=Zt(i.type,s),Nd(e,t,i,s,r);case 15:return Ld(e,t,t.type,t.pendingProps,r);case 17:return i=t.type,s=t.pendingProps,s=t.elementType===i?s:Zt(i,s),wi(e,t),t.tag=1,mt(i)?(e=!0,ti(t)):e=!1,no(t,r),Sd(t,i,s),ws(t,i,s,r),Ss(null,t,i,!0,e,r);case 19:return zd(e,t,r);case 22:return Fd(e,t,r)}throw Error(l(156,t.tag))};function df(e,t){return Dl(e,t)}function vg(e,t,r,i){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zt(e,t,r,i){return new vg(e,t,r,i)}function Hs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function wg(e){if(typeof e=="function")return Hs(e)?1:0;if(e!=null){if(e=e.$$typeof,e===U)return 11;if(e===de)return 14}return 2}function Xn(e,t){var r=e.alternate;return r===null?(r=zt(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function Ri(e,t,r,i,s,u){var p=2;if(i=e,typeof e=="function")Hs(e)&&(p=1);else if(typeof e=="string")p=5;else e:switch(e){case Y:return br(r.children,s,u,t);case J:p=8,s|=8;break;case le:return e=zt(12,r,t,s|2),e.elementType=le,e.lanes=u,e;case ne:return e=zt(13,r,t,s),e.elementType=ne,e.lanes=u,e;case re:return e=zt(19,r,t,s),e.elementType=re,e.lanes=u,e;case ie:return Ni(r,s,u,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ge:p=10;break e;case ke:p=9;break e;case U:p=11;break e;case de:p=14;break e;case se:p=16,i=null;break e}throw Error(l(130,e==null?e:typeof e,""))}return t=zt(p,r,t,s),t.elementType=e,t.type=i,t.lanes=u,t}function br(e,t,r,i){return e=zt(7,e,i,t),e.lanes=r,e}function Ni(e,t,r,i){return e=zt(22,e,i,t),e.elementType=ie,e.lanes=r,e.stateNode={isHidden:!1},e}function Vs(e,t,r){return e=zt(6,e,null,t),e.lanes=r,e}function Ws(e,t,r){return t=zt(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Eg(e,t,r,i,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Mr(0),this.expirationTimes=Mr(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Mr(0),this.identifierPrefix=i,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Gs(e,t,r,i,s,u,p,x,_){return e=new Eg(e,t,r,x,_),t===1?(t=1,u===!0&&(t|=8)):t=0,u=zt(3,null,null,t),e.current=u,u.stateNode=e,u.memoizedState={element:i,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},ls(u),e}function xg(e,t,r){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:X,key:i==null?null:""+i,children:e,containerInfo:t,implementation:r}}function ff(e){if(!e)return Hn;e=e._reactInternals;e:{if(yt(e)!==e||e.tag!==1)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(mt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(l(171))}if(e.tag===1){var r=e.type;if(mt(r))return Bc(e,r,t)}return t}function pf(e,t,r,i,s,u,p,x,_){return e=Gs(r,i,!0,e,s,u,p,x,_),e.context=ff(null),r=e.current,i=pt(),s=Qn(r),u=Sn(i,s),u.callback=t??null,Gn(r,u,s),e.current.lanes=s,cr(e,s,i),Et(e,i),e}function Li(e,t,r,i){var s=t.current,u=pt(),p=Qn(s);return r=ff(r),t.context===null?t.context=r:t.pendingContext=r,t=Sn(u,p),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=Gn(s,t,p),e!==null&&(Kt(e,s,p,u),ui(e,s,p)),p}function Fi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function hf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function Ys(e,t){hf(e,t),(e=e.alternate)&&hf(e,t)}function kg(){return null}var yf=typeof reportError=="function"?reportError:function(e){console.error(e)};function Zs(e){this._internalRoot=e}ji.prototype.render=Zs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(l(409));Li(e,t,null,null)},ji.prototype.unmount=Zs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xr(function(){Li(null,e,null,null)}),t[vn]=null}};function ji(e){this._internalRoot=e}ji.prototype.unstable_scheduleHydration=function(e){if(e){var t=$l();e={blockedOn:null,target:e,priority:t};for(var r=0;r<jt.length&&t!==0&&t<jt[r].priority;r++);jt.splice(r,0,e),r===0&&zo(e)}};function qs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ii(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function gf(){}function Sg(e,t,r,i,s){if(s){if(typeof i=="function"){var u=i;i=function(){var P=Fi(p);u.call(P)}}var p=pf(t,i,e,0,null,!1,!1,"",gf);return e._reactRootContainer=p,e[vn]=p.current,Qo(e.nodeType===8?e.parentNode:e),xr(),p}for(;s=e.lastChild;)e.removeChild(s);if(typeof i=="function"){var x=i;i=function(){var P=Fi(_);x.call(P)}}var _=Gs(e,0,!1,null,null,!1,!1,"",gf);return e._reactRootContainer=_,e[vn]=_.current,Qo(e.nodeType===8?e.parentNode:e),xr(function(){Li(t,_,r,i)}),_}function Pi(e,t,r,i,s){var u=r._reactRootContainer;if(u){var p=u;if(typeof s=="function"){var x=s;s=function(){var _=Fi(p);x.call(_)}}Li(t,p,e,s)}else p=Sg(r,t,e,s,i);return Fi(p)}Ul=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=ur(t.pendingLanes);r!==0&&(Do(t,r|1),Et(t,ze()),(Le&6)===0&&(ao=ze()+500,Vn()))}break;case 13:xr(function(){var i=kn(e,1);if(i!==null){var s=pt();Kt(i,e,1,s)}}),Ys(e,1)}},Mo=function(e){if(e.tag===13){var t=kn(e,134217728);if(t!==null){var r=pt();Kt(t,e,134217728,r)}Ys(e,134217728)}},Br=function(e){if(e.tag===13){var t=Qn(e),r=kn(e,t);if(r!==null){var i=pt();Kt(r,e,t,i)}Ys(e,t)}},$l=function(){return Fe},Hl=function(e,t){var r=Fe;try{return Fe=e,t()}finally{Fe=r}},Ar=function(e,t,r){switch(t){case"input":if(hn(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var i=r[t];if(i!==e&&i.form===e.form){var s=Jl(i);if(!s)throw Error(l(90));rr(i),hn(i,s)}}}break;case"textarea":_o(e,r);break;case"select":t=r.value,t!=null&&Jt(e,!!r.multiple,t,!1)}},Nl=zs,Ll=xr;var bg={usingClientEntryPoint:!1,Events:[Jo,Zr,Jl,Al,Rl,zs]},pl={findFiberByHostInstance:pr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},_g={bundleType:pl.bundleType,version:pl.version,rendererPackageName:pl.rendererPackageName,rendererConfig:pl.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:q.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Il(e),e===null?null:e.stateNode},findFiberByHostInstance:pl.findFiberByHostInstance||kg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Di=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Di.isDisabled&&Di.supportsFiber)try{sr=Di.inject(_g),Rt=Di}catch{}}return xt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=bg,xt.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!qs(t))throw Error(l(200));return xg(e,t,null,r)},xt.createRoot=function(e,t){if(!qs(e))throw Error(l(299));var r=!1,i="",s=yf;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Gs(e,1,!1,null,null,r,!1,i,s),e[vn]=t.current,Qo(e.nodeType===8?e.parentNode:e),new Zs(t)},xt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(l(188)):(e=Object.keys(e).join(","),Error(l(268,e)));return e=Il(t),e=e===null?null:e.stateNode,e},xt.flushSync=function(e){return xr(e)},xt.hydrate=function(e,t,r){if(!Ii(t))throw Error(l(200));return Pi(null,e,t,!0,r)},xt.hydrateRoot=function(e,t,r){if(!qs(e))throw Error(l(405));var i=r!=null&&r.hydratedSources||null,s=!1,u="",p=yf;if(r!=null&&(r.unstable_strictMode===!0&&(s=!0),r.identifierPrefix!==void 0&&(u=r.identifierPrefix),r.onRecoverableError!==void 0&&(p=r.onRecoverableError)),t=pf(t,null,e,1,r??null,s,!1,u,p),e[vn]=t.current,Qo(e),i)for(e=0;e<i.length;e++)r=i[e],s=r._getVersion,s=s(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,s]:t.mutableSourceEagerHydrationData.push(r,s);return new ji(t)},xt.render=function(e,t,r){if(!Ii(t))throw Error(l(200));return Pi(null,e,t,!1,r)},xt.unmountComponentAtNode=function(e){if(!Ii(e))throw Error(l(40));return e._reactRootContainer?(xr(function(){Pi(null,null,e,!1,function(){e._reactRootContainer=null,e[vn]=null})}),!0):!1},xt.unstable_batchedUpdates=zs,xt.unstable_renderSubtreeIntoContainer=function(e,t,r,i){if(!Ii(r))throw Error(l(200));if(e==null||e._reactInternals===void 0)throw Error(l(38));return Pi(e,t,r,!1,i)},xt.version="18.3.1-next-f1338f8080-20240426",xt}var Ff;function Ip(){if(Ff)return ru.exports;Ff=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(o){console.error(o)}}return n(),ru.exports=Am(),ru.exports}var Pp=Ip();const jf=kl(Pp);function ku(){return cn=Object.assign||function(n){for(let o=1;o<arguments.length;o++){const l=arguments[o];for(const a in l)Object.prototype.hasOwnProperty.call(l,a)&&(n[a]=l[a])}return n},cn.apply(this,arguments)}function Wu(n,o,l){return l={path:o,exports:{},require:function(a,c){return Rm(a,c??l.path)}},n(l,l.exports),l.exports}function Rm(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}let yo;typeof window<"u"?yo=window:typeof self<"u"?yo=self:yo={};yo.setTimeout;yo.clearTimeout;function Sl(){}const yl=yo.performance||{};yl.now||yl.mozNow||yl.msNow||yl.oNow||yl.webkitNow;const Nm=function(n,o){let l;const a=toObject(n);let c;for(let d=1;d<arguments.length;d++){l=Object(arguments[d]);for(const f in l)hasOwnProperty.call(l,f)&&(a[f]=l[f]);if(getOwnPropertySymbols){c=getOwnPropertySymbols(l);for(let f=0;f<c.length;f++)propIsEnumerable.call(l,c[f])&&(a[c[f]]=l[c[f]])}}return a},Lm=Mp()?N.useLayoutEffect:N.useEffect,If={};let Dp=Sl;Dp=function(o){If[o]||(If[o]=!0)};function Fm(n,o){if(n!=null)if(Im(n))n(o);else try{n.current=o}catch{throw new Error('Cannot assign value "'+o+'" to ref "'+n+'"')}}function Mp(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function oa(n){return N.forwardRef(n)}function jm(n){return Mp()?n?n.ownerDocument:document:null}function Im(n){return!!(n&&{}.toString.call(n)=="[object Function]")}function Pm(n){return typeof n=="string"}let Bp=Sl;Bp=function(o){const l=N.useRef(o);N.useEffect(function(){return void(l.current=o)},[o]),N.useEffect(function(){return Dp(l.current)},[])};function Dm(){const n=N.useState(Object.create(null)),o=n[1];return N.useCallback(function(){o(Object.create(null))},[])}function Mm(){for(var n=arguments.length,o=new Array(n),l=0;l<n;l++)o[l]=arguments[l];return N.useMemo(function(){return o.every(function(a){return a==null})?null:function(a){o.forEach(function(c){Fm(c,a)})}},[].concat(o))}function Hi(n,o){return function(l){if(n&&n(l),!l.defaultPrevented)return o(l)}}const zp=function(o){const l=o.children,a=o.type,c=a===void 0?"reach-portal":a,d=N.useRef(null),f=N.useRef(null),y=Dm();return Lm(function(){if(!d.current)return;const h=d.current.ownerDocument;return f.current=h==null?void 0:h.createElement(c),h.body.appendChild(f.current),y(),function(){f.current&&f.current.ownerDocument&&f.current.ownerDocument.body.removeChild(f.current)}},[c,y]),f.current?Pp.createPortal(l,f.current):N.createElement("span",{ref:d})};zp.displayName="Portal";function Bm(n,o){if(n==null)return{};const l={},a=Object.keys(n);let c,d;for(d=0;d<a.length;d++)c=a[d],!(o.indexOf(c)>=0)&&(l[c]=n[c]);return l}const zm=Wu(function(n,o){(function(){const l=typeof Symbol=="function"&&Symbol.for,a=l?Symbol.for("react.element"):60103,c=l?Symbol.for("react.portal"):60106,d=l?Symbol.for("react.fragment"):60107,f=l?Symbol.for("react.strict_mode"):60108,y=l?Symbol.for("react.profiler"):60114,h=l?Symbol.for("react.provider"):60109,m=l?Symbol.for("react.context"):60110,S=l?Symbol.for("react.async_mode"):60111,k=l?Symbol.for("react.concurrent_mode"):60111,C=l?Symbol.for("react.forward_ref"):60112,A=l?Symbol.for("react.suspense"):60113,b=l?Symbol.for("react.suspense_list"):60120,v=l?Symbol.for("react.memo"):60115,w=l?Symbol.for("react.lazy"):60116,I=l?Symbol.for("react.block"):60121,j=l?Symbol.for("react.fundamental"):60117,B=l?Symbol.for("react.responder"):60118,q=l?Symbol.for("react.scope"):60119;function te(ye){return typeof ye=="string"||typeof ye=="function"||ye===d||ye===k||ye===y||ye===f||ye===A||ye===b||typeof ye=="object"&&ye!==null&&(ye.$$typeof===w||ye.$$typeof===v||ye.$$typeof===h||ye.$$typeof===m||ye.$$typeof===C||ye.$$typeof===j||ye.$$typeof===B||ye.$$typeof===q||ye.$$typeof===I)}function X(ye){if(typeof ye=="object"&&ye!==null){const hn=ye.$$typeof;switch(hn){case a:var pn=ye.type;switch(pn){case S:case k:case d:case y:case f:case A:return pn;default:var or=pn&&pn.$$typeof;switch(or){case m:case C:case w:case v:case h:return or;default:return hn}}case c:return hn}}}const Y=S,J=k,le=m,ge=h,ke=a,U=C,ne=d,re=w,de=v,se=c,ie=y,z=f,Q=A;let V=!1;function O(ye){return V||(V=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),M(ye)||X(ye)===S}function M(ye){return X(ye)===k}function ee(ye){return X(ye)===m}function me(ye){return X(ye)===h}function be(ye){return typeof ye=="object"&&ye!==null&&ye.$$typeof===a}function ve(ye){return X(ye)===C}function _e(ye){return X(ye)===d}function Se(ye){return X(ye)===w}function Ne(ye){return X(ye)===v}function Ke(ye){return X(ye)===c}function fn(ye){return X(ye)===y}function rr(ye){return X(ye)===f}function An(ye){return X(ye)===A}o.AsyncMode=Y,o.ConcurrentMode=J,o.ContextConsumer=le,o.ContextProvider=ge,o.Element=ke,o.ForwardRef=U,o.Fragment=ne,o.Lazy=re,o.Memo=de,o.Portal=se,o.Profiler=ie,o.StrictMode=z,o.Suspense=Q,o.isAsyncMode=O,o.isConcurrentMode=M,o.isContextConsumer=ee,o.isContextProvider=me,o.isElement=be,o.isForwardRef=ve,o.isFragment=_e,o.isLazy=Se,o.isMemo=Ne,o.isPortal=Ke,o.isProfiler=fn,o.isStrictMode=rr,o.isSuspense=An,o.isValidElementType=te,o.typeOf=X})()}),Up=Wu(function(n){n.exports=zm}),Um="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",_r=Um;let Su=function(){};{var $m=_r,bu={},Hm=Function.call.bind(Object.prototype.hasOwnProperty);Su=function(n){const o="Warning: "+n;typeof console<"u"&&console.error(o);try{throw new Error(o)}catch{}}}function $p(n,o,l,a,c){for(const f in n)if(Hm(n,f)){var d;try{if(typeof n[f]!="function"){const y=Error((a||"React class")+": "+l+" type `"+f+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof n[f]+"`.");throw y.name="Invariant Violation",y}d=n[f](o,f,a,l,null,$m)}catch(y){d=y}if(d&&!(d instanceof Error)&&Su((a||"React class")+": type specification of "+l+" `"+f+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof d+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),d instanceof Error&&!(d.message in bu)){bu[d.message]=!0;const y=c?c():"";Su("Failed "+l+" type: "+d.message+(y??""))}}}$p.resetWarningCache=function(){bu={}};const Pf=$p,Vm=Function.call.bind(Object.prototype.hasOwnProperty);let ml=function(){};ml=function(n){const o="Warning: "+n;typeof console<"u"&&console.error(o);try{throw new Error(o)}catch{}};function zi(){return null}const Wm=function(n,o){const l=typeof Symbol=="function"&&Symbol.iterator,a="@@iterator";function c(U){const ne=U&&(l&&U[l]||U[a]);if(typeof ne=="function")return ne}const d="<<anonymous>>",f={array:S("array"),bool:S("boolean"),func:S("function"),number:S("number"),object:S("object"),string:S("string"),symbol:S("symbol"),any:k(),arrayOf:C,element:A(),elementType:b(),instanceOf:v,node:B(),objectOf:I,oneOf:w,oneOfType:j,shape:q,exact:te};function y(U,ne){return U===ne?U!==0||1/U===1/ne:U!==U&&ne!==ne}function h(U){this.message=U,this.stack=""}h.prototype=Error.prototype;function m(U){function ne(de,se,ie,z,Q,V,O){if(z=z||d,V=V||ie,O!==_r){const M=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw M.name="Invariant Violation",M}return se[ie]==null?de?se[ie]===null?new h("The "+Q+" `"+V+"` is marked as required "+("in `"+z+"`, but its value is `null`.")):new h("The "+Q+" `"+V+"` is marked as required in "+("`"+z+"`, but its value is `undefined`.")):null:U(se,ie,z,Q,V)}const re=ne.bind(null,!1);return re.isRequired=ne.bind(null,!0),re}function S(U){function ne(re,de,se,ie,z,Q){const V=re[de];if(J(V)!==U){const M=le(V);return new h("Invalid "+ie+" `"+z+"` of type "+("`"+M+"` supplied to `"+se+"`, expected ")+("`"+U+"`."))}return null}return m(ne)}function k(){return m(zi)}function C(U){function ne(re,de,se,ie,z){if(typeof U!="function")return new h("Property `"+z+"` of component `"+se+"` has invalid PropType notation inside arrayOf.");const Q=re[de];if(!Array.isArray(Q)){const V=J(Q);return new h("Invalid "+ie+" `"+z+"` of type "+("`"+V+"` supplied to `"+se+"`, expected an array."))}for(let V=0;V<Q.length;V++){const O=U(Q,V,se,ie,z+"["+V+"]",_r);if(O instanceof Error)return O}return null}return m(ne)}function A(){function U(ne,re,de,se,ie){const z=ne[re];if(!n(z)){const Q=J(z);return new h("Invalid "+se+" `"+ie+"` of type "+("`"+Q+"` supplied to `"+de+"`, expected a single ReactElement."))}return null}return m(U)}function b(){function U(ne,re,de,se,ie){const z=ne[re];if(!Up.isValidElementType(z)){const Q=J(z);return new h("Invalid "+se+" `"+ie+"` of type "+("`"+Q+"` supplied to `"+de+"`, expected a single ReactElement type."))}return null}return m(U)}function v(U){function ne(re,de,se,ie,z){if(!(re[de]instanceof U)){const Q=U.name||d,V=ke(re[de]);return new h("Invalid "+ie+" `"+z+"` of type "+("`"+V+"` supplied to `"+se+"`, expected ")+("instance of `"+Q+"`."))}return null}return m(ne)}function w(U){if(!Array.isArray(U))return arguments.length>1?ml("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):ml("Invalid argument supplied to oneOf, expected an array."),zi;function ne(re,de,se,ie,z){const Q=re[de];for(let O=0;O<U.length;O++)if(y(Q,U[O]))return null;const V=JSON.stringify(U,function(M,ee){return le(ee)==="symbol"?String(ee):ee});return new h("Invalid "+ie+" `"+z+"` of value `"+String(Q)+"` "+("supplied to `"+se+"`, expected one of "+V+"."))}return m(ne)}function I(U){function ne(re,de,se,ie,z){if(typeof U!="function")return new h("Property `"+z+"` of component `"+se+"` has invalid PropType notation inside objectOf.");const Q=re[de],V=J(Q);if(V!=="object")return new h("Invalid "+ie+" `"+z+"` of type "+("`"+V+"` supplied to `"+se+"`, expected an object."));for(const O in Q)if(Vm(Q,O)){const M=U(Q,O,se,ie,z+"."+O,_r);if(M instanceof Error)return M}return null}return m(ne)}function j(U){if(!Array.isArray(U))return ml("Invalid argument supplied to oneOfType, expected an instance of array."),zi;for(let re=0;re<U.length;re++){const de=U[re];if(typeof de!="function")return ml("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+ge(de)+" at index "+re+"."),zi}function ne(re,de,se,ie,z){for(let Q=0;Q<U.length;Q++){const V=U[Q];if(V(re,de,se,ie,z,_r)==null)return null}return new h("Invalid "+ie+" `"+z+"` supplied to "+("`"+se+"`."))}return m(ne)}function B(){function U(ne,re,de,se,ie){return X(ne[re])?null:new h("Invalid "+se+" `"+ie+"` supplied to "+("`"+de+"`, expected a ReactNode."))}return m(U)}function q(U){function ne(re,de,se,ie,z){const Q=re[de],V=J(Q);if(V!=="object")return new h("Invalid "+ie+" `"+z+"` of type `"+V+"` "+("supplied to `"+se+"`, expected `object`."));for(const O in U){const M=U[O];if(!M)continue;const ee=M(Q,O,se,ie,z+"."+O,_r);if(ee)return ee}return null}return m(ne)}function te(U){function ne(re,de,se,ie,z){const Q=re[de],V=J(Q);if(V!=="object")return new h("Invalid "+ie+" `"+z+"` of type `"+V+"` "+("supplied to `"+se+"`, expected `object`."));const O=Nm({},re[de],U);for(const M in O){const ee=U[M];if(!ee)return new h("Invalid "+ie+" `"+z+"` key `"+M+"` supplied to `"+se+"`.\nBad object: "+JSON.stringify(re[de],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(U),null,"  "));const me=ee(Q,M,se,ie,z+"."+M,_r);if(me)return me}return null}return m(ne)}function X(U){switch(typeof U){case"number":case"string":case"undefined":return!0;case"boolean":return!U;case"object":if(Array.isArray(U))return U.every(X);if(U===null||n(U))return!0;var ne=c(U);if(ne){const re=ne.call(U);let de;if(ne!==U.entries){for(;!(de=re.next()).done;)if(!X(de.value))return!1}else for(;!(de=re.next()).done;){const se=de.value;if(se&&!X(se[1]))return!1}}else return!1;return!0;default:return!1}}function Y(U,ne){return U==="symbol"?!0:ne?ne["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&ne instanceof Symbol:!1}function J(U){const ne=typeof U;return Array.isArray(U)?"array":U instanceof RegExp?"object":Y(ne,U)?"symbol":ne}function le(U){if(typeof U>"u"||U===null)return""+U;const ne=J(U);if(ne==="object"){if(U instanceof Date)return"date";if(U instanceof RegExp)return"regexp"}return ne}function ge(U){const ne=le(U);switch(ne){case"array":case"object":return"an "+ne;case"boolean":case"date":case"regexp":return"a "+ne;default:return ne}}function ke(U){return!U.constructor||!U.constructor.name?d:U.constructor.name}return f.checkPropTypes=Pf,f.resetWarningCache=Pf.resetWarningCache,f.PropTypes=f,f},Ie=Wu(function(n){{const o=Up;n.exports=Wm(o.isElement)}}),_u="data-focus-lock",Hp="data-focus-lock-disabled",Gm="data-no-focus-lock",Ym="data-autofocus-inside";function Zm(n,o){return typeof n=="function"?n(o):n&&(n.current=o),n}function qm(n,o){var l=N.useState(function(){return{value:n,callback:o,facade:{get current(){return l.value},set current(a){const c=l.value;c!==a&&(l.value=a,l.callback(a,c))}}}})[0];return l.callback=o,l.facade}function Vp(n,o){return qm(o,function(l){return n.forEach(function(a){return Zm(a,l)})})}const iu={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"};Ie.node;/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Zi=function(){return Zi=Object.assign||function(o){for(var l,a=1,c=arguments.length;a<c;a++){l=arguments[a];for(const d in l)Object.prototype.hasOwnProperty.call(l,d)&&(o[d]=l[d])}return o},Zi.apply(this,arguments)};function Qm(n,o){const l={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&o.indexOf(a)<0&&(l[a]=n[a]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,a=Object.getOwnPropertySymbols(n);c<a.length;c++)o.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(n,a[c])&&(l[a[c]]=n[a[c]]);return l}function Wp(n){return n}function Gp(n,o){o===void 0&&(o=Wp);let l=[],a=!1;return{read:function(){if(a)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:n},useMedium:function(d){const f=o(d,a);return l.push(f),function(){l=l.filter(function(y){return y!==f})}},assignSyncMedium:function(d){for(a=!0;l.length;){const f=l;l=[],f.forEach(d)}l={push:function(f){return d(f)},filter:function(){return l}}},assignMedium:function(d){a=!0;let f=[];if(l.length){const m=l;l=[],m.forEach(d),f=l}const y=function(){const m=f;f=[],m.forEach(d)},h=function(){return Promise.resolve().then(y)};h(),l={push:function(m){f.push(m),h()},filter:function(m){return f=f.filter(m),l}}}}}function Gu(n,o){return o===void 0&&(o=Wp),Gp(n,o)}function Yp(n){n===void 0&&(n={});const o=Gp(null);return o.options=Zi({async:!0,ssr:!1},n),o}const Zp=function(n){const o=n.sideCar,l=Qm(n,["sideCar"]);if(!o)throw new Error("Sidecar: please provide `sideCar` property to import the right car");const a=o.read();if(!a)throw new Error("Sidecar medium not found");return N.createElement(a,Zi({},l))};Zp.isSideCarExport=!0;function Km(n,o){return n.useMedium(o),Zp}const qp=Gu({},function(n){const o=n.target,l=n.currentTarget;return{target:o,currentTarget:l}}),Qp=Gu(),Xm=Gu(),Jm=Yp({async:!0}),ev=[],la=N.forwardRef(function(o,l){let a;const c=N.useState(),d=c[0],f=c[1],y=N.useRef(),h=N.useRef(!1),m=N.useRef(null),S=o.children,k=o.disabled,C=o.noFocusGuards,A=o.persistentFocus,b=o.crossFrame,v=o.autoFocus,w=o.allowTextSelection,I=o.group,j=o.className,B=o.whiteList,q=o.shards,te=q===void 0?ev:q,X=o.as,Y=X===void 0?"div":X,J=o.lockProps,le=J===void 0?{}:J,ge=o.sideCar,ke=o.returnFocus,U=o.onActivation,ne=o.onDeactivation,re=N.useState({}),de=re[0],se=N.useCallback(function(){m.current=m.current||document&&document.activeElement,y.current&&U&&U(y.current),h.current=!0},[U]),ie=N.useCallback(function(){h.current=!1,ne&&ne(y.current)},[ne]),z=N.useCallback(function(ve){const _e=m.current;if(ke&&_e&&_e.focus){const Se=typeof ke=="object"?ke:void 0;m.current=null,ve?Promise.resolve().then(function(){return _e.focus(Se)}):_e.focus(Se)}},[ke]),Q=N.useCallback(function(ve){h.current&&qp.useMedium(ve)},[]),V=Qp.useMedium,O=N.useCallback(function(ve){y.current!==ve&&(y.current=ve,f(ve))},[]);typeof w<"u"&&console.warn("React-Focus-Lock: allowTextSelection is deprecated and enabled by default"),N.useEffect(function(){y.current||console.error("FocusLock: could not obtain ref to internal node")},[]);const M=ku((a={},a[Hp]=k&&"disabled",a[_u]=I,a),le),ee=C!==!0,me=ee&&C!=="tail",be=Vp([l,O]);return N.createElement(N.Fragment,null,ee&&[N.createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:k?-1:0,style:iu}),N.createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:k?-1:1,style:iu})],!k&&N.createElement(ge,{id:de,sideCar:Jm,observed:d,disabled:k,persistentFocus:A,crossFrame:b,autoFocus:v,whiteList:B,shards:te,onActivation:se,onDeactivation:ie,returnFocus:z}),N.createElement(Y,ku({ref:be},M,{className:j,onBlur:V,onFocus:Q}),S),me&&N.createElement("div",{"data-focus-guard":!0,tabIndex:k?-1:0,style:iu}))});la.propTypes={children:Ie.node,disabled:Ie.bool,returnFocus:Ie.oneOfType([Ie.bool,Ie.object]),noFocusGuards:Ie.bool,allowTextSelection:Ie.bool,autoFocus:Ie.bool,persistentFocus:Ie.bool,crossFrame:Ie.bool,group:Ie.string,className:Ie.string,whiteList:Ie.func,shards:Ie.arrayOf(Ie.any),as:Ie.oneOfType([Ie.string,Ie.func,Ie.object]),lockProps:Ie.object,onActivation:Ie.func,onDeactivation:Ie.func,sideCar:Ie.any.isRequired};la.defaultProps={children:void 0,disabled:!1,returnFocus:!1,noFocusGuards:!1,autoFocus:!0,persistentFocus:!1,crossFrame:!0,allowTextSelection:void 0,group:void 0,className:void 0,whiteList:void 0,shards:void 0,as:"div",lockProps:{},onActivation:void 0,onDeactivation:void 0};function Cu(n,o){return Cu=Object.setPrototypeOf||function(a,c){return a.__proto__=c,a},Cu(n,o)}function tv(n,o){n.prototype=Object.create(o.prototype),n.prototype.constructor=n,Cu(n,o)}function nv(n,o,l){return o in n?Object.defineProperty(n,o,{value:l,enumerable:!0,configurable:!0,writable:!0}):n[o]=l,n}function rv(n,o){{if(typeof n!="function")throw new Error("Expected reducePropsToState to be a function.");if(typeof o!="function")throw new Error("Expected handleStateChangeOnClient to be a function.")}function l(a){return a.displayName||a.name||"Component"}return function(c){if(typeof c!="function")throw new Error("Expected WrappedComponent to be a React component.");const d=[];let f;function y(){f=n(d.map(function(m){return m.props})),o(f)}const h=function(m){tv(S,m);function S(){return m.apply(this,arguments)||this}S.peek=function(){return f};const k=S.prototype;return k.componentDidMount=function(){d.push(this),y()},k.componentDidUpdate=function(){y()},k.componentWillUnmount=function(){const A=d.indexOf(this);d.splice(A,1),y()},k.render=function(){return N.createElement(c,this.props)},S}(N.PureComponent);return nv(h,"displayName","SideEffect("+l(c)+")"),h}}const nr=function(n){const o=Array(n.length);for(let l=0;l<n.length;++l)o[l]=n[l];return o},Ou=function(n){return Array.isArray(n)?n:[n]},ov=function(n){const o=new Set,l=n.length;for(let a=0;a<l;a+=1)for(let c=a+1;c<l;c+=1){const d=n[a].compareDocumentPosition(n[c]);(d&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&o.add(c),(d&Node.DOCUMENT_POSITION_CONTAINS)>0&&o.add(a)}return n.filter(function(a,c){return!o.has(c)})};var Kp=function(n){return n.parentNode?Kp(n.parentNode):n};const Yu=function(n){return Ou(n).filter(Boolean).reduce(function(l,a){const c=a.getAttribute(_u);return l.push.apply(l,c?ov(nr(Kp(a).querySelectorAll("["+_u+'="'+c+'"]:not(['+Hp+'="disabled"])'))):[a]),l},[])},lv=function(n){return!n||!n.getPropertyValue?!1:n.getPropertyValue("display")==="none"||n.getPropertyValue("visibility")==="hidden"};var Xp=function(n){return!n||n===document||n&&n.nodeType===Node.DOCUMENT_NODE||!lv(window.getComputedStyle(n,null))&&Xp(n.parentNode&&n.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?n.parentNode.host:n.parentNode)};const iv=function(n){return!((n.tagName==="INPUT"||n.tagName==="BUTTON")&&(n.type==="hidden"||n.disabled))},Zu=function(n){return!!(n&&n.dataset&&n.dataset.focusGuard)},qi=function(n){return!Zu(n)},av=function(n){return!!n},sv=function(n,o){const l=n.tabIndex-o.tabIndex,a=n.index-o.index;if(l){if(!n.tabIndex)return 1;if(!o.tabIndex)return-1}return l||a},Jp=function(n,o,l){return nr(n).map(function(a,c){return{node:a,index:c,tabIndex:l&&a.tabIndex===-1?(a.dataset||{}).focusGuard?0:-1:a.tabIndex}}).filter(function(a){return!o||a.tabIndex>=0}).sort(sv)},uv=["button:enabled","select:enabled","textarea:enabled","input:enabled","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[tabindex]","[contenteditable]","[autofocus]"],Tu=uv.join(","),cv=Tu+", [data-focus-guard]",qu=function(n,o){return n.reduce(function(l,a){return l.concat(nr(a.querySelectorAll(o?cv:Tu)),a.parentNode?nr(a.parentNode.querySelectorAll(Tu)).filter(function(c){return c===a}):[])},[])},dv=function(n){const o=n.querySelectorAll("["+Ym+"]");return nr(o).map(function(l){return qu([l])}).reduce(function(l,a){return l.concat(a)},[])},Qu=function(n){return nr(n).filter(function(o){return Xp(o)}).filter(function(o){return iv(o)})},Au=function(n,o){return Jp(Qu(qu(n,o)),!0,o)},Df=function(n){return Jp(Qu(qu(n)),!1)},fv=function(n){return Qu(dv(n))};var Ru=function(n,o){return o===void 0&&(o=[]),o.push(n),n.parentNode&&Ru(n.parentNode,o),o};const au=function(n,o){const l=Ru(n),a=Ru(o);for(let c=0;c<l.length;c+=1){const d=l[c];if(a.indexOf(d)>=0)return d}return!1},eh=function(n,o,l){const a=Ou(n),c=Ou(o),d=a[0];let f=!1;return c.filter(Boolean).forEach(function(y){f=au(f||y,y)||f,l.filter(Boolean).forEach(function(h){const m=au(d,h);m&&(!f||m.contains(f)?f=m:f=au(m,f))})}),f},pv=function(n){return n.reduce(function(o,l){return o.concat(fv(l))},[])},hv=function(n){const o=Yu(n).filter(qi),l=eh(n,n,o),a=Au([l],!0),c=Au(o).filter(function(d){const f=d.node;return qi(f)}).map(function(d){return d.node});return a.map(function(d){const f=d.node,y=d.index;return{node:f,index:y,lockItem:c.indexOf(f)>=0,guard:Zu(f)}})},yv=function(n){return n===document.activeElement},gv=function(n){return!!nr(n.querySelectorAll("iframe")).some(function(o){return yv(o)})},th=function(n){const o=document&&document.activeElement;return!o||o.dataset&&o.dataset.focusGuard?!1:Yu(n).reduce(function(l,a){return l||a.contains(o)||gv(a)},!1)},mv=function(){return document&&nr(document.querySelectorAll("["+Gm+"]")).some(function(n){return n.contains(document.activeElement)})},nh=function(n){return n.tagName==="INPUT"&&n.type==="radio"},vv=function(n,o){return o.filter(nh).filter(function(l){return l.name===n.name}).filter(function(l){return l.checked})[0]||n},Ku=function(n,o){return nh(n)&&n.name?vv(n,o):n},wv=function(n){const o=new Set;return n.forEach(function(l){return o.add(Ku(l,n))}),n.filter(function(l){return o.has(l)})},Mf=function(n){return n[0]&&n.length>1?Ku(n[0],n):n[0]},Bf=function(n,o){return n.length>1?n.indexOf(Ku(n[o],n)):o},rh="NEW_FOCUS",Ev=function(n,o,l,a){const c=n.length,d=n[0],f=n[c-1],y=Zu(l);if(n.indexOf(l)>=0)return;const h=o.indexOf(l),m=a?o.indexOf(a):h,S=a?n.indexOf(a):-1,k=h-m,C=o.indexOf(d),A=o.indexOf(f),b=wv(o),v=b.indexOf(l)-(a?b.indexOf(a):h),w=Bf(n,0),I=Bf(n,c-1);if(h===-1||S===-1)return rh;if(!k&&S>=0)return S;if(h<=C&&y&&Math.abs(k)>1)return I;if(h>=A&&y&&Math.abs(k)>1)return w;if(k&&Math.abs(v)>1)return S;if(h<=C)return I;if(h>A)return w;if(k)return Math.abs(k)>1?S:(c+S+k)%c},xv=function(n){return function(o){return o.autofocus||o.dataset&&!!o.dataset.autofocus||n.indexOf(o)>=0}},kv=function(n,o){const l=new Map;return o.forEach(function(a){return l.set(a.node,a)}),n.map(function(a){return l.get(a)}).filter(av)},Sv=function(n,o){const l=document&&document.activeElement,a=Yu(n).filter(qi),c=eh(l||n,n,a),d=Df(a);let f=Au(a).filter(function(k){const C=k.node;return qi(C)});if(!f[0]&&(f=d,!f[0]))return;const y=Df([c]).map(function(k){return k.node}),h=kv(y,f),m=h.map(function(k){return k.node}),S=Ev(m,y,l,o);if(S===rh){const k=d.map(function(C){return C.node}).filter(xv(pv(a)));return{node:k&&k.length?Mf(k):Mf(m)}}return S===void 0?S:h[S]},bv=function(n){n.focus(),"contentWindow"in n&&n.contentWindow&&n.contentWindow.focus()};let su=0,uu=!1;const oh=function(n,o){const l=Sv(n,o);if(!uu&&l){if(su>2){console.error("FocusLock: focus-fighting detected. Only one focus management system could be active. See https://github.com/theKashey/focus-lock/#focus-fighting"),uu=!0,setTimeout(function(){uu=!1},1);return}su++,bv(l.node),su--}};function lh(n){const o=window,l=o.setImmediate;typeof l<"u"?l(n):setTimeout(n,1)}const _v=function(){return document&&document.activeElement===document.body},Cv=function(){return _v()||mv()};let go=null,ho=null,mo=null,xl=!1;const Ov=function(){return!0},Tv=function(o){return(go.whiteList||Ov)(o)},Av=function(o,l){mo={observerNode:o,portaledElement:l}},Rv=function(o){return mo&&mo.portaledElement===o};function zf(n,o,l,a){let c=null,d=n;do{const f=a[d];if(f.guard)f.node.dataset.focusAutoGuard&&(c=f);else if(f.lockItem){if(d!==n)return;c=null}else break}while((d+=l)!==o);c&&(c.node.tabIndex=0)}const Nv=function(o){return o&&"current"in o?o.current:o},Lv=function(o){return o?!!xl:xl==="meanwhile"},Qi=function(){let o=!1;if(go){const l=go,a=l.observed,c=l.persistentFocus,d=l.autoFocus,f=l.shards,y=l.crossFrame,h=a||mo&&mo.portaledElement,m=document&&document.activeElement;if(h){const S=[h].concat(f.map(Nv).filter(Boolean));if((!m||Tv(m))&&(c||Lv(y)||!Cv()||!ho&&d)&&(h&&!(th(S)||Rv(m))&&(document&&!ho&&m&&!d?(m.blur&&m.blur(),document.body.focus()):(o=oh(S,ho),mo={})),xl=!1,ho=document&&document.activeElement),document){const k=document&&document.activeElement,C=hv(S),A=C.map(function(b){return b.node}).indexOf(k);A>-1&&(C.filter(function(b){const v=b.guard,w=b.node;return v&&w.dataset.focusAutoGuard}).forEach(function(b){return b.node.removeAttribute("tabIndex")}),zf(A,C.length,1,C),zf(A,-1,-1,C))}}}return o},ih=function(o){Qi()&&o&&(o.stopPropagation(),o.preventDefault())},Xu=function(){return lh(Qi)},Fv=function(o){const l=o.target,a=o.currentTarget;a.contains(l)||Av(a,l)},jv=function(){return null};Ie.node.isRequired;const ah=function(){xl="just",setTimeout(function(){xl="meanwhile"},0)},Iv=function(){document.addEventListener("focusin",ih,!0),document.addEventListener("focusout",Xu),window.addEventListener("blur",ah)},Pv=function(){document.removeEventListener("focusin",ih,!0),document.removeEventListener("focusout",Xu),window.removeEventListener("blur",ah)};function Dv(n){return n.filter(function(o){return!o.disabled})}function Mv(n){const o=n.slice(-1)[0];o&&!go&&Iv();const l=go,a=l&&o&&o.id===l.id;go=o,l&&!a&&(l.onDeactivation(),n.filter(function(c){return c.id===l.id}).length||l.returnFocus(!o)),o?(ho=null,(!a||l.observed!==o.observed)&&o.onActivation(),Qi(),lh(Qi)):(Pv(),ho=null)}qp.assignSyncMedium(Fv);Qp.assignMedium(Xu);Xm.assignMedium(function(n){return n({moveFocusInside:oh,focusInside:th})});const Bv=rv(Dv,Mv)(jv),sh=N.forwardRef(function(o,l){return N.createElement(la,ku({sideCar:Bv,ref:l},o))}),uh=la.propTypes||{};uh.sideCar;const zv=Bm(uh,["sideCar"]);sh.propTypes=zv;/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var vo=function(){return vo=Object.assign||function(o){for(var l,a=1,c=arguments.length;a<c;a++){l=arguments[a];for(const d in l)Object.prototype.hasOwnProperty.call(l,d)&&(o[d]=l[d])}return o},vo.apply(this,arguments)};function Uv(n,o){const l={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&o.indexOf(a)<0&&(l[a]=n[a]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,a=Object.getOwnPropertySymbols(n);c<a.length;c++)o.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(n,a[c])&&(l[a[c]]=n[a[c]]);return l}const Vi="right-scroll-bar-position",Wi="width-before-scroll-bar",$v="with-scroll-bars-hidden",Hv="--removed-body-scroll-bar-size",ch=Yp(),cu=function(){},ia=N.forwardRef(function(n,o){const l=N.useRef(null),a=N.useState({onScrollCapture:cu,onWheelCapture:cu,onTouchMoveCapture:cu}),c=a[0],d=a[1],f=n.forwardProps,y=n.children,h=n.className,m=n.removeScrollBar,S=n.enabled,k=n.shards,C=n.sideCar,A=n.noIsolation,b=n.inert,v=n.allowPinchZoom,w=n.as,I=w===void 0?"div":w,j=Uv(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),B=C,q=Vp([l,o]),te=vo({},j,c);return N.createElement(N.Fragment,null,S&&N.createElement(B,{sideCar:ch,removeScrollBar:m,shards:k,noIsolation:A,inert:b,setCallbacks:d,allowPinchZoom:!!v,lockRef:l}),f?N.cloneElement(N.Children.only(y),vo({},te,{ref:q})):N.createElement(I,vo({},te,{className:h,ref:q}),y))});ia.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ia.classNames={fullWidth:Wi,zeroRight:Vi};const Vv=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Wv(){if(!document)return null;const n=document.createElement("style");n.type="text/css";const o=Vv();return o&&n.setAttribute("nonce",o),n}function Gv(n,o){n.styleSheet?n.styleSheet.cssText=o:n.appendChild(document.createTextNode(o))}function Yv(n){(document.head||document.getElementsByTagName("head")[0]).appendChild(n)}const Zv=function(){let n=0,o=null;return{add:function(l){n==0&&(o=Wv())&&(Gv(o,l),Yv(o)),n++},remove:function(){n--,!n&&o&&(o.parentNode&&o.parentNode.removeChild(o),o=null)}}},qv=function(){const n=Zv();return function(o){N.useEffect(function(){return n.add(o),function(){n.remove()}},[])}},dh=function(){const n=qv();return function(l){const a=l.styles;return n(a),null}},Qv={left:0,top:0,right:0,gap:0},du=function(n){return parseInt(n||"",10)||0},Kv=function(n){const o=window.getComputedStyle(document.body),l=o[n==="padding"?"paddingLeft":"marginLeft"],a=o[n==="padding"?"paddingTop":"marginTop"],c=o[n==="padding"?"paddingRight":"marginRight"];return[du(l),du(a),du(c)]},Uf=function(n){if(n===void 0&&(n="margin"),typeof window>"u")return Qv;const o=Kv(n),l=document.documentElement.clientWidth,a=window.innerWidth;return{left:o[0],top:o[1],right:o[2],gap:Math.max(0,a-l+o[2]-o[0])}},Xv=dh(),Jv=function(n,o,l,a){const c=n.left,d=n.top,f=n.right,y=n.gap;return l===void 0&&(l="margin"),`
  .`+$v+` {
   overflow: hidden `+a+`;
   padding-right: `+y+"px "+a+`;
  }
  body {
    overflow: hidden `+a+`;
    `+[o&&"position: relative "+a+";",l==="margin"&&`
    padding-left: `+c+`px;
    padding-top: `+d+`px;
    padding-right: `+f+`px;
    margin-left:0;
    margin-top:0;
    margin-right: `+y+"px "+a+`;
    `,l==="padding"&&"padding-right: "+y+"px "+a+";"].filter(Boolean).join("")+`
  }
  
  .`+Vi+` {
    right: `+y+"px "+a+`;
  }
  
  .`+Wi+` {
    margin-right: `+y+"px "+a+`;
  }
  
  .`+Vi+" ."+Vi+` {
    right: 0 `+a+`;
  }
  
  .`+Wi+" ."+Wi+` {
    margin-right: 0 `+a+`;
  }
  
  body {
    `+Hv+": "+y+`px;
  }
`},e0=function(n){const o=N.useState(Uf(n.gapMode)),l=o[0],a=o[1];N.useEffect(function(){a(Uf(n.gapMode))},[n.gapMode]);const c=n.noRelative,d=n.noImportant,f=n.gapMode,y=f===void 0?"margin":f;return N.createElement(Xv,{styles:Jv(l,!c,y,d?"":"!important")})},t0=function(n){const o=window.getComputedStyle(n);return o.overflowY!=="hidden"&&!(o.overflowY===o.overflowX&&o.overflowY==="visible")},n0=function(n){const o=window.getComputedStyle(n);return o.overflowX!=="hidden"&&!(o.overflowY===o.overflowX&&o.overflowX==="visible")},$f=function(n,o){let l=o;do{if(fh(n,l)){const c=ph(n,l),d=c[1],f=c[2];if(d>f)return!0}l=l.parentNode}while(l&&l!==document.body);return!1},r0=function(n){const o=n.scrollTop,l=n.scrollHeight,a=n.clientHeight;return[o,l,a]},o0=function(n){const o=n.scrollLeft,l=n.scrollWidth,a=n.clientWidth;return[o,l,a]};var fh=function(n,o){return n==="v"?t0(o):n0(o)},ph=function(n,o){return n==="v"?r0(o):o0(o)};const l0=function(n,o,l,a,c){const d=a;let f=l.target;const y=o.contains(f);let h=!1;const m=d>0;let S=0,k=0;do{const C=ph(n,f),A=C[0],b=C[1],v=C[2],w=b-v-A;(A||w)&&fh(n,f)&&(S+=w,k+=A),f=f.parentNode}while(!y&&f!==document.body||y&&(o.contains(f)||o===f));return(m&&S===0||!m&&k===0)&&(h=!0),h};let Nu=!1;if(typeof window<"u")try{const n=Object.defineProperty({},"passive",{get:function(){return Nu=!0,!0}});window.addEventListener("test",n,n),window.removeEventListener("test",n,n)}catch{Nu=!1}const co=Nu?{passive:!1}:!1,Ui=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},Hf=function(n){return[n.deltaX,n.deltaY]},Vf=function(n){return n&&"current"in n?n.current:n},i0=function(n,o){return n[0]===o[0]&&n[1]===o[1]},a0=function(n){return`
  .block-interactivity-`+n+` {pointer-events: none;}
  .allow-interactivity-`+n+` {pointer-events: all;}
`};let s0=0,fo=[];function u0(n){const o=N.useRef([]),l=N.useRef([0,0]),a=N.useRef(),c=N.useState(s0++)[0],d=N.useState(function(){return dh()})[0],f=N.useRef(n);N.useEffect(function(){f.current=n},[n]),N.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-"+c);const v=[n.lockRef.current].concat((n.shards||[]).map(Vf)).filter(Boolean);return v.forEach(function(w){return w.classList.add("allow-interactivity-"+c)}),function(){document.body.classList.remove("block-interactivity-"+c),v.forEach(function(w){return w.classList.remove("allow-interactivity-"+c)})}}},[n.inert,n.lockRef.current,n.shards]);const y=N.useCallback(function(v,w){if("touches"in v&&v.touches.length===2)return!f.current.allowPinchZoom;const I=Ui(v),j=l.current,B="deltaX"in v?v.deltaX:j[0]-I[0],q="deltaY"in v?v.deltaY:j[1]-I[1];let te;const X=v.target,Y=Math.abs(B)>Math.abs(q)?"h":"v";let J=$f(Y,X);if(!J)return!0;if(J?te=Y:(te=Y==="v"?"h":"v",J=$f(Y,X)),!J)return!1;if(!a.current&&"changedTouches"in v&&(B||q)&&(a.current=te),!te)return!0;const le=a.current||te;return l0(le,w,v,le==="h"?B:q)},[]),h=N.useCallback(function(v){const w=v;if(!fo.length||fo[fo.length-1]!==d)return;const I="deltaY"in w?Hf(w):Ui(w),j=o.current.filter(function(B){return B.name===w.type&&B.target===w.target&&i0(B.delta,I)})[0];if(j&&j.should){w.preventDefault();return}if(!j){const B=(f.current.shards||[]).map(Vf).filter(Boolean).filter(function(te){return te.contains(w.target)});(B.length>0?y(w,B[0]):!f.current.noIsolation)&&w.preventDefault()}},[]),m=N.useCallback(function(v,w,I,j){const B={name:v,delta:w,target:I,should:j};o.current.push(B),setTimeout(function(){o.current=o.current.filter(function(q){return q!==B})},1)},[]),S=N.useCallback(function(v){l.current=Ui(v),a.current=void 0},[]),k=N.useCallback(function(v){m(v.type,Hf(v),v.target,y(v,n.lockRef.current))},[]),C=N.useCallback(function(v){m(v.type,Ui(v),v.target,y(v,n.lockRef.current))},[]);N.useEffect(function(){return fo.push(d),n.setCallbacks({onScrollCapture:k,onWheelCapture:k,onTouchMoveCapture:C}),document.addEventListener("wheel",h,co),document.addEventListener("touchmove",h,co),document.addEventListener("touchstart",S,co),function(){fo=fo.filter(function(v){return v!==d}),document.removeEventListener("wheel",h,co),document.removeEventListener("touchmove",h,co),document.removeEventListener("touchstart",S,co)}},[]);const A=n.removeScrollBar,b=n.inert;return N.createElement(N.Fragment,null,b?N.createElement(d,{styles:a0(c)}):null,A?N.createElement(e0,{gapMode:"margin"}):null)}const c0=Km(ch,u0),hh=N.forwardRef(function(n,o){return N.createElement(ia,vo({},n,{ref:o,sideCar:c0}))});hh.classNames=ia.classNames;function cn(){return cn=Object.assign||function(n){for(let o=1;o<arguments.length;o++){const l=arguments[o];for(const a in l)Object.prototype.hasOwnProperty.call(l,a)&&(n[a]=l[a])}return n},cn.apply(this,arguments)}function aa(n,o){if(n==null)return{};const l={},a=Object.keys(n);let c,d;for(d=0;d<a.length;d++)c=a[d],!(o.indexOf(c)>=0)&&(l[c]=n[c]);return l}const yh={allowPinchZoom:Ie.bool,dangerouslyBypassFocusLock:Ie.bool,dangerouslyBypassScrollLock:Ie.bool,initialFocusRef:function(){return null},onDismiss:Ie.func},Eo=oa(function(o,l){const a=o.as,c=a===void 0?"div":a,d=o.isOpen,f=d===void 0?!0:d,y=aa(o,["as","isOpen"]);return Bp("dialog"),N.useEffect(function(){f?window.__REACH_DISABLE_TOOLTIPS=!0:window.requestAnimationFrame(function(){window.__REACH_DISABLE_TOOLTIPS=!1})},[f]),f?N.createElement(zp,{"data-reach-dialog-wrapper":""},N.createElement(d0,cn({ref:l,as:c},y))):null});Eo.displayName="DialogOverlay",Eo.propTypes=cn({},yh,{isOpen:Ie.bool});var d0=oa(function(o,l){const a=o.allowPinchZoom,c=o.as,d=c===void 0?"div":c,f=o.dangerouslyBypassFocusLock,y=f===void 0?!1:f,h=o.dangerouslyBypassScrollLock,m=h===void 0?!1:h,S=o.initialFocusRef,k=o.onClick,C=o.onDismiss,A=C===void 0?Sl:C,b=o.onKeyDown,v=o.onMouseDown,w=o.unstable_lockFocusAcrossFrames,I=w===void 0?!0:w,j=aa(o,["allowPinchZoom","as","dangerouslyBypassFocusLock","dangerouslyBypassScrollLock","initialFocusRef","onClick","onDismiss","onKeyDown","onMouseDown","unstable_lockFocusAcrossFrames"]),B=N.useRef(null),q=N.useRef(null),te=Mm(q,l),X=N.useCallback(function(){S&&S.current&&S.current.focus()},[S]);function Y(ge){B.current===ge.target&&(ge.stopPropagation(),A(ge))}function J(ge){ge.key==="Escape"&&(ge.stopPropagation(),A(ge))}function le(ge){B.current=ge.target}return N.useEffect(function(){return q.current?f0(q.current):void 0},[]),N.createElement(sh,{autoFocus:!0,returnFocus:!0,onActivation:X,disabled:y,crossFrame:I},N.createElement(hh,{allowPinchZoom:a,enabled:!m},N.createElement(d,cn({},j,{ref:te,"data-reach-dialog-overlay":"",onClick:Hi(k,Y),onKeyDown:Hi(b,J),onMouseDown:Hi(v,le)}))))});Eo.displayName="DialogOverlay",Eo.propTypes=cn({},yh);const Ki=oa(function(o,l){const a=o.as,c=a===void 0?"div":a,d=o.onClick;o.onKeyDown;const f=aa(o,["as","onClick","onKeyDown"]);return N.createElement(c,cn({"aria-modal":"true",role:"dialog",tabIndex:-1},f,{ref:l,"data-reach-dialog-content":"",onClick:Hi(d,function(y){y.stopPropagation()})}))});Ki.displayName="DialogContent",Ki.propTypes={"aria-label":Xi,"aria-labelledby":Xi};const Wf=oa(function(o,l){const a=o.allowPinchZoom,c=a===void 0?!1:a,d=o.initialFocusRef,f=o.isOpen,y=o.onDismiss,h=y===void 0?Sl:y,m=aa(o,["allowPinchZoom","initialFocusRef","isOpen","onDismiss"]);return N.createElement(Eo,{allowPinchZoom:c,initialFocusRef:d,isOpen:f,onDismiss:h},N.createElement(Ki,cn({ref:l},m)))});Wf.displayName="Dialog",Wf.propTypes={isOpen:Ie.bool,onDismiss:Ie.func,"aria-label":Xi,"aria-labelledby":Xi};function f0(n){const o=[],l=[],a=jm(n);return n?(Array.prototype.forEach.call(a.querySelectorAll("body > *"),function(c){let d,f;const y=(d=n.parentNode)==null||(f=d.parentNode)==null?void 0:f.parentNode;if(c===y)return;const h=c.getAttribute("aria-hidden");h!==null&&h!=="false"||(o.push(h),l.push(c),c.setAttribute("aria-hidden","true"))}),function(){l.forEach(function(c,d){const f=o[d];f===null?c.removeAttribute("aria-hidden"):c.setAttribute("aria-hidden",f)})}):(console.warn("A ref has not yet been attached to a dialog node when attempting to call `createAriaHider`."),Sl)}function Xi(n,o,l,a,c){const d=`
See https://www.w3.org/TR/wai-aria/#aria-label for details.`;return!n["aria-label"]&&!n["aria-labelledby"]?new Error("A <"+l+"> must have either an `aria-label` or `aria-labelledby` prop.\n      "+d):n["aria-label"]&&n["aria-labelledby"]?new Error("You provided both `aria-label` and `aria-labelledby` props to a <"+l+">. If the a label for this component is visible on the screen, that label's component should be given a unique ID prop, and that ID should be passed as the `aria-labelledby` prop into <"+l+">. If the label cannot be determined programmatically from the content of the element, an alternative label should be provided as the `aria-label` prop, which will be used as an `aria-label` on the HTML tag."+d):n[o]!=null&&!Pm(n[o])?new Error("Invalid prop `"+o+"` supplied to `"+l+"`. Expected `string`, received `"+(Array.isArray(c)?"array":typeof c)+"`."):null}const p0=({children:n,onClick:o,style:l,...a})=>g.jsx("button",{className:"ladle-button",onClick:o,style:l,"aria-label":a["aria-label"],type:"button",children:n}),wo=({children:n,href:o,style:l})=>g.jsx("a",{className:"ladle-link",href:o,style:l,children:n}),Or=({children:n})=>g.jsx("code",{className:"ladle-code",children:n}),So=({children:n,close:o,isOpen:l,label:a,maxWidth:c="40em"})=>g.jsx(Eo,{isOpen:l,onDismiss:()=>o(),"data-testid":"ladle-dialog-overlay",children:g.jsxs(Ki,{"aria-label":a||"Modal","data-testid":"ladle-dialog",style:{maxWidth:c},children:[g.jsx("div",{style:{position:"absolute",insetInlineEnd:"-6px",top:"0px"},children:g.jsx(p0,{onClick:()=>o(),"aria-label":"Close modal",style:{height:"36px",width:"36px",borderColor:"transparent",boxShadow:"none"},children:g.jsx(hm,{})})}),g.jsx("div",{className:"ladle-addon-modal-body",children:n})]})});var ht;(function(n){n.Full="full",n.Preview="preview"})(ht||(ht={}));var Ze;(function(n){n.Light="light",n.Dark="dark",n.Auto="auto"})(Ze||(Ze={}));var Te;(function(n){n.Boolean="boolean",n.String="string",n.Number="number",n.Complex="complex",n.Function="function",n.Radio="radio",n.InlineRadio="inline-radio",n.Select="select",n.MultiSelect="multi-select",n.Check="check",n.InlineCheck="inline-check",n.Action="action",n.Range="range",n.Background="background"})(Te||(Te={}));var Re;(function(n){n.UpdateAll="update-all",n.UpdateMode="update-mode",n.UpdateAction="update-action",n.UpdateRtl="update-rtl",n.UpdateSource="update-source",n.UpdateStory="update-story",n.UpdateTheme="update-theme",n.UpdateWidth="update-width",n.UpdateControl="update-control",n.UpdateControlIntialized="update-control-initialized",n.UpdateHotkeys="update-hotkeys"})(Re||(Re={}));const Gf=n=>{switch(n){case Te.Boolean:return"checkbox";case Te.Number:return"number";case Te.Range:return"range";default:return"text"}},Yf=(n,o)=>{switch(o){case Te.Boolean:return n.checked;case Te.Number:case Te.Range:return parseFloat(n.value);default:return n.value}},Ji=(n,o)=>o&&o.some(a=>a===Number(n))?Number(n):n==="true"||n==="false"?n!=="false":n,h0=(n,o)=>{const l=dn.parse(n),a={};return Object.keys(o).length===0?o:(Object.keys(l).forEach(c=>{if(c.startsWith("arg-")&&o[c.split("-")[1]]){const f=c.split("-")[1],y=l[c],h=o[f].type;if(h!==Te.Action){let m=y;switch(h){case Te.String:m=decodeURI(y);break;case Te.Boolean:m=y==="true";break;case Te.Range:m=parseFloat(y);break;case Te.Number:m=parseInt(y,10);break;case Te.Complex:m=JSON.parse(decodeURI(y));break;case Te.Radio:case Te.InlineRadio:case Te.Select:case Te.Background:m=Ji(decodeURI(y),o[f].options);break;case Te.InlineCheck:case Te.MultiSelect:case Te.Check:m=Ji(JSON.parse(decodeURI(y)),o[f].options);break}a[f]={value:m,defaultValue:o[f].defaultValue,description:o[f].description,type:o[f].type}}}}),a)},y0=({controlKey:n,globalState:o,dispatch:l})=>{const a=o.control[n],c=o.control[n].name||n;if(o.control[n].type===Te.Action)return g.jsxs("tr",{children:[g.jsx("td",{children:c}),g.jsx("td",{children:"action"})]});if(o.control[n].type===Te.Function)return g.jsxs("tr",{children:[g.jsx("td",{children:c}),g.jsx("td",{children:"function"})]});if(o.control[n].type===Te.Radio||o.control[n].type===Te.InlineRadio||o.control[n].type===Te.Background&&o.control[n].options.length<5)return g.jsxs("tr",{children:[g.jsx("td",{children:c}),g.jsx("td",{style:o.control[n].type===Te.InlineRadio?{display:"flex"}:{},children:(o.control[n].options||[]).map(d=>{const f=o.control[n].value,h=(o.control[n].labels||{})[d]||d,m=f===d||f===String(d);return g.jsxs("div",{style:{display:"flex",alignItems:"center",...o.control[n].type===Te.InlineRadio?{paddingRight:"0.5em"}:{}},children:[g.jsx("input",{id:`${n}-${String(d)}`,type:"radio",name:n,value:String(d),onChange:()=>{l({type:Re.UpdateControl,value:{...o.control,[n]:{...o.control[n],value:Ji(String(d),o.control[n].options)}}})},checked:m}),g.jsx("label",{htmlFor:`${n}-${String(d)}`,children:String(h)})]},`${String(d)}-${n}`)})})]});if(o.control[n].type===Te.Check||o.control[n].type===Te.InlineCheck||o.control[n].type===Te.MultiSelect)return g.jsxs("tr",{children:[g.jsx("td",{children:c}),g.jsx("td",{style:o.control[n].type===Te.InlineCheck?{display:"flex"}:{},children:(o.control[n].options||[]).map(d=>{const f=new Set(o.control[n].value),h=(o.control[n].labels||{})[d]||d;return g.jsxs("div",{style:{display:"flex",alignItems:"center",...o.control[n].type===Te.InlineCheck?{paddingRight:"0.5em"}:{}},children:[g.jsx("input",{id:`${n}-${String(d)}`,type:"checkbox",name:`${n}-${String(d)}`,value:String(d),checked:f.has(String(d)),onChange:()=>{const m=String(d);f.has(m)?f.delete(m):f.add(m),l({type:Re.UpdateControl,value:{...o.control,[n]:{...o.control[n],value:f.size>0?Array.from(f):void 0}}})}}),g.jsx("label",{htmlFor:`${n}-${String(d)}`,style:{marginLeft:"0.3em"},children:String(h)})]},`${String(d)}-${n}`)})})]});if(o.control[n].type===Te.Select||o.control[n].type===Te.Background)return g.jsxs("tr",{children:[g.jsx("td",{children:g.jsx("label",{htmlFor:n,children:c})}),g.jsx("td",{children:g.jsxs("select",{id:n,value:String(o.control[n].value),onChange:d=>{const f=o.control[n].labels||{},y=Object.keys(f).find(h=>f[h]===d.target.value)||d.target.value;l({type:Re.UpdateControl,value:{...o.control,[n]:{...o.control[n],value:Ji(y,o.control[n].options)}}})},children:[g.jsx("option",{value:"undefined",disabled:!0,children:"Choose option..."}),(o.control[n].options||[]).map(d=>{const y=(o.control[n].labels||{})[d]||d;return g.jsx("option",{children:String(y)},`${d}-${n}`)})]})})]});if(o.control[n].type===Te.Complex){let d="";try{d=JSON.stringify(o.control[n].value)}catch{d="Object/Array argument must be serializable."}return g.jsxs("tr",{children:[g.jsx("td",{children:g.jsx("label",{htmlFor:n,children:c})}),g.jsx("td",{children:g.jsx("textarea",{id:n,defaultValue:d,onChange:f=>{let y=o.control[n].value;try{y=JSON.parse(f.target.value)}catch{}l({type:Re.UpdateControl,value:{...o.control,[n]:{...o.control[n],value:y}}})}})})]})}if(a.type===Te.Range){const d=a.min??0,f=a.max??100;return g.jsxs("tr",{children:[g.jsx("td",{children:g.jsx("label",{htmlFor:n,children:c})}),g.jsxs("td",{children:[d,g.jsx("input",{id:n,type:Gf(a.type),value:a.value,min:a.min,max:a.max,step:a.step,onChange:y=>l({type:Re.UpdateControl,value:{...o.control,[n]:{...a,value:Yf(y.target,a.type)}}})}),a.value," / ",f]})]})}return g.jsxs("tr",{children:[g.jsx("td",{children:g.jsx("label",{htmlFor:n,children:c})}),g.jsx("td",{children:g.jsx("input",{id:n,type:Gf(o.control[n].type),value:o.control[n].value,checked:o.control[n].type===Te.Boolean&&o.control[n].value===!0,onChange:d=>l({type:Re.UpdateControl,value:{...o.control,[n]:{...o.control[n],value:Yf(d.target,o.control[n].type)}}})})})]})},g0=({globalState:n,dispatch:o})=>{const[l,a]=N.useState(!1);$t(he.hotkeys.control,()=>a(f=>!f),{enabled:n.hotkeys&&he.addons.control.enabled});const c="Explore different versions of this story through controls.",d=Object.keys(n.control).filter(f=>JSON.stringify(n.control[f].value)!==JSON.stringify(n.control[f].defaultValue));return g.jsx("li",{children:g.jsxs("button",{"aria-label":c,title:c,onClick:()=>a(!0),className:l?"ladle-active":"","data-testid":"addon-control",type:"button",children:[g.jsx(km,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:c}),g.jsx("label",{children:"Story Controls"}),d.length?g.jsx("div",{className:"ladle-badge",children:d.length}):null,g.jsxs(So,{isOpen:l,close:()=>a(!1),label:"Toggle different controls to update the story.",children:[g.jsx("table",{className:"ladle-controls-table",children:g.jsx("tbody",{children:Object.keys(n.control).sort().map(f=>g.jsx(y0,{globalState:n,dispatch:o,controlKey:f},f))})}),g.jsx("button",{onClick:()=>{const f={};Object.keys(n.control).forEach(y=>{f[y]={...n.control[y],value:n.control[y].defaultValue}}),o({type:Re.UpdateControl,value:f})},type:"button",children:"Reset to defaults"})]})]})})},m0="modulepreload",v0=function(n){return"/"+n},Zf={},w0=function(o,l,a){let c=Promise.resolve();if(l&&l.length>0){let f=function(m){return Promise.all(m.map(S=>Promise.resolve(S).then(k=>({status:"fulfilled",value:k}),k=>({status:"rejected",reason:k}))))};document.getElementsByTagName("link");const y=document.querySelector("meta[property=csp-nonce]"),h=(y==null?void 0:y.nonce)||(y==null?void 0:y.getAttribute("nonce"));c=f(l.map(m=>{if(m=v0(m),m in Zf)return;Zf[m]=!0;const S=m.endsWith(".css"),k=S?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${k}`))return;const C=document.createElement("link");if(C.rel=S?"stylesheet":m0,S||(C.as="script"),C.crossOrigin="",C.href=m,h&&C.setAttribute("nonce",h),document.head.appendChild(C),S)return new Promise((A,b)=>{C.addEventListener("load",A),C.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${m}`)))})}))}function d(f){const y=new Event("vite:preloadError",{cancelable:!0});if(y.payload=f,window.dispatchEvent(y),!y.defaultPrevented)throw f}return c.then(f=>{for(const y of f||[])y.status==="rejected"&&d(y.reason);return o().catch(d)})};let xo={},Ut={stories:"src/**/*.stories.{js,jsx,ts,tsx}",addons:{control:{enabled:!0,defaultState:{}},theme:{enabled:!0,defaultState:"light"},mode:{enabled:!0,defaultState:"full"},rtl:{enabled:!0,defaultState:!1},source:{enabled:!0,defaultState:!1},a11y:{enabled:!1},msw:{enabled:!1},action:{enabled:!0,defaultState:[]},ladle:{enabled:!0},width:{enabled:!0,options:{xsmall:414,small:640,medium:768,large:1024},defaultState:0}},hotkeys:{search:["/","meta+p"],nextStory:["alt+arrowright"],previousStory:["alt+arrowleft"],nextComponent:["alt+arrowdown"],previousComponent:["alt+arrowup"],control:["c"],darkMode:["d"],fullscreen:["f"],width:["w"],rtl:["r"],source:["s"],a11y:["a"]},i18n:{buildTooltip:'💡 Tip: Run "ladle preview" to check that the build works!'},storyOrder:"(stories) => stories"};const E0=({children:n})=>N.createElement(N.Fragment,null,n),x0=({path:n})=>N.createElement("div",{style:{paddingTop:"2em"}},N.createElement("code",{className:"ladle-code"},n));let k0={};const gh={stories:"src/**/*.stories.{js,jsx,ts,tsx,mdx}",defaultStory:"",storyOrder:n=>n,viteConfig:void 0,appendToHead:"",disableHttp2:!1,noWatch:!1,port:61e3,previewPort:8080,hmrHost:void 0,hmrPort:void 0,outDir:"build",base:void 0,expandStoryTree:!1,hotkeys:{search:["/","meta+p"],nextStory:["alt+arrowright"],previousStory:["alt+arrowleft"],nextComponent:["alt+arrowdown"],previousComponent:["alt+arrowup"],control:["c"],darkMode:["d"],fullscreen:["f"],width:["w"],rtl:["r"],source:["s"],a11y:["a"]},onDevServerStart:()=>{},i18n:{buildTooltip:'💡 Tip: Run "ladle preview" to check that the build works!'},addons:{control:{enabled:!0,defaultState:{}},theme:{enabled:!0,defaultState:"light"},mode:{enabled:!0,defaultState:"full"},rtl:{enabled:!0,defaultState:!1},source:{enabled:!0,defaultState:!1},a11y:{enabled:!1},msw:{enabled:!1},action:{enabled:!0,defaultState:[]},ladle:{enabled:!0},width:{enabled:!0,options:{xsmall:414,small:640,medium:768,large:1024},defaultState:0}}};Object.keys(Ut).length===0?sn("No custom config found."):(Ut.storyOrder&&typeof Ut.storyOrder=="string"&&(Ut.storyOrder=new Function("return "+Ut.storyOrder)()),sn("Custom config found:"),sn(Ut));var yp,gp;(gp=(yp=Ut==null?void 0:Ut.addons)==null?void 0:yp.width)!=null&&gp.options&&(gh.addons.width.options={});const he=Zg(gh,Ut);he.defaultStory===""&&(he.defaultStory=Ap(Object.keys(xo),he.storyOrder)[0]);he.hotkeys={...he.hotkeys,...Ut.hotkeys};sn("Final config",he);const mh=n=>{switch(dn.parse(n).theme){case Ze.Light:return Ze.Light;case Ze.Dark:return Ze.Dark;case Ze.Auto:return Ze.Auto;default:return"light"}},S0=({globalState:n,dispatch:o})=>{const l="Switch to dark theme.",a="Switch to light theme.",c=()=>{const d=n.theme===Ze.Light?Ze.Dark:Ze.Light;document.documentElement.setAttribute("data-theme",d),o({type:Re.UpdateTheme,value:d})};return $t(he.hotkeys.darkMode,c,{enabled:n.hotkeys&&he.addons.mode.enabled}),g.jsx("li",{children:g.jsxs("button",{"aria-label":n.theme===Ze.Light?l:a,title:n.theme===Ze.Light?l:a,onClick:c,type:"button",children:[g.jsx(wm,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:n.theme===Ze.Light?l:a}),g.jsxs("label",{children:["Switch to"," ",n.theme===Ze.Light?Ze.Dark:Ze.Light," ","theme"]})]})})},vh=Tp(Cp(location.search,he.defaultStory));sn(`Initial document.title: ${vh}`);document.title=`${vh} | Ladle`;const Lu=mh(location.search);sn(`Initial theme state: ${Lu}`);Lu===Ze.Auto?window.matchMedia("(prefers-color-scheme: dark)").matches?document.documentElement.setAttribute("data-theme",Ze.Dark):document.documentElement.setAttribute("data-theme",Ze.Light):document.documentElement.setAttribute("data-theme",Lu);var $i={},qf;function b0(){if(qf)return $i;qf=1;var n=Ip();return $i.createRoot=n.createRoot,$i.hydrateRoot=n.hydrateRoot,$i}var _0=b0();const Fu={},wh=$.createContext(Fu);function C0(n){const o=$.useContext(wh);return $.useMemo(function(){return typeof n=="function"?n(o):{...o,...n}},[o,n])}function O0(n){let o;return n.disableParentContext?o=typeof n.components=="function"?n.components(Fu):n.components||Fu:o=C0(n.components),$.createElement(wh.Provider,{value:o},n.children)}let Eh,xh;typeof document<"u"&&(Eh=document);typeof window<"u"&&(xh=window);const kh=$.createContext({document:Eh,window:xh}),T0=()=>N.useContext(kh),{Provider:A0,Consumer:uE}=kh;class R0 extends N.Component{componentDidMount(){this.props.contentDidMount&&this.props.contentDidMount()}componentDidUpdate(){this.props.contentDidUpdate&&this.props.contentDidUpdate()}render(){return $.Children.only(this.props.children)}}class Sh extends N.Component{constructor(l){super(l);uo(this,"_isMounted",!1);uo(this,"nodeRef",$.createRef());uo(this,"setRef",l=>{this.nodeRef.current=l;const{forwardedRef:a}=this.props;typeof a=="function"?a(l):a&&(a.current=l)});uo(this,"handleLoad",()=>{this.setState({iframeLoaded:!0})});this.state={iframeLoaded:!1}}componentDidMount(){var a;this._isMounted=!0;const l=this.getDoc();l&&l.readyState==="complete"?this.forceUpdate():(a=this.nodeRef.current)==null||a.addEventListener("load",this.handleLoad)}componentWillUnmount(){var l;this._isMounted=!1,(l=this.nodeRef.current)==null||l.removeEventListener("load",this.handleLoad)}getDoc(){return this.nodeRef.current?this.nodeRef.current.contentDocument:null}getMountTarget(){const l=this.getDoc();return this.props.mountTarget?l==null?void 0:l.querySelector(this.props.mountTarget):l==null?void 0:l.body.children[0]}renderFrameContents(){if(!this._isMounted)return null;const l=this.getDoc();if(!l)return null;const a=this.props.contentDidMount,c=this.props.contentDidUpdate,d=l.defaultView||l.parentView,f=g.jsx(R0,{contentDidMount:a,contentDidUpdate:c,children:g.jsx(A0,{value:{document:l,window:d},children:g.jsx("div",{className:"frame-content",children:this.props.children})})}),y=this.getMountTarget();return[jf.createPortal(this.props.head,this.getDoc().head),jf.createPortal(f,y)]}render(){const l={...this.props,srcDoc:this.props.initialContent,children:void 0};return delete l.head,delete l.initialContent,delete l.mountTarget,delete l.contentDidMount,delete l.contentDidUpdate,delete l.forwardedRef,g.jsx("iframe",{...l,ref:this.setRef,onLoad:this.handleLoad,children:this.state.iframeLoaded&&this.renderFrameContents()})}}uo(Sh,"defaultProps",{style:{},head:null,children:void 0,mountTarget:void 0,contentDidMount:()=>{},contentDidUpdate:()=>{},initialContent:'<!DOCTYPE html><html><head></head><body><div class="frame-root"></div></body></html>'});const N0=$.forwardRef((n,o)=>g.jsx(Sh,{...n,forwardedRef:o})),L0=n=>n.altKey||n.ctrlKey||n.shiftKey||n.metaKey,bh=n=>{const o=n.target||{};return!!(!n.key||o.isContentEditable||["INPUT","TEXTAREA"].includes(o.nodeName)&&!L0(n))},Qf=n=>{if(bh(n))return;const o=new KeyboardEvent("keydown",{key:n.key,code:n.code,keyCode:n.keyCode,altKey:n.altKey,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,metaKey:n.metaKey});document.dispatchEvent(o)},Kf=n=>{if(bh(n))return;const o=new KeyboardEvent("keyup",{key:n.key,code:n.code,keyCode:n.keyCode,altKey:n.altKey,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,metaKey:n.metaKey});document.dispatchEvent(o)},ju="data-debug-css";function Xf(n,o,l){const a=o.head.querySelectorAll(`style[${ju}]`),c=o.createElement("style");return c.setAttribute(ju,"true"),c.appendChild(o.createTextNode(n)),l&&a[l]?(a[l].after(c),l+1):(o.head.appendChild(c),a.length)}function Jf(n,o){const l=n.head.querySelectorAll(`style[${ju}]`);o!=null&&l[o]&&l[o].remove()}const F0=({active:n,children:o,rtl:l,width:a})=>{const{window:c,document:d}=T0(),f=()=>{c&&(c.document.documentElement.setAttribute("dir",l?"rtl":"ltr"),[...document.head.children].forEach(y=>{if(y.tagName==="STYLE"||y.tagName==="LINK"&&(y.getAttribute("type")==="text/css"||y.getAttribute("rel")==="stylesheet")){if([...c.document.head.children].some(m=>m.tagName==="LINK"?m.getAttribute("href")===y.getAttribute("href"):m.tagName==="STYLE"?m.innerHTML===y.innerHTML:!1))return;c.document.head.appendChild(y.cloneNode(!0))}}))};return N.useEffect(()=>{const y=window.CSSStyleSheet.prototype.insertRule,h=window.CSSStyleSheet.prototype.deleteRule;return window.CSSStyleSheet.prototype.insertRule=function(m,S){const k=Xf(m,document,S);return n&&d?Xf(m,d,S):k},window.CSSStyleSheet.prototype.deleteRule=function(m){Jf(document,m),n&&d&&Jf(d,m)},()=>{window.CSSStyleSheet.prototype.insertRule=y,window.CSSStyleSheet.prototype.deleteRule=h}},[]),N.useEffect(()=>{if(n){f(),d==null||d.addEventListener("keydown",Qf),d==null||d.addEventListener("keyup",Kf);const y=new MutationObserver(()=>f());return document.documentElement.setAttribute("data-iframed",`${a}`),y.observe(document.head,{subtree:!0,characterData:!0,childList:!0}),()=>{y&&y.disconnect(),d==null||d.removeEventListener("keydown",Qf),d==null||d.removeEventListener("keyup",Kf)}}},[n,l,d]),o};class j0 extends N.Component{constructor(o){super(o),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(){}render(){return this.state.hasError?null:this.props.children}}const I0=({activeStory:n})=>g.jsxs("div",{className:"ladle-error-content",children:[g.jsx("h1",{children:"Story not found"}),g.jsxs("p",{children:["The story id ",g.jsx(Or,{children:n})," you are trying to open does not exist. Typo?"]}),g.jsx("p",{children:g.jsx(wo,{href:"/",children:"Back to home"})}),g.jsx("p",{children:g.jsx(wo,{href:"https://github.com/tajo/ladle",children:"GitHub"})}),g.jsx("p",{children:g.jsx(wo,{href:"https://www.ladle.dev",children:"Docs"})})]});function _h(n){var o,l,a="";if(typeof n=="string"||typeof n=="number")a+=n;else if(typeof n=="object")if(Array.isArray(n)){var c=n.length;for(o=0;o<c;o++)n[o]&&(l=_h(n[o]))&&(a&&(a+=" "),a+=l)}else for(l in n)n[l]&&(a&&(a+=" "),a+=l);return a}function Ch(){for(var n,o,l=0,a="",c=arguments.length;l<c;l++)(n=arguments[l])&&(o=_h(n))&&(a&&(a+=" "),a+=o);return a}var P0=Object.create,sa=Object.defineProperty,D0=Object.defineProperties,M0=Object.getOwnPropertyDescriptor,B0=Object.getOwnPropertyDescriptors,Oh=Object.getOwnPropertyNames,ea=Object.getOwnPropertySymbols,z0=Object.getPrototypeOf,Ju=Object.prototype.hasOwnProperty,Th=Object.prototype.propertyIsEnumerable,ep=(n,o,l)=>o in n?sa(n,o,{enumerable:!0,configurable:!0,writable:!0,value:l}):n[o]=l,un=(n,o)=>{for(var l in o||(o={}))Ju.call(o,l)&&ep(n,l,o[l]);if(ea)for(var l of ea(o))Th.call(o,l)&&ep(n,l,o[l]);return n},ua=(n,o)=>D0(n,B0(o)),Ah=(n,o)=>{var l={};for(var a in n)Ju.call(n,a)&&o.indexOf(a)<0&&(l[a]=n[a]);if(n!=null&&ea)for(var a of ea(n))o.indexOf(a)<0&&Th.call(n,a)&&(l[a]=n[a]);return l},U0=(n,o)=>function(){return o||(0,n[Oh(n)[0]])((o={exports:{}}).exports,o),o.exports},$0=(n,o)=>{for(var l in o)sa(n,l,{get:o[l],enumerable:!0})},H0=(n,o,l,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let c of Oh(o))!Ju.call(n,c)&&c!==l&&sa(n,c,{get:()=>o[c],enumerable:!(a=M0(o,c))||a.enumerable});return n},V0=(n,o,l)=>(l=n!=null?P0(z0(n)):{},H0(!n||!n.__esModule?sa(l,"default",{value:n,enumerable:!0}):l,n)),W0=U0({"../../node_modules/.pnpm/prismjs@1.29.0_patch_hash=vrxx3pzkik6jpmgpayxfjunetu/node_modules/prismjs/prism.js"(n,o){var l=function(){var a=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,c=0,d={},f={util:{encode:function b(v){return v instanceof y?new y(v.type,b(v.content),v.alias):Array.isArray(v)?v.map(b):v.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(b){return Object.prototype.toString.call(b).slice(8,-1)},objId:function(b){return b.__id||Object.defineProperty(b,"__id",{value:++c}),b.__id},clone:function b(v,w){w=w||{};var I,j;switch(f.util.type(v)){case"Object":if(j=f.util.objId(v),w[j])return w[j];I={},w[j]=I;for(var B in v)v.hasOwnProperty(B)&&(I[B]=b(v[B],w));return I;case"Array":return j=f.util.objId(v),w[j]?w[j]:(I=[],w[j]=I,v.forEach(function(q,te){I[te]=b(q,w)}),I);default:return v}},getLanguage:function(b){for(;b;){var v=a.exec(b.className);if(v)return v[1].toLowerCase();b=b.parentElement}return"none"},setLanguage:function(b,v){b.className=b.className.replace(RegExp(a,"gi"),""),b.classList.add("language-"+v)},isActive:function(b,v,w){for(var I="no-"+v;b;){var j=b.classList;if(j.contains(v))return!0;if(j.contains(I))return!1;b=b.parentElement}return!!w}},languages:{plain:d,plaintext:d,text:d,txt:d,extend:function(b,v){var w=f.util.clone(f.languages[b]);for(var I in v)w[I]=v[I];return w},insertBefore:function(b,v,w,I){I=I||f.languages;var j=I[b],B={};for(var q in j)if(j.hasOwnProperty(q)){if(q==v)for(var te in w)w.hasOwnProperty(te)&&(B[te]=w[te]);w.hasOwnProperty(q)||(B[q]=j[q])}var X=I[b];return I[b]=B,f.languages.DFS(f.languages,function(Y,J){J===X&&Y!=b&&(this[Y]=B)}),B},DFS:function b(v,w,I,j){j=j||{};var B=f.util.objId;for(var q in v)if(v.hasOwnProperty(q)){w.call(v,q,v[q],I||q);var te=v[q],X=f.util.type(te);X==="Object"&&!j[B(te)]?(j[B(te)]=!0,b(te,w,null,j)):X==="Array"&&!j[B(te)]&&(j[B(te)]=!0,b(te,w,q,j))}}},plugins:{},highlight:function(b,v,w){var I={code:b,grammar:v,language:w};if(f.hooks.run("before-tokenize",I),!I.grammar)throw new Error('The language "'+I.language+'" has no grammar.');return I.tokens=f.tokenize(I.code,I.grammar),f.hooks.run("after-tokenize",I),y.stringify(f.util.encode(I.tokens),I.language)},tokenize:function(b,v){var w=v.rest;if(w){for(var I in w)v[I]=w[I];delete v.rest}var j=new S;return k(j,j.head,b),m(b,j,v,j.head,0),A(j)},hooks:{all:{},add:function(b,v){var w=f.hooks.all;w[b]=w[b]||[],w[b].push(v)},run:function(b,v){var w=f.hooks.all[b];if(!(!w||!w.length))for(var I=0,j;j=w[I++];)j(v)}},Token:y};function y(b,v,w,I){this.type=b,this.content=v,this.alias=w,this.length=(I||"").length|0}y.stringify=function b(v,w){if(typeof v=="string")return v;if(Array.isArray(v)){var I="";return v.forEach(function(X){I+=b(X,w)}),I}var j={type:v.type,content:b(v.content,w),tag:"span",classes:["token",v.type],attributes:{},language:w},B=v.alias;B&&(Array.isArray(B)?Array.prototype.push.apply(j.classes,B):j.classes.push(B)),f.hooks.run("wrap",j);var q="";for(var te in j.attributes)q+=" "+te+'="'+(j.attributes[te]||"").replace(/"/g,"&quot;")+'"';return"<"+j.tag+' class="'+j.classes.join(" ")+'"'+q+">"+j.content+"</"+j.tag+">"};function h(b,v,w,I){b.lastIndex=v;var j=b.exec(w);if(j&&I&&j[1]){var B=j[1].length;j.index+=B,j[0]=j[0].slice(B)}return j}function m(b,v,w,I,j,B){for(var q in w)if(!(!w.hasOwnProperty(q)||!w[q])){var te=w[q];te=Array.isArray(te)?te:[te];for(var X=0;X<te.length;++X){if(B&&B.cause==q+","+X)return;var Y=te[X],J=Y.inside,le=!!Y.lookbehind,ge=!!Y.greedy,ke=Y.alias;if(ge&&!Y.pattern.global){var U=Y.pattern.toString().match(/[imsuy]*$/)[0];Y.pattern=RegExp(Y.pattern.source,U+"g")}for(var ne=Y.pattern||Y,re=I.next,de=j;re!==v.tail&&!(B&&de>=B.reach);de+=re.value.length,re=re.next){var se=re.value;if(v.length>b.length)return;if(!(se instanceof y)){var ie=1,z;if(ge){if(z=h(ne,de,b,le),!z||z.index>=b.length)break;var M=z.index,Q=z.index+z[0].length,V=de;for(V+=re.value.length;M>=V;)re=re.next,V+=re.value.length;if(V-=re.value.length,de=V,re.value instanceof y)continue;for(var O=re;O!==v.tail&&(V<Q||typeof O.value=="string");O=O.next)ie++,V+=O.value.length;ie--,se=b.slice(de,V),z.index-=de}else if(z=h(ne,0,se,le),!z)continue;var M=z.index,ee=z[0],me=se.slice(0,M),be=se.slice(M+ee.length),ve=de+se.length;B&&ve>B.reach&&(B.reach=ve);var _e=re.prev;me&&(_e=k(v,_e,me),de+=me.length),C(v,_e,ie);var Se=new y(q,J?f.tokenize(ee,J):ee,ke,ee);if(re=k(v,_e,Se),be&&k(v,re,be),ie>1){var Ne={cause:q+","+X,reach:ve};m(b,v,w,re.prev,de,Ne),B&&Ne.reach>B.reach&&(B.reach=Ne.reach)}}}}}}function S(){var b={value:null,prev:null,next:null},v={value:null,prev:b,next:null};b.next=v,this.head=b,this.tail=v,this.length=0}function k(b,v,w){var I=v.next,j={value:w,prev:v,next:I};return v.next=j,I.prev=j,b.length++,j}function C(b,v,w){for(var I=v.next,j=0;j<w&&I!==b.tail;j++)I=I.next;v.next=I,I.prev=v,b.length-=j}function A(b){for(var v=[],w=b.head.next;w!==b.tail;)v.push(w.value),w=w.next;return v}return f}();o.exports=l,l.default=l}}),H=V0(W0());H.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},H.languages.markup.tag.inside["attr-value"].inside.entity=H.languages.markup.entity,H.languages.markup.doctype.inside["internal-subset"].inside=H.languages.markup,H.hooks.add("wrap",function(n){n.type==="entity"&&(n.attributes.title=n.content.replace(/&amp;/,"&"))}),Object.defineProperty(H.languages.markup.tag,"addInlined",{value:function(n,a){var l={},l=(l["language-"+a]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:H.languages[a]},l.cdata=/^<!\[CDATA\[|\]\]>$/i,{"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:l}}),a=(l["language-"+a]={pattern:/[\s\S]+/,inside:H.languages[a]},{});a[n]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return n}),"i"),lookbehind:!0,greedy:!0,inside:l},H.languages.insertBefore("markup","cdata",a)}}),Object.defineProperty(H.languages.markup.tag,"addAttribute",{value:function(n,o){H.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+n+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[o,"language-"+o],inside:H.languages[o]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),H.languages.html=H.languages.markup,H.languages.mathml=H.languages.markup,H.languages.svg=H.languages.markup,H.languages.xml=H.languages.extend("markup",{}),H.languages.ssml=H.languages.xml,H.languages.atom=H.languages.xml,H.languages.rss=H.languages.xml,function(n){var o={pattern:/\\[\\(){}[\]^$+*?|.]/,alias:"escape"},l=/\\(?:x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]+\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/,a="(?:[^\\\\-]|"+l.source+")",a=RegExp(a+"-"+a),c={pattern:/(<|')[^<>']+(?=[>']$)/,lookbehind:!0,alias:"variable"};n.languages.regex={"char-class":{pattern:/((?:^|[^\\])(?:\\\\)*)\[(?:[^\\\]]|\\[\s\S])*\]/,lookbehind:!0,inside:{"char-class-negation":{pattern:/(^\[)\^/,lookbehind:!0,alias:"operator"},"char-class-punctuation":{pattern:/^\[|\]$/,alias:"punctuation"},range:{pattern:a,inside:{escape:l,"range-punctuation":{pattern:/-/,alias:"operator"}}},"special-escape":o,"char-set":{pattern:/\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},escape:l}},"special-escape":o,"char-set":{pattern:/\.|\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},backreference:[{pattern:/\\(?![123][0-7]{2})[1-9]/,alias:"keyword"},{pattern:/\\k<[^<>']+>/,alias:"keyword",inside:{"group-name":c}}],anchor:{pattern:/[$^]|\\[ABbGZz]/,alias:"function"},escape:l,group:[{pattern:/\((?:\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,alias:"punctuation",inside:{"group-name":c}},{pattern:/\)/,alias:"punctuation"}],quantifier:{pattern:/(?:[+*?]|\{\d+(?:,\d*)?\})[?+]?/,alias:"number"},alternation:{pattern:/\|/,alias:"keyword"}}}(H),H.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},H.languages.javascript=H.languages.extend("clike",{"class-name":[H.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),H.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,H.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:H.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:H.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:H.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:H.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:H.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),H.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:H.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),H.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),H.languages.markup&&(H.languages.markup.tag.addInlined("script","javascript"),H.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),H.languages.js=H.languages.javascript,H.languages.actionscript=H.languages.extend("javascript",{keyword:/\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\b/,operator:/\+\+|--|(?:[+\-*\/%^]|&&?|\|\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/}),H.languages.actionscript["class-name"].alias="function",delete H.languages.actionscript.parameter,delete H.languages.actionscript["literal-property"],H.languages.markup&&H.languages.insertBefore("actionscript","string",{xml:{pattern:/(^|[^.])<\/?\w+(?:\s+[^\s>\/=]+=("|')(?:\\[\s\S]|(?!\2)[^\\])*\2)*\s*\/?>/,lookbehind:!0,inside:H.languages.markup}}),function(n){var o=/#(?!\{).+/,l={pattern:/#\{[^}]+\}/,alias:"variable"};n.languages.coffeescript=n.languages.extend("javascript",{comment:o,string:[{pattern:/'(?:\\[\s\S]|[^\\'])*'/,greedy:!0},{pattern:/"(?:\\[\s\S]|[^\\"])*"/,greedy:!0,inside:{interpolation:l}}],keyword:/\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\b/,"class-member":{pattern:/@(?!\d)\w+/,alias:"variable"}}),n.languages.insertBefore("coffeescript","comment",{"multiline-comment":{pattern:/###[\s\S]+?###/,alias:"comment"},"block-regex":{pattern:/\/{3}[\s\S]*?\/{3}/,alias:"regex",inside:{comment:o,interpolation:l}}}),n.languages.insertBefore("coffeescript","string",{"inline-javascript":{pattern:/`(?:\\[\s\S]|[^\\`])*`/,inside:{delimiter:{pattern:/^`|`$/,alias:"punctuation"},script:{pattern:/[\s\S]+/,alias:"language-javascript",inside:n.languages.javascript}}},"multiline-string":[{pattern:/'''[\s\S]*?'''/,greedy:!0,alias:"string"},{pattern:/"""[\s\S]*?"""/,greedy:!0,alias:"string",inside:{interpolation:l}}]}),n.languages.insertBefore("coffeescript","keyword",{property:/(?!\d)\w+(?=\s*:(?!:))/}),delete n.languages.coffeescript["template-string"],n.languages.coffee=n.languages.coffeescript}(H),function(n){var o=n.languages.javadoclike={parameter:{pattern:/(^[\t ]*(?:\/{3}|\*|\/\*\*)\s*@(?:arg|arguments|param)\s+)\w+/m,lookbehind:!0},keyword:{pattern:/(^[\t ]*(?:\/{3}|\*|\/\*\*)\s*|\{)@[a-z][a-zA-Z-]+\b/m,lookbehind:!0},punctuation:/[{}]/};Object.defineProperty(o,"addSupport",{value:function(l,a){(l=typeof l=="string"?[l]:l).forEach(function(c){var d=function(k){k.inside||(k.inside={}),k.inside.rest=a},f="doc-comment";if(y=n.languages[c]){var y,h=y[f];if((h=h||(y=n.languages.insertBefore(c,"comment",{"doc-comment":{pattern:/(^|[^\\])\/\*\*[^/][\s\S]*?(?:\*\/|$)/,lookbehind:!0,alias:"comment"}}))[f])instanceof RegExp&&(h=y[f]={pattern:h}),Array.isArray(h))for(var m=0,S=h.length;m<S;m++)h[m]instanceof RegExp&&(h[m]={pattern:h[m]}),d(h[m]);else d(h)}})}}),o.addSupport(["java","javascript","php"],o)}(H),function(n){var o=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,o=(n.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+o.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+o.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+o.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+o.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:o,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},n.languages.css.atrule.inside.rest=n.languages.css,n.languages.markup);o&&(o.tag.addInlined("style","css"),o.tag.addAttribute("style","css"))}(H),function(n){var o=/("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,o=(n.languages.css.selector={pattern:n.languages.css.selector.pattern,lookbehind:!0,inside:o={"pseudo-element":/:(?:after|before|first-letter|first-line|selection)|::[-\w]+/,"pseudo-class":/:[-\w]+/,class:/\.[-\w]+/,id:/#[-\w]+/,attribute:{pattern:RegExp(`\\[(?:[^[\\]"']|`+o.source+")*\\]"),greedy:!0,inside:{punctuation:/^\[|\]$/,"case-sensitivity":{pattern:/(\s)[si]$/i,lookbehind:!0,alias:"keyword"},namespace:{pattern:/^(\s*)(?:(?!\s)[-*\w\xA0-\uFFFF])*\|(?!=)/,lookbehind:!0,inside:{punctuation:/\|$/}},"attr-name":{pattern:/^(\s*)(?:(?!\s)[-\w\xA0-\uFFFF])+/,lookbehind:!0},"attr-value":[o,{pattern:/(=\s*)(?:(?!\s)[-\w\xA0-\uFFFF])+(?=\s*$)/,lookbehind:!0}],operator:/[|~*^$]?=/}},"n-th":[{pattern:/(\(\s*)[+-]?\d*[\dn](?:\s*[+-]\s*\d+)?(?=\s*\))/,lookbehind:!0,inside:{number:/[\dn]+/,operator:/[+-]/}},{pattern:/(\(\s*)(?:even|odd)(?=\s*\))/i,lookbehind:!0}],combinator:/>|\+|~|\|\|/,punctuation:/[(),]/}},n.languages.css.atrule.inside["selector-function-argument"].inside=o,n.languages.insertBefore("css","property",{variable:{pattern:/(^|[^-\w\xA0-\uFFFF])--(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*/i,lookbehind:!0}}),{pattern:/(\b\d+)(?:%|[a-z]+(?![\w-]))/,lookbehind:!0}),l={pattern:/(^|[^\w.-])-?(?:\d+(?:\.\d+)?|\.\d+)/,lookbehind:!0};n.languages.insertBefore("css","function",{operator:{pattern:/(\s)[+\-*\/](?=\s)/,lookbehind:!0},hexcode:{pattern:/\B#[\da-f]{3,8}\b/i,alias:"color"},color:[{pattern:/(^|[^\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|RebeccaPurple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\w-])/i,lookbehind:!0},{pattern:/\b(?:hsl|rgb)\(\s*\d{1,3}\s*,\s*\d{1,3}%?\s*,\s*\d{1,3}%?\s*\)\B|\b(?:hsl|rgb)a\(\s*\d{1,3}\s*,\s*\d{1,3}%?\s*,\s*\d{1,3}%?\s*,\s*(?:0|0?\.\d+|1)\s*\)\B/i,inside:{unit:o,number:l,function:/[\w-]+(?=\()/,punctuation:/[(),]/}}],entity:/\\[\da-f]{1,8}/i,unit:o,number:l})}(H),function(n){var o=/[*&][^\s[\]{},]+/,l=/!(?:<[\w\-%#;/?:@&=+$,.!~*'()[\]]+>|(?:[a-zA-Z\d-]*!)?[\w\-%#;/?:@&=+$.~*'()]+)?/,a="(?:"+l.source+"(?:[ 	]+"+o.source+")?|"+o.source+"(?:[ 	]+"+l.source+")?)",c=/(?:[^\s\x00-\x08\x0e-\x1f!"#%&'*,\-:>?@[\]`{|}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]|[?:-]<PLAIN>)(?:[ \t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,function(){return/[^\s\x00-\x08\x0e-\x1f,[\]{}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]/.source}),d=/"(?:[^"\\\r\n]|\\.)*"|'(?:[^'\\\r\n]|\\.)*'/.source;function f(y,h){h=(h||"").replace(/m/g,"")+"m";var m=/([:\-,[{]\s*(?:\s<<prop>>[ \t]+)?)(?:<<value>>)(?=[ \t]*(?:$|,|\]|\}|(?:[\r\n]\s*)?#))/.source.replace(/<<prop>>/g,function(){return a}).replace(/<<value>>/g,function(){return y});return RegExp(m,h)}n.languages.yaml={scalar:{pattern:RegExp(/([\-:]\s*(?:\s<<prop>>[ \t]+)?[|>])[ \t]*(?:((?:\r?\n|\r)[ \t]+)\S[^\r\n]*(?:\2[^\r\n]+)*)/.source.replace(/<<prop>>/g,function(){return a})),lookbehind:!0,alias:"string"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\-,[{\r\n?])[ \t]*(?:<<prop>>[ \t]+)?)<<key>>(?=\s*:\s)/.source.replace(/<<prop>>/g,function(){return a}).replace(/<<key>>/g,function(){return"(?:"+c+"|"+d+")"})),lookbehind:!0,greedy:!0,alias:"atrule"},directive:{pattern:/(^[ \t]*)%.+/m,lookbehind:!0,alias:"important"},datetime:{pattern:f(/\d{4}-\d\d?-\d\d?(?:[tT]|[ \t]+)\d\d?:\d{2}:\d{2}(?:\.\d*)?(?:[ \t]*(?:Z|[-+]\d\d?(?::\d{2})?))?|\d{4}-\d{2}-\d{2}|\d\d?:\d{2}(?::\d{2}(?:\.\d*)?)?/.source),lookbehind:!0,alias:"number"},boolean:{pattern:f(/false|true/.source,"i"),lookbehind:!0,alias:"important"},null:{pattern:f(/null|~/.source,"i"),lookbehind:!0,alias:"important"},string:{pattern:f(d),lookbehind:!0,greedy:!0},number:{pattern:f(/[+-]?(?:0x[\da-f]+|0o[0-7]+|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?|\.inf|\.nan)/.source,"i"),lookbehind:!0},tag:l,important:o,punctuation:/---|[:[\]{}\-,|>?]|\.\.\./},n.languages.yml=n.languages.yaml}(H),function(n){var o=/(?:\\.|[^\\\n\r]|(?:\n|\r\n?)(?![\r\n]))/.source;function l(m){return m=m.replace(/<inner>/g,function(){return o}),RegExp(/((?:^|[^\\])(?:\\{2})*)/.source+"(?:"+m+")")}var a=/(?:\\.|``(?:[^`\r\n]|`(?!`))+``|`[^`\r\n]+`|[^\\|\r\n`])+/.source,c=/\|?__(?:\|__)+\|?(?:(?:\n|\r\n?)|(?![\s\S]))/.source.replace(/__/g,function(){return a}),d=/\|?[ \t]*:?-{3,}:?[ \t]*(?:\|[ \t]*:?-{3,}:?[ \t]*)+\|?(?:\n|\r\n?)/.source,f=(n.languages.markdown=n.languages.extend("markup",{}),n.languages.insertBefore("markdown","prolog",{"front-matter-block":{pattern:/(^(?:\s*[\r\n])?)---(?!.)[\s\S]*?[\r\n]---(?!.)/,lookbehind:!0,greedy:!0,inside:{punctuation:/^---|---$/,"front-matter":{pattern:/\S+(?:\s+\S+)*/,alias:["yaml","language-yaml"],inside:n.languages.yaml}}},blockquote:{pattern:/^>(?:[\t ]*>)*/m,alias:"punctuation"},table:{pattern:RegExp("^"+c+d+"(?:"+c+")*","m"),inside:{"table-data-rows":{pattern:RegExp("^("+c+d+")(?:"+c+")*$"),lookbehind:!0,inside:{"table-data":{pattern:RegExp(a),inside:n.languages.markdown},punctuation:/\|/}},"table-line":{pattern:RegExp("^("+c+")"+d+"$"),lookbehind:!0,inside:{punctuation:/\||:?-{3,}:?/}},"table-header-row":{pattern:RegExp("^"+c+"$"),inside:{"table-header":{pattern:RegExp(a),alias:"important",inside:n.languages.markdown},punctuation:/\|/}}}},code:[{pattern:/((?:^|\n)[ \t]*\n|(?:^|\r\n?)[ \t]*\r\n?)(?: {4}|\t).+(?:(?:\n|\r\n?)(?: {4}|\t).+)*/,lookbehind:!0,alias:"keyword"},{pattern:/^```[\s\S]*?^```$/m,greedy:!0,inside:{"code-block":{pattern:/^(```.*(?:\n|\r\n?))[\s\S]+?(?=(?:\n|\r\n?)^```$)/m,lookbehind:!0},"code-language":{pattern:/^(```).+/,lookbehind:!0},punctuation:/```/}}],title:[{pattern:/\S.*(?:\n|\r\n?)(?:==+|--+)(?=[ \t]*$)/m,alias:"important",inside:{punctuation:/==+$|--+$/}},{pattern:/(^\s*)#.+/m,lookbehind:!0,alias:"important",inside:{punctuation:/^#+|#+$/}}],hr:{pattern:/(^\s*)([*-])(?:[\t ]*\2){2,}(?=\s*$)/m,lookbehind:!0,alias:"punctuation"},list:{pattern:/(^\s*)(?:[*+-]|\d+\.)(?=[\t ].)/m,lookbehind:!0,alias:"punctuation"},"url-reference":{pattern:/!?\[[^\]]+\]:[\t ]+(?:\S+|<(?:\\.|[^>\\])+>)(?:[\t ]+(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\)))?/,inside:{variable:{pattern:/^(!?\[)[^\]]+/,lookbehind:!0},string:/(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\))$/,punctuation:/^[\[\]!:]|[<>]/},alias:"url"},bold:{pattern:l(/\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\b|\*\*(?:(?!\*)<inner>|\*(?:(?!\*)<inner>)+\*)+\*\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^..)[\s\S]+(?=..$)/,lookbehind:!0,inside:{}},punctuation:/\*\*|__/}},italic:{pattern:l(/\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\b|\*(?:(?!\*)<inner>|\*\*(?:(?!\*)<inner>)+\*\*)+\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^.)[\s\S]+(?=.$)/,lookbehind:!0,inside:{}},punctuation:/[*_]/}},strike:{pattern:l(/(~~?)(?:(?!~)<inner>)+\2/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^~~?)[\s\S]+(?=\1$)/,lookbehind:!0,inside:{}},punctuation:/~~?/}},"code-snippet":{pattern:/(^|[^\\`])(?:``[^`\r\n]+(?:`[^`\r\n]+)*``(?!`)|`[^`\r\n]+`(?!`))/,lookbehind:!0,greedy:!0,alias:["code","keyword"]},url:{pattern:l(/!?\[(?:(?!\])<inner>)+\](?:\([^\s)]+(?:[\t ]+"(?:\\.|[^"\\])*")?\)|[ \t]?\[(?:(?!\])<inner>)+\])/.source),lookbehind:!0,greedy:!0,inside:{operator:/^!/,content:{pattern:/(^\[)[^\]]+(?=\])/,lookbehind:!0,inside:{}},variable:{pattern:/(^\][ \t]?\[)[^\]]+(?=\]$)/,lookbehind:!0},url:{pattern:/(^\]\()[^\s)]+/,lookbehind:!0},string:{pattern:/(^[ \t]+)"(?:\\.|[^"\\])*"(?=\)$)/,lookbehind:!0}}}}),["url","bold","italic","strike"].forEach(function(m){["url","bold","italic","strike","code-snippet"].forEach(function(S){m!==S&&(n.languages.markdown[m].inside.content.inside[S]=n.languages.markdown[S])})}),n.hooks.add("after-tokenize",function(m){m.language!=="markdown"&&m.language!=="md"||function S(k){if(k&&typeof k!="string")for(var C=0,A=k.length;C<A;C++){var b,v=k[C];v.type!=="code"?S(v.content):(b=v.content[1],v=v.content[3],b&&v&&b.type==="code-language"&&v.type==="code-block"&&typeof b.content=="string"&&(b=b.content.replace(/\b#/g,"sharp").replace(/\b\+\+/g,"pp"),b="language-"+(b=(/[a-z][\w-]*/i.exec(b)||[""])[0].toLowerCase()),v.alias?typeof v.alias=="string"?v.alias=[v.alias,b]:v.alias.push(b):v.alias=[b]))}}(m.tokens)}),n.hooks.add("wrap",function(m){if(m.type==="code-block"){for(var S="",k=0,C=m.classes.length;k<C;k++){var A=m.classes[k],A=/language-(.+)/.exec(A);if(A){S=A[1];break}}var b,v=n.languages[S];v?m.content=n.highlight(function(w){return w=w.replace(f,""),w=w.replace(/&(\w{1,8}|#x?[\da-f]{1,8});/gi,function(I,j){var B;return(j=j.toLowerCase())[0]==="#"?(B=j[1]==="x"?parseInt(j.slice(2),16):Number(j.slice(1)),h(B)):y[j]||I})}(m.content),v,S):S&&S!=="none"&&n.plugins.autoloader&&(b="md-"+new Date().valueOf()+"-"+Math.floor(1e16*Math.random()),m.attributes.id=b,n.plugins.autoloader.loadLanguages(S,function(){var w=document.getElementById(b);w&&(w.innerHTML=n.highlight(w.textContent,n.languages[S],S))}))}}),RegExp(n.languages.markup.tag.pattern.source,"gi")),y={amp:"&",lt:"<",gt:">",quot:'"'},h=String.fromCodePoint||String.fromCharCode;n.languages.md=n.languages.markdown}(H),H.languages.graphql={comment:/#.*/,description:{pattern:/(?:"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*")(?=\s*[a-z_])/i,greedy:!0,alias:"string",inside:{"language-markdown":{pattern:/(^"(?:"")?)(?!\1)[\s\S]+(?=\1$)/,lookbehind:!0,inside:H.languages.markdown}}},string:{pattern:/"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*"/,greedy:!0},number:/(?:\B-|\b)\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,boolean:/\b(?:false|true)\b/,variable:/\$[a-z_]\w*/i,directive:{pattern:/@[a-z_]\w*/i,alias:"function"},"attr-name":{pattern:/\b[a-z_]\w*(?=\s*(?:\((?:[^()"]|"(?:\\.|[^\\"\r\n])*")*\))?:)/i,greedy:!0},"atom-input":{pattern:/\b[A-Z]\w*Input\b/,alias:"class-name"},scalar:/\b(?:Boolean|Float|ID|Int|String)\b/,constant:/\b[A-Z][A-Z_\d]*\b/,"class-name":{pattern:/(\b(?:enum|implements|interface|on|scalar|type|union)\s+|&\s*|:\s*|\[)[A-Z_]\w*/,lookbehind:!0},fragment:{pattern:/(\bfragment\s+|\.{3}\s*(?!on\b))[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-mutation":{pattern:/(\bmutation\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-query":{pattern:/(\bquery\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},keyword:/\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\b/,operator:/[!=|&]|\.{3}/,"property-query":/\w+(?=\s*\()/,object:/\w+(?=\s*\{)/,punctuation:/[!(){}\[\]:=,]/,property:/\w+/},H.hooks.add("after-tokenize",function(n){if(n.language==="graphql")for(var o=n.tokens.filter(function(b){return typeof b!="string"&&b.type!=="comment"&&b.type!=="scalar"}),l=0;l<o.length;){var a=o[l++];if(a.type==="keyword"&&a.content==="mutation"){var c=[];if(k(["definition-mutation","punctuation"])&&S(1).content==="("){l+=2;var d=C(/^\($/,/^\)$/);if(d===-1)continue;for(;l<d;l++){var f=S(0);f.type==="variable"&&(A(f,"variable-input"),c.push(f.content))}l=d+1}if(k(["punctuation","property-query"])&&S(0).content==="{"&&(l++,A(S(0),"property-mutation"),0<c.length)){var y=C(/^\{$/,/^\}$/);if(y!==-1)for(var h=l;h<y;h++){var m=o[h];m.type==="variable"&&0<=c.indexOf(m.content)&&A(m,"variable-input")}}}}function S(b){return o[l+b]}function k(b,v){v=v||0;for(var w=0;w<b.length;w++){var I=S(w+v);if(!I||I.type!==b[w])return}return 1}function C(b,v){for(var w=1,I=l;I<o.length;I++){var j=o[I],B=j.content;if(j.type==="punctuation"&&typeof B=="string"){if(b.test(B))w++;else if(v.test(B)&&--w===0)return I}}return-1}function A(b,v){var w=b.alias;w?Array.isArray(w)||(b.alias=w=[w]):b.alias=w=[],w.push(v)}}),H.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},function(n){var o=n.languages.javascript["template-string"],l=o.pattern.source,a=o.inside.interpolation,c=a.inside["interpolation-punctuation"],d=a.pattern.source;function f(k,C){if(n.languages[k])return{pattern:RegExp("((?:"+C+")\\s*)"+l),lookbehind:!0,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},"embedded-code":{pattern:/[\s\S]+/,alias:k}}}}function y(k,C,A){return k={code:k,grammar:C,language:A},n.hooks.run("before-tokenize",k),k.tokens=n.tokenize(k.code,k.grammar),n.hooks.run("after-tokenize",k),k.tokens}function h(k,C,A){var w=n.tokenize(k,{interpolation:{pattern:RegExp(d),lookbehind:!0}}),b=0,v={},w=y(w.map(function(j){if(typeof j=="string")return j;for(var B,q,j=j.content;k.indexOf((q=b++,B="___"+A.toUpperCase()+"_"+q+"___"))!==-1;);return v[B]=j,B}).join(""),C,A),I=Object.keys(v);return b=0,function j(B){for(var q=0;q<B.length;q++){if(b>=I.length)return;var te,X,Y,J,le,ge,ke,U=B[q];typeof U=="string"||typeof U.content=="string"?(te=I[b],(ke=(ge=typeof U=="string"?U:U.content).indexOf(te))!==-1&&(++b,X=ge.substring(0,ke),le=v[te],Y=void 0,(J={})["interpolation-punctuation"]=c,(J=n.tokenize(le,J)).length===3&&((Y=[1,1]).push.apply(Y,y(J[1],n.languages.javascript,"javascript")),J.splice.apply(J,Y)),Y=new n.Token("interpolation",J,a.alias,le),J=ge.substring(ke+te.length),le=[],X&&le.push(X),le.push(Y),J&&(j(ge=[J]),le.push.apply(le,ge)),typeof U=="string"?(B.splice.apply(B,[q,1].concat(le)),q+=le.length-1):U.content=le)):(ke=U.content,Array.isArray(ke)?j(ke):j([ke]))}}(w),new n.Token(A,w,"language-"+A,k)}n.languages.javascript["template-string"]=[f("css",/\b(?:styled(?:\([^)]*\))?(?:\s*\.\s*\w+(?:\([^)]*\))*)*|css(?:\s*\.\s*(?:global|resolve))?|createGlobalStyle|keyframes)/.source),f("html",/\bhtml|\.\s*(?:inner|outer)HTML\s*\+?=/.source),f("svg",/\bsvg/.source),f("markdown",/\b(?:markdown|md)/.source),f("graphql",/\b(?:gql|graphql(?:\s*\.\s*experimental)?)/.source),f("sql",/\bsql/.source),o].filter(Boolean);var m={javascript:!0,js:!0,typescript:!0,ts:!0,jsx:!0,tsx:!0};function S(k){return typeof k=="string"?k:Array.isArray(k)?k.map(S).join(""):S(k.content)}n.hooks.add("after-tokenize",function(k){k.language in m&&function C(A){for(var b=0,v=A.length;b<v;b++){var w,I,j,B=A[b];typeof B!="string"&&(w=B.content,Array.isArray(w)?B.type==="template-string"?(B=w[1],w.length===3&&typeof B!="string"&&B.type==="embedded-code"&&(I=S(B),B=B.alias,B=Array.isArray(B)?B[0]:B,j=n.languages[B])&&(w[1]=h(I,j,B))):C(w):typeof w!="string"&&C([w]))}}(k.tokens)})}(H),function(n){n.languages.typescript=n.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),n.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete n.languages.typescript.parameter,delete n.languages.typescript["literal-property"];var o=n.languages.extend("typescript",{});delete o["class-name"],n.languages.typescript["class-name"].inside=o,n.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:o}}}}),n.languages.ts=n.languages.typescript}(H),function(n){var o=n.languages.javascript,l=/\{(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})+\}/.source,a="(@(?:arg|argument|param|property)\\s+(?:"+l+"\\s+)?)";n.languages.jsdoc=n.languages.extend("javadoclike",{parameter:{pattern:RegExp(a+/(?:(?!\s)[$\w\xA0-\uFFFF.])+(?=\s|$)/.source),lookbehind:!0,inside:{punctuation:/\./}}}),n.languages.insertBefore("jsdoc","keyword",{"optional-parameter":{pattern:RegExp(a+/\[(?:(?!\s)[$\w\xA0-\uFFFF.])+(?:=[^[\]]+)?\](?=\s|$)/.source),lookbehind:!0,inside:{parameter:{pattern:/(^\[)[$\w\xA0-\uFFFF\.]+/,lookbehind:!0,inside:{punctuation:/\./}},code:{pattern:/(=)[\s\S]*(?=\]$)/,lookbehind:!0,inside:o,alias:"language-javascript"},punctuation:/[=[\]]/}},"class-name":[{pattern:RegExp(/(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\s+(?:<TYPE>\s+)?)[A-Z]\w*(?:\.[A-Z]\w*)*/.source.replace(/<TYPE>/g,function(){return l})),lookbehind:!0,inside:{punctuation:/\./}},{pattern:RegExp("(@[a-z]+\\s+)"+l),lookbehind:!0,inside:{string:o.string,number:o.number,boolean:o.boolean,keyword:n.languages.typescript.keyword,operator:/=>|\.\.\.|[&|?:*]/,punctuation:/[.,;=<>{}()[\]]/}}],example:{pattern:/(@example\s+(?!\s))(?:[^@\s]|\s+(?!\s))+?(?=\s*(?:\*\s*)?(?:@\w|\*\/))/,lookbehind:!0,inside:{code:{pattern:/^([\t ]*(?:\*\s*)?)\S.*$/m,lookbehind:!0,inside:o,alias:"language-javascript"}}}}),n.languages.javadoclike.addSupport("javascript",n.languages.jsdoc)}(H),function(n){n.languages.flow=n.languages.extend("javascript",{}),n.languages.insertBefore("flow","keyword",{type:[{pattern:/\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|[Ss]ymbol|any|mixed|null|void)\b/,alias:"class-name"}]}),n.languages.flow["function-variable"].pattern=/(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=\s*(?:function\b|(?:\([^()]*\)(?:\s*:\s*\w+)?|(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/i,delete n.languages.flow.parameter,n.languages.insertBefore("flow","operator",{"flow-punctuation":{pattern:/\{\||\|\}/,alias:"punctuation"}}),Array.isArray(n.languages.flow.keyword)||(n.languages.flow.keyword=[n.languages.flow.keyword]),n.languages.flow.keyword.unshift({pattern:/(^|[^$]\b)(?:Class|declare|opaque|type)\b(?!\$)/,lookbehind:!0},{pattern:/(^|[^$]\B)\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\b(?!\$)/,lookbehind:!0})}(H),H.languages.n4js=H.languages.extend("javascript",{keyword:/\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\b/}),H.languages.insertBefore("n4js","constant",{annotation:{pattern:/@+\w+/,alias:"operator"}}),H.languages.n4jsd=H.languages.n4js,function(n){function o(f,y){return RegExp(f.replace(/<ID>/g,function(){return/(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/.source}),y)}n.languages.insertBefore("javascript","function-variable",{"method-variable":{pattern:RegExp("(\\.\\s*)"+n.languages.javascript["function-variable"].pattern.source),lookbehind:!0,alias:["function-variable","method","function","property-access"]}}),n.languages.insertBefore("javascript","function",{method:{pattern:RegExp("(\\.\\s*)"+n.languages.javascript.function.source),lookbehind:!0,alias:["function","property-access"]}}),n.languages.insertBefore("javascript","constant",{"known-class-name":[{pattern:/\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\b/,alias:"class-name"},{pattern:/\b(?:[A-Z]\w*)Error\b/,alias:"class-name"}]}),n.languages.insertBefore("javascript","keyword",{imports:{pattern:o(/(\bimport\b\s*)(?:<ID>(?:\s*,\s*(?:\*\s*as\s+<ID>|\{[^{}]*\}))?|\*\s*as\s+<ID>|\{[^{}]*\})(?=\s*\bfrom\b)/.source),lookbehind:!0,inside:n.languages.javascript},exports:{pattern:o(/(\bexport\b\s*)(?:\*(?:\s*as\s+<ID>)?(?=\s*\bfrom\b)|\{[^{}]*\})/.source),lookbehind:!0,inside:n.languages.javascript}}),n.languages.javascript.keyword.unshift({pattern:/\b(?:as|default|export|from|import)\b/,alias:"module"},{pattern:/\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\b/,alias:"control-flow"},{pattern:/\bnull\b/,alias:["null","nil"]},{pattern:/\bundefined\b/,alias:"nil"}),n.languages.insertBefore("javascript","operator",{spread:{pattern:/\.{3}/,alias:"operator"},arrow:{pattern:/=>/,alias:"operator"}}),n.languages.insertBefore("javascript","punctuation",{"property-access":{pattern:o(/(\.\s*)#?<ID>/.source),lookbehind:!0},"maybe-class-name":{pattern:/(^|[^$\w\xA0-\uFFFF])[A-Z][$\w\xA0-\uFFFF]+/,lookbehind:!0},dom:{pattern:/\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\b/,alias:"variable"},console:{pattern:/\bconsole(?=\s*\.)/,alias:"class-name"}});for(var l=["function","function-variable","method","method-variable","property-access"],a=0;a<l.length;a++){var d=l[a],c=n.languages.javascript[d],d=(c=n.util.type(c)==="RegExp"?n.languages.javascript[d]={pattern:c}:c).inside||{};(c.inside=d)["maybe-class-name"]=/^[A-Z][\s\S]*/}}(H),function(n){var o=n.util.clone(n.languages.javascript),l=/(?:\s|\/\/.*(?!.)|\/\*(?:[^*]|\*(?!\/))\*\/)/.source,a=/(?:\{(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])*\})/.source,c=/(?:\{<S>*\.{3}(?:[^{}]|<BRACES>)*\})/.source;function d(h,m){return h=h.replace(/<S>/g,function(){return l}).replace(/<BRACES>/g,function(){return a}).replace(/<SPREAD>/g,function(){return c}),RegExp(h,m)}c=d(c).source,n.languages.jsx=n.languages.extend("markup",o),n.languages.jsx.tag.pattern=d(/<\/?(?:[\w.:-]+(?:<S>+(?:[\w.:$-]+(?:=(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s{'"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\/?)?>/.source),n.languages.jsx.tag.inside.tag.pattern=/^<\/?[^\s>\/]*/,n.languages.jsx.tag.inside["attr-value"].pattern=/=(?!\{)(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s'">]+)/,n.languages.jsx.tag.inside.tag.inside["class-name"]=/^[A-Z]\w*(?:\.[A-Z]\w*)*$/,n.languages.jsx.tag.inside.comment=o.comment,n.languages.insertBefore("inside","attr-name",{spread:{pattern:d(/<SPREAD>/.source),inside:n.languages.jsx}},n.languages.jsx.tag),n.languages.insertBefore("inside","special-attr",{script:{pattern:d(/=<BRACES>/.source),alias:"language-javascript",inside:{"script-punctuation":{pattern:/^=(?=\{)/,alias:"punctuation"},rest:n.languages.jsx}}},n.languages.jsx.tag);function f(h){for(var m=[],S=0;S<h.length;S++){var k=h[S],C=!1;typeof k!="string"&&(k.type==="tag"&&k.content[0]&&k.content[0].type==="tag"?k.content[0].content[0].content==="</"?0<m.length&&m[m.length-1].tagName===y(k.content[0].content[1])&&m.pop():k.content[k.content.length-1].content!=="/>"&&m.push({tagName:y(k.content[0].content[1]),openedBraces:0}):0<m.length&&k.type==="punctuation"&&k.content==="{"?m[m.length-1].openedBraces++:0<m.length&&0<m[m.length-1].openedBraces&&k.type==="punctuation"&&k.content==="}"?m[m.length-1].openedBraces--:C=!0),(C||typeof k=="string")&&0<m.length&&m[m.length-1].openedBraces===0&&(C=y(k),S<h.length-1&&(typeof h[S+1]=="string"||h[S+1].type==="plain-text")&&(C+=y(h[S+1]),h.splice(S+1,1)),0<S&&(typeof h[S-1]=="string"||h[S-1].type==="plain-text")&&(C=y(h[S-1])+C,h.splice(S-1,1),S--),h[S]=new n.Token("plain-text",C,null,C)),k.content&&typeof k.content!="string"&&f(k.content)}}var y=function(h){return h?typeof h=="string"?h:typeof h.content=="string"?h.content:h.content.map(y).join(""):""};n.hooks.add("after-tokenize",function(h){h.language!=="jsx"&&h.language!=="tsx"||f(h.tokens)})}(H),function(n){var o=n.util.clone(n.languages.typescript),o=(n.languages.tsx=n.languages.extend("jsx",o),delete n.languages.tsx.parameter,delete n.languages.tsx["literal-property"],n.languages.tsx.tag);o.pattern=RegExp(/(^|[^\w$]|(?=<\/))/.source+"(?:"+o.pattern.source+")",o.pattern.flags),o.lookbehind=!0}(H),H.languages.swift={comment:{pattern:/(^|[^\\:])(?:\/\/.*|\/\*(?:[^/*]|\/(?!\*)|\*(?!\/)|\/\*(?:[^*]|\*(?!\/))*\*\/)*\*\/)/,lookbehind:!0,greedy:!0},"string-literal":[{pattern:RegExp(/(^|[^"#])/.source+"(?:"+/"(?:\\(?:\((?:[^()]|\([^()]*\))*\)|\r\n|[^(])|[^\\\r\n"])*"/.source+"|"+/"""(?:\\(?:\((?:[^()]|\([^()]*\))*\)|[^(])|[^\\"]|"(?!""))*"""/.source+")"+/(?!["#])/.source),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\\($/,alias:"punctuation"},punctuation:/\\(?=[\r\n])/,string:/[\s\S]+/}},{pattern:RegExp(/(^|[^"#])(#+)/.source+"(?:"+/"(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|\r\n|[^#])|[^\\\r\n])*?"/.source+"|"+/"""(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|[^#])|[^\\])*?"""/.source+")\\2"),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\#+\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\#+\($/,alias:"punctuation"},string:/[\s\S]+/}}],directive:{pattern:RegExp(/#/.source+"(?:"+/(?:elseif|if)\b/.source+"(?:[ 	]*"+/(?:![ \t]*)?(?:\b\w+\b(?:[ \t]*\((?:[^()]|\([^()]*\))*\))?|\((?:[^()]|\([^()]*\))*\))(?:[ \t]*(?:&&|\|\|))?/.source+")+|"+/(?:else|endif)\b/.source+")"),alias:"property",inside:{"directive-name":/^#\w+/,boolean:/\b(?:false|true)\b/,number:/\b\d+(?:\.\d+)*\b/,operator:/!|&&|\|\||[<>]=?/,punctuation:/[(),]/}},literal:{pattern:/#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\b/,alias:"constant"},"other-directive":{pattern:/#\w+\b/,alias:"property"},attribute:{pattern:/@\w+/,alias:"atrule"},"function-definition":{pattern:/(\bfunc\s+)\w+/,lookbehind:!0,alias:"function"},label:{pattern:/\b(break|continue)\s+\w+|\b[a-zA-Z_]\w*(?=\s*:\s*(?:for|repeat|while)\b)/,lookbehind:!0,alias:"important"},keyword:/\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\b/,boolean:/\b(?:false|true)\b/,nil:{pattern:/\bnil\b/,alias:"constant"},"short-argument":/\$\d+\b/,omit:{pattern:/\b_\b/,alias:"keyword"},number:/\b(?:[\d_]+(?:\.[\de_]+)?|0x[a-f0-9_]+(?:\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\b/i,"class-name":/\b[A-Z](?:[A-Z_\d]*[a-z]\w*)?\b/,function:/\b[a-z_]\w*(?=\s*\()/i,constant:/\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\b/,operator:/[-+*/%=!<>&|^~?]+|\.[.\-+*/%=!<>&|^~?]+/,punctuation:/[{}[\]();,.:\\]/},H.languages.swift["string-literal"].forEach(function(n){n.inside.interpolation.inside=H.languages.swift}),function(n){n.languages.kotlin=n.languages.extend("clike",{keyword:{pattern:/(^|[^.])\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\b/,lookbehind:!0},function:[{pattern:/(?:`[^\r\n`]+`|\b\w+)(?=\s*\()/,greedy:!0},{pattern:/(\.)(?:`[^\r\n`]+`|\w+)(?=\s*\{)/,lookbehind:!0,greedy:!0}],number:/\b(?:0[xX][\da-fA-F]+(?:_[\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\d+(?:_\d+)*(?:\.\d+(?:_\d+)*)?(?:[eE][+-]?\d+(?:_\d+)*)?[fFL]?)\b/,operator:/\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\/*%<>]=?|[?:]:?|\.\.|&&|\|\||\b(?:and|inv|or|shl|shr|ushr|xor)\b/}),delete n.languages.kotlin["class-name"];var o={"interpolation-punctuation":{pattern:/^\$\{?|\}$/,alias:"punctuation"},expression:{pattern:/[\s\S]+/,inside:n.languages.kotlin}};n.languages.insertBefore("kotlin","string",{"string-literal":[{pattern:/"""(?:[^$]|\$(?:(?!\{)|\{[^{}]*\}))*?"""/,alias:"multiline",inside:{interpolation:{pattern:/\$(?:[a-z_]\w*|\{[^{}]*\})/i,inside:o},string:/[\s\S]+/}},{pattern:/"(?:[^"\\\r\n$]|\\.|\$(?:(?!\{)|\{[^{}]*\}))*"/,alias:"singleline",inside:{interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$(?:[a-z_]\w*|\{[^{}]*\})/i,lookbehind:!0,inside:o},string:/[\s\S]+/}}],char:{pattern:/'(?:[^'\\\r\n]|\\(?:.|u[a-fA-F0-9]{0,4}))'/,greedy:!0}}),delete n.languages.kotlin.string,n.languages.insertBefore("kotlin","keyword",{annotation:{pattern:/\B@(?:\w+:)?(?:[A-Z]\w*|\[[^\]]+\])/,alias:"builtin"}}),n.languages.insertBefore("kotlin","function",{label:{pattern:/\b\w+@|@\w+\b/,alias:"symbol"}}),n.languages.kt=n.languages.kotlin,n.languages.kts=n.languages.kotlin}(H),H.languages.c=H.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/}),H.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}}),H.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},H.languages.c.string],char:H.languages.c.char,comment:H.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:H.languages.c}}}}),H.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/}),delete H.languages.c.boolean,H.languages.objectivec=H.languages.extend("c",{string:{pattern:/@?"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},keyword:/\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\b/,operator:/-[->]?|\+\+?|!=?|<<?=?|>>?=?|==?|&&?|\|\|?|[~^%?*\/@]/}),delete H.languages.objectivec["class-name"],H.languages.objc=H.languages.objectivec,H.languages.reason=H.languages.extend("clike",{string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^\\\r\n"])*"/,greedy:!0},"class-name":/\b[A-Z]\w*/,keyword:/\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\b/,operator:/\.{3}|:[:=]|\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\-*\/]\.?|\b(?:asr|land|lor|lsl|lsr|lxor|mod)\b/}),H.languages.insertBefore("reason","class-name",{char:{pattern:/'(?:\\x[\da-f]{2}|\\o[0-3][0-7][0-7]|\\\d{3}|\\.|[^'\\\r\n])'/,greedy:!0},constructor:/\b[A-Z]\w*\b(?!\s*\.)/,label:{pattern:/\b[a-z]\w*(?=::)/,alias:"symbol"}}),delete H.languages.reason.function,function(n){for(var o=/\/\*(?:[^*/]|\*(?!\/)|\/(?!\*)|<self>)*\*\//.source,l=0;l<2;l++)o=o.replace(/<self>/g,function(){return o});o=o.replace(/<self>/g,function(){return/[^\s\S]/.source}),n.languages.rust={comment:[{pattern:RegExp(/(^|[^\\])/.source+o),lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/b?"(?:\\[\s\S]|[^\\"])*"|b?r(#*)"(?:[^"]|"(?!\1))*"\1/,greedy:!0},char:{pattern:/b?'(?:\\(?:x[0-7][\da-fA-F]|u\{(?:[\da-fA-F]_*){1,6}\}|.)|[^\\\r\n\t'])'/,greedy:!0},attribute:{pattern:/#!?\[(?:[^\[\]"]|"(?:\\[\s\S]|[^\\"])*")*\]/,greedy:!0,alias:"attr-name",inside:{string:null}},"closure-params":{pattern:/([=(,:]\s*|\bmove\s*)\|[^|]*\||\|[^|]*\|(?=\s*(?:\{|->))/,lookbehind:!0,greedy:!0,inside:{"closure-punctuation":{pattern:/^\||\|$/,alias:"punctuation"},rest:null}},"lifetime-annotation":{pattern:/'\w+/,alias:"symbol"},"fragment-specifier":{pattern:/(\$\w+:)[a-z]+/,lookbehind:!0,alias:"punctuation"},variable:/\$\w+/,"function-definition":{pattern:/(\bfn\s+)\w+/,lookbehind:!0,alias:"function"},"type-definition":{pattern:/(\b(?:enum|struct|trait|type|union)\s+)\w+/,lookbehind:!0,alias:"class-name"},"module-declaration":[{pattern:/(\b(?:crate|mod)\s+)[a-z][a-z_\d]*/,lookbehind:!0,alias:"namespace"},{pattern:/(\b(?:crate|self|super)\s*)::\s*[a-z][a-z_\d]*\b(?:\s*::(?:\s*[a-z][a-z_\d]*\s*::)*)?/,lookbehind:!0,alias:"namespace",inside:{punctuation:/::/}}],keyword:[/\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\b/,/\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\b/],function:/\b[a-z_]\w*(?=\s*(?:::\s*<|\())/,macro:{pattern:/\b\w+!/,alias:"property"},constant:/\b[A-Z_][A-Z_\d]+\b/,"class-name":/\b[A-Z]\w*\b/,namespace:{pattern:/(?:\b[a-z][a-z_\d]*\s*::\s*)*\b[a-z][a-z_\d]*\s*::(?!\s*<)/,inside:{punctuation:/::/}},number:/\b(?:0x[\dA-Fa-f](?:_?[\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\d(?:_?\d)*)?\.)?\d(?:_?\d)*(?:[Ee][+-]?\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\b/,boolean:/\b(?:false|true)\b/,punctuation:/->|\.\.=|\.{1,3}|::|[{}[\];(),:]/,operator:/[-+*\/%!^]=?|=[=>]?|&[&=]?|\|[|=]?|<<?=?|>>?=?|[@?]/},n.languages.rust["closure-params"].inside.rest=n.languages.rust,n.languages.rust.attribute.inside.string=n.languages.rust.string}(H),H.languages.go=H.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"|`[^`]*`/,lookbehind:!0,greedy:!0},keyword:/\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/,boolean:/\b(?:_|false|iota|nil|true)\b/,number:[/\b0(?:b[01_]+|o[0-7_]+)i?\b/i,/\b0x(?:[a-f\d_]+(?:\.[a-f\d_]*)?|\.[a-f\d_]+)(?:p[+-]?\d+(?:_\d+)*)?i?(?!\w)/i,/(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?[\d_]+)?i?(?!\w)/i],operator:/[*\/%^!=]=?|\+[=+]?|-[=-]?|\|[=|]?|&(?:=|&|\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\.\.\./,builtin:/\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\b/}),H.languages.insertBefore("go","string",{char:{pattern:/'(?:\\.|[^'\\\r\n]){0,10}'/,greedy:!0}}),delete H.languages.go["class-name"],function(n){var o=/\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\b/,l=/\b(?!<keyword>)\w+(?:\s*\.\s*\w+)*\b/.source.replace(/<keyword>/g,function(){return o.source});n.languages.cpp=n.languages.extend("c",{"class-name":[{pattern:RegExp(/(\b(?:class|concept|enum|struct|typename)\s+)(?!<keyword>)\w+/.source.replace(/<keyword>/g,function(){return o.source})),lookbehind:!0},/\b[A-Z]\w*(?=\s*::\s*\w+\s*\()/,/\b[A-Z_]\w*(?=\s*::\s*~\w+\s*\()/i,/\b\w+(?=\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\s*::\s*\w+\s*\()/],keyword:o,number:{pattern:/(?:\b0b[01']+|\b0x(?:[\da-f']+(?:\.[\da-f']*)?|\.[\da-f']+)(?:p[+-]?[\d']+)?|(?:\b[\d']+(?:\.[\d']*)?|\B\.[\d']+)(?:e[+-]?[\d']+)?)[ful]{0,4}/i,greedy:!0},operator:/>>=?|<<=?|->|--|\+\+|&&|\|\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\b/,boolean:/\b(?:false|true)\b/}),n.languages.insertBefore("cpp","string",{module:{pattern:RegExp(/(\b(?:import|module)\s+)/.source+"(?:"+/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|<[^<>\r\n]*>/.source+"|"+/<mod-name>(?:\s*:\s*<mod-name>)?|:\s*<mod-name>/.source.replace(/<mod-name>/g,function(){return l})+")"),lookbehind:!0,greedy:!0,inside:{string:/^[<"][\s\S]+/,operator:/:/,punctuation:/\./}},"raw-string":{pattern:/R"([^()\\ ]{0,16})\([\s\S]*?\)\1"/,alias:"string",greedy:!0}}),n.languages.insertBefore("cpp","keyword",{"generic-function":{pattern:/\b(?!operator\b)[a-z_]\w*\s*<(?:[^<>]|<[^<>]*>)*>(?=\s*\()/i,inside:{function:/^\w+/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:n.languages.cpp}}}}),n.languages.insertBefore("cpp","operator",{"double-colon":{pattern:/::/,alias:"punctuation"}}),n.languages.insertBefore("cpp","class-name",{"base-clause":{pattern:/(\b(?:class|struct)\s+\w+\s*:\s*)[^;{}"'\s]+(?:\s+[^;{}"'\s]+)*(?=\s*[;{])/,lookbehind:!0,greedy:!0,inside:n.languages.extend("cpp",{})}}),n.languages.insertBefore("inside","double-colon",{"class-name":/\b[a-z_]\w*\b(?!\s*::)/i},n.languages.cpp["base-clause"])}(H),H.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/},H.languages.python["string-interpolation"].inside.interpolation.inside.rest=H.languages.python,H.languages.py=H.languages.python,H.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},H.languages.webmanifest=H.languages.json;var Cn={};$0(Cn,{dracula:()=>Y0,duotoneDark:()=>q0,duotoneLight:()=>K0,github:()=>J0,gruvboxMaterialDark:()=>Tw,gruvboxMaterialLight:()=>Rw,jettwaveDark:()=>Ew,jettwaveLight:()=>kw,nightOwl:()=>tw,nightOwlLight:()=>rw,oceanicNext:()=>lw,okaidia:()=>aw,oneDark:()=>bw,oneLight:()=>Cw,palenight:()=>uw,shadesOfPurple:()=>dw,synthwave84:()=>pw,ultramin:()=>yw,vsDark:()=>Rh,vsLight:()=>vw});var G0={plain:{color:"#F8F8F2",backgroundColor:"#282A36"},styles:[{types:["prolog","constant","builtin"],style:{color:"rgb(189, 147, 249)"}},{types:["inserted","function"],style:{color:"rgb(80, 250, 123)"}},{types:["deleted"],style:{color:"rgb(255, 85, 85)"}},{types:["changed"],style:{color:"rgb(255, 184, 108)"}},{types:["punctuation","symbol"],style:{color:"rgb(248, 248, 242)"}},{types:["string","char","tag","selector"],style:{color:"rgb(255, 121, 198)"}},{types:["keyword","variable"],style:{color:"rgb(189, 147, 249)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(98, 114, 164)"}},{types:["attr-name"],style:{color:"rgb(241, 250, 140)"}}]},Y0=G0,Z0={plain:{backgroundColor:"#2a2734",color:"#9a86fd"},styles:[{types:["comment","prolog","doctype","cdata","punctuation"],style:{color:"#6c6783"}},{types:["namespace"],style:{opacity:.7}},{types:["tag","operator","number"],style:{color:"#e09142"}},{types:["property","function"],style:{color:"#9a86fd"}},{types:["tag-id","selector","atrule-id"],style:{color:"#eeebff"}},{types:["attr-name"],style:{color:"#c4b9fe"}},{types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","atrule","placeholder","variable"],style:{color:"#ffcc99"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"#c4b9fe"}}]},q0=Z0,Q0={plain:{backgroundColor:"#faf8f5",color:"#728fcb"},styles:[{types:["comment","prolog","doctype","cdata","punctuation"],style:{color:"#b6ad9a"}},{types:["namespace"],style:{opacity:.7}},{types:["tag","operator","number"],style:{color:"#063289"}},{types:["property","function"],style:{color:"#b29762"}},{types:["tag-id","selector","atrule-id"],style:{color:"#2d2006"}},{types:["attr-name"],style:{color:"#896724"}},{types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","atrule"],style:{color:"#728fcb"}},{types:["placeholder","variable"],style:{color:"#93abdc"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"#896724"}}]},K0=Q0,X0={plain:{color:"#393A34",backgroundColor:"#f6f8fa"},styles:[{types:["comment","prolog","doctype","cdata"],style:{color:"#999988",fontStyle:"italic"}},{types:["namespace"],style:{opacity:.7}},{types:["string","attr-value"],style:{color:"#e3116c"}},{types:["punctuation","operator"],style:{color:"#393A34"}},{types:["entity","url","symbol","number","boolean","variable","constant","property","regex","inserted"],style:{color:"#36acaa"}},{types:["atrule","keyword","attr-name","selector"],style:{color:"#00a4db"}},{types:["function","deleted","tag"],style:{color:"#d73a49"}},{types:["function-variable"],style:{color:"#6f42c1"}},{types:["tag","selector","keyword"],style:{color:"#00009f"}}]},J0=X0,ew={plain:{color:"#d6deeb",backgroundColor:"#011627"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)",fontStyle:"italic"}},{types:["inserted","attr-name"],style:{color:"rgb(173, 219, 103)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(99, 119, 119)",fontStyle:"italic"}},{types:["string","url"],style:{color:"rgb(173, 219, 103)"}},{types:["variable"],style:{color:"rgb(214, 222, 235)"}},{types:["number"],style:{color:"rgb(247, 140, 108)"}},{types:["builtin","char","constant","function"],style:{color:"rgb(130, 170, 255)"}},{types:["punctuation"],style:{color:"rgb(199, 146, 234)"}},{types:["selector","doctype"],style:{color:"rgb(199, 146, 234)",fontStyle:"italic"}},{types:["class-name"],style:{color:"rgb(255, 203, 139)"}},{types:["tag","operator","keyword"],style:{color:"rgb(127, 219, 202)"}},{types:["boolean"],style:{color:"rgb(255, 88, 116)"}},{types:["property"],style:{color:"rgb(128, 203, 196)"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)"}}]},tw=ew,nw={plain:{color:"#403f53",backgroundColor:"#FBFBFB"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)",fontStyle:"italic"}},{types:["inserted","attr-name"],style:{color:"rgb(72, 118, 214)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(152, 159, 177)",fontStyle:"italic"}},{types:["string","builtin","char","constant","url"],style:{color:"rgb(72, 118, 214)"}},{types:["variable"],style:{color:"rgb(201, 103, 101)"}},{types:["number"],style:{color:"rgb(170, 9, 130)"}},{types:["punctuation"],style:{color:"rgb(153, 76, 195)"}},{types:["function","selector","doctype"],style:{color:"rgb(153, 76, 195)",fontStyle:"italic"}},{types:["class-name"],style:{color:"rgb(17, 17, 17)"}},{types:["tag"],style:{color:"rgb(153, 76, 195)"}},{types:["operator","property","keyword","namespace"],style:{color:"rgb(12, 150, 155)"}},{types:["boolean"],style:{color:"rgb(188, 84, 84)"}}]},rw=nw,Ct={char:"#D8DEE9",comment:"#999999",keyword:"#c5a5c5",primitive:"#5a9bcf",string:"#8dc891",variable:"#d7deea",boolean:"#ff8b50",tag:"#fc929e",function:"#79b6f2",className:"#FAC863"},ow={plain:{backgroundColor:"#282c34",color:"#ffffff"},styles:[{types:["attr-name"],style:{color:Ct.keyword}},{types:["attr-value"],style:{color:Ct.string}},{types:["comment","block-comment","prolog","doctype","cdata","shebang"],style:{color:Ct.comment}},{types:["property","number","function-name","constant","symbol","deleted"],style:{color:Ct.primitive}},{types:["boolean"],style:{color:Ct.boolean}},{types:["tag"],style:{color:Ct.tag}},{types:["string"],style:{color:Ct.string}},{types:["punctuation"],style:{color:Ct.string}},{types:["selector","char","builtin","inserted"],style:{color:Ct.char}},{types:["function"],style:{color:Ct.function}},{types:["operator","entity","url","variable"],style:{color:Ct.variable}},{types:["keyword"],style:{color:Ct.keyword}},{types:["atrule","class-name"],style:{color:Ct.className}},{types:["important"],style:{fontWeight:"400"}},{types:["bold"],style:{fontWeight:"bold"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["namespace"],style:{opacity:.7}}]},lw=ow,iw={plain:{color:"#f8f8f2",backgroundColor:"#272822"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"#f92672",fontStyle:"italic"}},{types:["inserted"],style:{color:"rgb(173, 219, 103)",fontStyle:"italic"}},{types:["comment"],style:{color:"#8292a2",fontStyle:"italic"}},{types:["string","url"],style:{color:"#a6e22e"}},{types:["variable"],style:{color:"#f8f8f2"}},{types:["number"],style:{color:"#ae81ff"}},{types:["builtin","char","constant","function","class-name"],style:{color:"#e6db74"}},{types:["punctuation"],style:{color:"#f8f8f2"}},{types:["selector","doctype"],style:{color:"#a6e22e",fontStyle:"italic"}},{types:["tag","operator","keyword"],style:{color:"#66d9ef"}},{types:["boolean"],style:{color:"#ae81ff"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)",opacity:.7}},{types:["tag","property"],style:{color:"#f92672"}},{types:["attr-name"],style:{color:"#a6e22e !important"}},{types:["doctype"],style:{color:"#8292a2"}},{types:["rule"],style:{color:"#e6db74"}}]},aw=iw,sw={plain:{color:"#bfc7d5",backgroundColor:"#292d3e"},styles:[{types:["comment"],style:{color:"rgb(105, 112, 152)",fontStyle:"italic"}},{types:["string","inserted"],style:{color:"rgb(195, 232, 141)"}},{types:["number"],style:{color:"rgb(247, 140, 108)"}},{types:["builtin","char","constant","function"],style:{color:"rgb(130, 170, 255)"}},{types:["punctuation","selector"],style:{color:"rgb(199, 146, 234)"}},{types:["variable"],style:{color:"rgb(191, 199, 213)"}},{types:["class-name","attr-name"],style:{color:"rgb(255, 203, 107)"}},{types:["tag","deleted"],style:{color:"rgb(255, 85, 114)"}},{types:["operator"],style:{color:"rgb(137, 221, 255)"}},{types:["boolean"],style:{color:"rgb(255, 88, 116)"}},{types:["keyword"],style:{fontStyle:"italic"}},{types:["doctype"],style:{color:"rgb(199, 146, 234)",fontStyle:"italic"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)"}},{types:["url"],style:{color:"rgb(221, 221, 221)"}}]},uw=sw,cw={plain:{color:"#9EFEFF",backgroundColor:"#2D2A55"},styles:[{types:["changed"],style:{color:"rgb(255, 238, 128)"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)"}},{types:["inserted"],style:{color:"rgb(173, 219, 103)"}},{types:["comment"],style:{color:"rgb(179, 98, 255)",fontStyle:"italic"}},{types:["punctuation"],style:{color:"rgb(255, 255, 255)"}},{types:["constant"],style:{color:"rgb(255, 98, 140)"}},{types:["string","url"],style:{color:"rgb(165, 255, 144)"}},{types:["variable"],style:{color:"rgb(255, 238, 128)"}},{types:["number","boolean"],style:{color:"rgb(255, 98, 140)"}},{types:["attr-name"],style:{color:"rgb(255, 180, 84)"}},{types:["keyword","operator","property","namespace","tag","selector","doctype"],style:{color:"rgb(255, 157, 0)"}},{types:["builtin","char","constant","function","class-name"],style:{color:"rgb(250, 208, 0)"}}]},dw=cw,fw={plain:{backgroundColor:"linear-gradient(to bottom, #2a2139 75%, #34294f)",backgroundImage:"#34294f",color:"#f92aad",textShadow:"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"},styles:[{types:["comment","block-comment","prolog","doctype","cdata"],style:{color:"#495495",fontStyle:"italic"}},{types:["punctuation"],style:{color:"#ccc"}},{types:["tag","attr-name","namespace","number","unit","hexcode","deleted"],style:{color:"#e2777a"}},{types:["property","selector"],style:{color:"#72f1b8",textShadow:"0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475"}},{types:["function-name"],style:{color:"#6196cc"}},{types:["boolean","selector-id","function"],style:{color:"#fdfdfd",textShadow:"0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"}},{types:["class-name","maybe-class-name","builtin"],style:{color:"#fff5f6",textShadow:"0 0 2px #000, 0 0 10px #fc1f2c75, 0 0 5px #fc1f2c75, 0 0 25px #fc1f2c75"}},{types:["constant","symbol"],style:{color:"#f92aad",textShadow:"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"}},{types:["important","atrule","keyword","selector-class"],style:{color:"#f4eee4",textShadow:"0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"}},{types:["string","char","attr-value","regex","variable"],style:{color:"#f87c32"}},{types:["parameter"],style:{fontStyle:"italic"}},{types:["entity","url"],style:{color:"#67cdcc"}},{types:["operator"],style:{color:"ffffffee"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["entity"],style:{cursor:"help"}},{types:["inserted"],style:{color:"green"}}]},pw=fw,hw={plain:{color:"#282a2e",backgroundColor:"#ffffff"},styles:[{types:["comment"],style:{color:"rgb(197, 200, 198)"}},{types:["string","number","builtin","variable"],style:{color:"rgb(150, 152, 150)"}},{types:["class-name","function","tag","attr-name"],style:{color:"rgb(40, 42, 46)"}}]},yw=hw,gw={plain:{color:"#9CDCFE",backgroundColor:"#1E1E1E"},styles:[{types:["prolog"],style:{color:"rgb(0, 0, 128)"}},{types:["comment"],style:{color:"rgb(106, 153, 85)"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"rgb(86, 156, 214)"}},{types:["number","inserted"],style:{color:"rgb(181, 206, 168)"}},{types:["constant"],style:{color:"rgb(100, 102, 149)"}},{types:["attr-name","variable"],style:{color:"rgb(156, 220, 254)"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"rgb(206, 145, 120)"}},{types:["selector"],style:{color:"rgb(215, 186, 125)"}},{types:["tag"],style:{color:"rgb(78, 201, 176)"}},{types:["tag"],languages:["markup"],style:{color:"rgb(86, 156, 214)"}},{types:["punctuation","operator"],style:{color:"rgb(212, 212, 212)"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"rgb(220, 220, 170)"}},{types:["class-name"],style:{color:"rgb(78, 201, 176)"}},{types:["char"],style:{color:"rgb(209, 105, 105)"}}]},Rh=gw,mw={plain:{color:"#000000",backgroundColor:"#ffffff"},styles:[{types:["comment"],style:{color:"rgb(0, 128, 0)"}},{types:["builtin"],style:{color:"rgb(0, 112, 193)"}},{types:["number","variable","inserted"],style:{color:"rgb(9, 134, 88)"}},{types:["operator"],style:{color:"rgb(0, 0, 0)"}},{types:["constant","char"],style:{color:"rgb(129, 31, 63)"}},{types:["tag"],style:{color:"rgb(128, 0, 0)"}},{types:["attr-name"],style:{color:"rgb(255, 0, 0)"}},{types:["deleted","string"],style:{color:"rgb(163, 21, 21)"}},{types:["changed","punctuation"],style:{color:"rgb(4, 81, 165)"}},{types:["function","keyword"],style:{color:"rgb(0, 0, 255)"}},{types:["class-name"],style:{color:"rgb(38, 127, 153)"}}]},vw=mw,ww={plain:{color:"#f8fafc",backgroundColor:"#011627"},styles:[{types:["prolog"],style:{color:"#000080"}},{types:["comment"],style:{color:"#6A9955"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"#569CD6"}},{types:["number","inserted"],style:{color:"#B5CEA8"}},{types:["constant"],style:{color:"#f8fafc"}},{types:["attr-name","variable"],style:{color:"#9CDCFE"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"#cbd5e1"}},{types:["selector"],style:{color:"#D7BA7D"}},{types:["tag"],style:{color:"#0ea5e9"}},{types:["tag"],languages:["markup"],style:{color:"#0ea5e9"}},{types:["punctuation","operator"],style:{color:"#D4D4D4"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"#7dd3fc"}},{types:["class-name"],style:{color:"#0ea5e9"}},{types:["char"],style:{color:"#D16969"}}]},Ew=ww,xw={plain:{color:"#0f172a",backgroundColor:"#f1f5f9"},styles:[{types:["prolog"],style:{color:"#000080"}},{types:["comment"],style:{color:"#6A9955"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"#0c4a6e"}},{types:["number","inserted"],style:{color:"#B5CEA8"}},{types:["constant"],style:{color:"#0f172a"}},{types:["attr-name","variable"],style:{color:"#0c4a6e"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"#64748b"}},{types:["selector"],style:{color:"#D7BA7D"}},{types:["tag"],style:{color:"#0ea5e9"}},{types:["tag"],languages:["markup"],style:{color:"#0ea5e9"}},{types:["punctuation","operator"],style:{color:"#475569"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"#0e7490"}},{types:["class-name"],style:{color:"#0ea5e9"}},{types:["char"],style:{color:"#D16969"}}]},kw=xw,Sw={plain:{backgroundColor:"hsl(220, 13%, 18%)",color:"hsl(220, 14%, 71%)",textShadow:"0 1px rgba(0, 0, 0, 0.3)"},styles:[{types:["comment","prolog","cdata"],style:{color:"hsl(220, 10%, 40%)"}},{types:["doctype","punctuation","entity"],style:{color:"hsl(220, 14%, 71%)"}},{types:["attr-name","class-name","maybe-class-name","boolean","constant","number","atrule"],style:{color:"hsl(29, 54%, 61%)"}},{types:["keyword"],style:{color:"hsl(286, 60%, 67%)"}},{types:["property","tag","symbol","deleted","important"],style:{color:"hsl(355, 65%, 65%)"}},{types:["selector","string","char","builtin","inserted","regex","attr-value"],style:{color:"hsl(95, 38%, 62%)"}},{types:["variable","operator","function"],style:{color:"hsl(207, 82%, 66%)"}},{types:["url"],style:{color:"hsl(187, 47%, 55%)"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"hsl(220, 14%, 71%)"}}]},bw=Sw,_w={plain:{backgroundColor:"hsl(230, 1%, 98%)",color:"hsl(230, 8%, 24%)"},styles:[{types:["comment","prolog","cdata"],style:{color:"hsl(230, 4%, 64%)"}},{types:["doctype","punctuation","entity"],style:{color:"hsl(230, 8%, 24%)"}},{types:["attr-name","class-name","boolean","constant","number","atrule"],style:{color:"hsl(35, 99%, 36%)"}},{types:["keyword"],style:{color:"hsl(301, 63%, 40%)"}},{types:["property","tag","symbol","deleted","important"],style:{color:"hsl(5, 74%, 59%)"}},{types:["selector","string","char","builtin","inserted","regex","attr-value","punctuation"],style:{color:"hsl(119, 34%, 47%)"}},{types:["variable","operator","function"],style:{color:"hsl(221, 87%, 60%)"}},{types:["url"],style:{color:"hsl(198, 99%, 37%)"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"hsl(230, 8%, 24%)"}}]},Cw=_w,Ow={plain:{color:"#ebdbb2",backgroundColor:"#292828"},styles:[{types:["imports","class-name","maybe-class-name","constant","doctype","builtin","function"],style:{color:"#d8a657"}},{types:["property-access"],style:{color:"#7daea3"}},{types:["tag"],style:{color:"#e78a4e"}},{types:["attr-name","char","url","regex"],style:{color:"#a9b665"}},{types:["attr-value","string"],style:{color:"#89b482"}},{types:["comment","prolog","cdata","operator","inserted"],style:{color:"#a89984"}},{types:["delimiter","boolean","keyword","selector","important","atrule","property","variable","deleted"],style:{color:"#ea6962"}},{types:["entity","number","symbol"],style:{color:"#d3869b"}}]},Tw=Ow,Aw={plain:{color:"#654735",backgroundColor:"#f9f5d7"},styles:[{types:["delimiter","boolean","keyword","selector","important","atrule","property","variable","deleted"],style:{color:"#af2528"}},{types:["imports","class-name","maybe-class-name","constant","doctype","builtin"],style:{color:"#b4730e"}},{types:["string","attr-value"],style:{color:"#477a5b"}},{types:["property-access"],style:{color:"#266b79"}},{types:["function","attr-name","char","url"],style:{color:"#72761e"}},{types:["tag"],style:{color:"#b94c07"}},{types:["comment","prolog","cdata","operator","inserted"],style:{color:"#a89984"}},{types:["entity","number","symbol"],style:{color:"#924f79"}}]},Rw=Aw,Nw=n=>N.useCallback(o=>{var l=o,{className:a,style:c,line:d}=l,f=Ah(l,["className","style","line"]);const y=ua(un({},f),{className:Ch("token-line",a)});return typeof n=="object"&&"plain"in n&&(y.style=n.plain),typeof c=="object"&&(y.style=un(un({},y.style||{}),c)),y},[n]),Lw=n=>{const o=N.useCallback(({types:l,empty:a})=>{if(n!=null){{if(l.length===1&&l[0]==="plain")return a!=null?{display:"inline-block"}:void 0;if(l.length===1&&a!=null)return n[l[0]]}return Object.assign(a!=null?{display:"inline-block"}:{},...l.map(c=>n[c]))}},[n]);return N.useCallback(l=>{var a=l,{token:c,className:d,style:f}=a,y=Ah(a,["token","className","style"]);const h=ua(un({},y),{className:Ch("token",...c.types,d),children:c.content,style:o(c)});return f!=null&&(h.style=un(un({},h.style||{}),f)),h},[o])},Fw=/\r\n|\r|\n/,tp=n=>{n.length===0?n.push({types:["plain"],content:`
`,empty:!0}):n.length===1&&n[0].content===""&&(n[0].content=`
`,n[0].empty=!0)},np=(n,o)=>{const l=n.length;return l>0&&n[l-1]===o?n:n.concat(o)},jw=n=>{const o=[[]],l=[n],a=[0],c=[n.length];let d=0,f=0,y=[];const h=[y];for(;f>-1;){for(;(d=a[f]++)<c[f];){let m,S=o[f];const C=l[f][d];if(typeof C=="string"?(S=f>0?S:["plain"],m=C):(S=np(S,C.type),C.alias&&(S=np(S,C.alias)),m=C.content),typeof m!="string"){f++,o.push(S),l.push(m),a.push(0),c.push(m.length);continue}const A=m.split(Fw),b=A.length;y.push({types:S,content:A[0]});for(let v=1;v<b;v++)tp(y),h.push(y=[]),y.push({types:S,content:A[v]})}f--,o.pop(),l.pop(),a.pop(),c.pop()}return tp(y),h},rp=jw,Iw=({prism:n,code:o,grammar:l,language:a})=>N.useMemo(()=>{if(l==null)return rp([o]);const c={code:o,grammar:l,language:a,tokens:[]};return n.hooks.run("before-tokenize",c),c.tokens=n.tokenize(o,l),n.hooks.run("after-tokenize",c),rp(c.tokens)},[o,l,a,n]),Pw=(n,o)=>{const{plain:l}=n,a=n.styles.reduce((c,d)=>{const{languages:f,style:y}=d;return f&&!f.includes(o)||d.types.forEach(h=>{const m=un(un({},c[h]),y);c[h]=m}),c},{});return a.root=l,a.plain=ua(un({},l),{backgroundColor:void 0}),a},Dw=Pw,Mw=({children:n,language:o,code:l,theme:a,prism:c})=>{const d=o.toLowerCase(),f=Dw(a,d),y=Nw(f),h=Lw(f),m=c.languages[d],S=Iw({prism:c,language:d,code:l,grammar:m});return n({tokens:S,className:`prism-code language-${d}`,style:f!=null?f.root:{},getLineProps:y,getTokenProps:h})},op=n=>N.createElement(Mw,ua(un({},n),{prism:n.prism||H,theme:n.theme||Rh,code:n.code,language:n.language}));/*! Bundled license information:

prismjs/prism.js:
  (**
   * Prism: Lightweight, robust, elegant syntax highlighting
   *
   * @license MIT <https://opensource.org/licenses/MIT>
   * <AUTHOR> Verou <https://lea.verou.me>
   * @namespace
   * @public
   *)
*/const Bw=n=>{const o=dn.parse(n).source;return o==="true"?!0:o==="false"?!1:he.addons.source.defaultState},Nh=({children:n,theme:o,language:l="tsx",locStart:a,locEnd:c,className:d})=>{const f=typeof a<"u"&&typeof c<"u",y=/language-(\w+)/.exec(d||"");return y?(l=y[1],g.jsx(op,{code:n.trim(),language:l,theme:{...o==="dark"?Cn.nightOwl:Cn.github,plain:{...(o==="dark"?Cn.nightOwl:Cn.github).plain,backgroundColor:"var(--ladle-bg-color-secondary)"}},children:({className:h,style:m,tokens:S,getTokenProps:k})=>g.jsx("div",{className:h,style:{...m,textAlign:"left",margin:"0.5em 0 1em 0",padding:"1em"},children:S.map((C,A)=>g.jsx("div",{children:C.map((b,v)=>g.jsx("span",{...k({token:b})},v))},A))})})):f?g.jsx(op,{code:n.trim(),language:l,theme:{...o==="dark"?Cn.nightOwl:Cn.github,plain:{...(o==="dark"?Cn.nightOwl:Cn.github).plain,backgroundColor:"var(--ladle-bg-color-secondary)"}},children:({className:h,style:m,tokens:S,getLineProps:k,getTokenProps:C})=>g.jsx("pre",{className:h,style:{...m,textAlign:"left",margin:"0.5em 0 1em 0",padding:"1em 0",overflow:"auto",maxHeight:"50vh"},children:S.map((A,b)=>g.jsxs("div",{id:`ladle_loc_${b+1}`,...k({line:A,key:b}),style:{display:"table-row"},children:[g.jsx("span",{className:"ladle-addon-source-lineno",style:b+1>=a&&b+1<=c?{backgroundColor:"var(--ladle-color-accent)",color:"#FFF"}:void 0,children:b+1}),g.jsx("div",{style:{display:"table-cell",paddingLeft:"0.5em"},children:A.map((v,w)=>g.jsx("span",{...C({token:v,key:w})},w))})]},b))})}):g.jsx("code",{children:n})},zw=({globalState:n})=>{if(!xo[n.story])return g.jsx(g.Fragment,{children:"There is no story loaded."});const{entry:o,locStart:l,locEnd:a}=xo[n.story];return N.useEffect(()=>{window.location.hash="",window.location.hash=`ladle_loc_${l}`},[l]),g.jsxs(g.Fragment,{children:[g.jsx(x0,{path:o,locStart:l,locEnd:a}),g.jsx(Nh,{theme:n.theme,language:"tsx",locEnd:a,locStart:l,children:decodeURIComponent(k0[n.story])})]})},Uw=({globalState:n,dispatch:o})=>{const l="Show the story source code.";return $t(he.hotkeys.source,()=>{o({type:Re.UpdateSource,value:!n.source})},{enabled:n.hotkeys&&he.addons.source.enabled}),g.jsx("li",{children:g.jsxs("button",{"aria-label":l,"data-testid":"addon-source",title:l,onClick:()=>{o({type:Re.UpdateSource,value:!n.source})},className:n.source?"source-active":"",type:"button",children:[g.jsx(Sm,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:l}),g.jsx("label",{children:"Story Source Code"}),g.jsx(So,{isOpen:n.source,close:()=>o({type:Re.UpdateSource,value:!1}),label:"Dialog with the story source code.",children:g.jsx(zw,{globalState:n})})]})})},Tn=Date;let Iu=null;const ko=class extends Tn{constructor(o,l,a,c,d,f,y){super();let h;switch(arguments.length){case 0:Iu!==null?h=new Tn(Iu.valueOf()):h=new Tn;break;case 1:h=new Tn(o);break;default:a=typeof a>"u"?1:a,c=c||0,d=d||0,f=f||0,y=y||0,h=new Tn(o,l,a,c,d,f,y);break}return h}};ko.UTC=Tn.UTC;ko.now=function(){return new ko().valueOf()};ko.parse=function(n){return Tn.parse(n)};ko.toString=function(){return Tn.toString()};function $w(n){const o=new Date(n.valueOf());if(isNaN(o.getTime()))throw new TypeError("mockdate: The time set is an invalid date: "+n);Date=ko,Iu=o.valueOf()}function Hw(){Date=Tn}const Vw=({children:n,active:o,width:l,story:a,mode:c})=>!o&&l===0||c===ht.Preview?n:g.jsx(N0,{title:`Story ${a}`,initialContent:'<!DOCTYPE html><html><head><base target="_parent" /></head><body style="margin:0"><div id="root"></div></body></html>',mountTarget:"#root",className:"ladle-iframe",style:{width:l||"100%"},children:n}),lp=({globalState:n,dispatch:o})=>{var m;const l=xo[n.story],a=n.width,c=(m=l==null?void 0:l.meta)==null?void 0:m.meta,d=c?c.hotkeys:!0,f=c?c.mockDate:void 0,y=l&&c?c.iframed:!1;let h=l&&c?c.width:0;return Object.keys(he.addons.width.options).forEach(S=>{S===h&&(h=he.addons.width.options[S])}),N.useEffect(()=>{f?$w(f):Hw()},[f]),N.useEffect(()=>{typeof d<"u"&&d!==n.hotkeys&&o({type:Re.UpdateHotkeys,value:d})},[d]),N.useEffect(()=>{if(h&&h!==0){o({type:Re.UpdateWidth,value:h});return}he.addons.width.defaultState!==0&&o({type:Re.UpdateWidth,value:he.addons.width.defaultState})},[h,n.story]),N.useEffect(()=>{n.mode!==ht.Preview&&(y||a)?document.documentElement.setAttribute("data-iframed",`${a}`):document.documentElement.removeAttribute("data-iframed")},[y,n.story,n.mode,n.width]),n.story?g.jsx(j0,{children:g.jsx(N.Suspense,{fallback:g.jsx(mm,{}),children:g.jsx(Vw,{active:y,story:n.story,width:a,mode:n.mode,children:g.jsx(F0,{active:(y||a>0)&&n.mode!==ht.Preview,rtl:n.rtl,width:a,children:g.jsx(O0,{components:{code:S=>g.jsx(Nh,{...S,theme:n.theme})},children:g.jsx(E0,{config:he,globalState:n,dispatch:o,storyMeta:c,children:l?N.createElement(l.component):g.jsx(I0,{activeStory:n.story})})})})})})}):null},Ww=()=>g.jsxs("div",{className:"ladle-error-content",children:[g.jsx("h1",{children:"No stories found"}),g.jsxs("p",{children:["The configured glob pattern for stories is: ",g.jsx(Or,{children:Ut.stories}),"."]}),g.jsxs("p",{children:["It can be changed through the"," ",g.jsx(wo,{href:"https://www.ladle.dev/docs/config#story-filenames",children:"configuration file"})," ","or CLI flag ",g.jsx(Or,{children:"--stories=your-glob"}),"."]}),g.jsx("p",{children:g.jsx(wo,{href:"https://github.com/tajo/ladle",children:"GitHub"})}),g.jsx("p",{children:g.jsx(wo,{href:"https://www.ladle.dev",children:"Docs"})})]});var fu={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var ip;function Gw(){return ip||(ip=1,function(n){(function(){var o={}.hasOwnProperty;function l(){for(var d="",f=0;f<arguments.length;f++){var y=arguments[f];y&&(d=c(d,a(y)))}return d}function a(d){if(typeof d=="string"||typeof d=="number")return d;if(typeof d!="object")return"";if(Array.isArray(d))return l.apply(null,d);if(d.toString!==Object.prototype.toString&&!d.toString.toString().includes("[native code]"))return d.toString();var f="";for(var y in d)o.call(d,y)&&d[y]&&(f=c(f,y));return f}function c(d,f){return f?d?d+" "+f:d+f:d}n.exports?(l.default=l,n.exports=l):window.classNames=l})()}(fu)),fu.exports}var Yw=Gw();const Lh=kl(Yw);function ta(){return ta=Object.assign?Object.assign.bind():function(n){for(var o=1;o<arguments.length;o++){var l=arguments[o];for(var a in l)({}).hasOwnProperty.call(l,a)&&(n[a]=l[a])}return n},ta.apply(null,arguments)}var Cr;(function(n){n.Pop="POP",n.Push="PUSH",n.Replace="REPLACE"})(Cr||(Cr={}));var ap=function(n){return n},sp="beforeunload",Zw="popstate";function qw(n){n===void 0&&(n={});var o=n,l=o.window,a=l===void 0?document.defaultView:l,c=a.history;function d(){var Y=a.location,J=Y.pathname,le=Y.search,ge=Y.hash,ke=c.state||{};return[ke.idx,ap({pathname:J,search:le,hash:ge,state:ke.usr||null,key:ke.key||"default"})]}var f=null;function y(){if(f)A.call(f),f=null;else{var Y=Cr.Pop,J=d(),le=J[0],ge=J[1];if(A.length){if(le!=null){var ke=S-le;ke&&(f={action:Y,location:ge,retry:function(){te(ke*-1)}},te(ke))}}else j(Y)}}a.addEventListener(Zw,y);var h=Cr.Pop,m=d(),S=m[0],k=m[1],C=cp(),A=cp();S==null&&(S=0,c.replaceState(ta({},c.state,{idx:S}),""));function b(Y){return typeof Y=="string"?Y:Kw(Y)}function v(Y,J){return J===void 0&&(J=null),ap(ta({pathname:k.pathname,hash:"",search:""},typeof Y=="string"?Xw(Y):Y,{state:J,key:Qw()}))}function w(Y,J){return[{usr:Y.state,key:Y.key,idx:J},b(Y)]}function I(Y,J,le){return!A.length||(A.call({action:Y,location:J,retry:le}),!1)}function j(Y){h=Y;var J=d();S=J[0],k=J[1],C.call({action:h,location:k})}function B(Y,J){var le=Cr.Push,ge=v(Y,J);function ke(){B(Y,J)}if(I(le,ge,ke)){var U=w(ge,S+1),ne=U[0],re=U[1];try{c.pushState(ne,"",re)}catch{a.location.assign(re)}j(le)}}function q(Y,J){var le=Cr.Replace,ge=v(Y,J);function ke(){q(Y,J)}if(I(le,ge,ke)){var U=w(ge,S),ne=U[0],re=U[1];c.replaceState(ne,"",re),j(le)}}function te(Y){c.go(Y)}var X={get action(){return h},get location(){return k},createHref:b,push:B,replace:q,go:te,back:function(){te(-1)},forward:function(){te(1)},listen:function(J){return C.push(J)},block:function(J){var le=A.push(J);return A.length===1&&a.addEventListener(sp,up),function(){le(),A.length||a.removeEventListener(sp,up)}}};return X}function up(n){n.preventDefault(),n.returnValue=""}function cp(){var n=[];return{get length(){return n.length},push:function(l){return n.push(l),function(){n=n.filter(function(a){return a!==l})}},call:function(l){n.forEach(function(a){return a&&a(l)})}}}function Qw(){return Math.random().toString(36).substr(2,8)}function Kw(n){var o=n.pathname,l=o===void 0?"/":o,a=n.search,c=a===void 0?"":a,d=n.hash,f=d===void 0?"":d;return c&&c!=="?"&&(l+=c.charAt(0)==="?"?c:"?"+c),f&&f!=="#"&&(l+=f.charAt(0)==="#"?f:"#"+f),l}function Xw(n){var o={};if(n){var l=n.indexOf("#");l>=0&&(o.hash=n.substr(l),n=n.substr(0,l));var a=n.indexOf("?");a>=0&&(o.search=n.substr(a),n=n.substr(0,a)),n&&(o.pathname=n)}return o}const ec=qw(),Jw=()=>{ec.push(wl({}))},Fh=n=>{Object.keys(n).forEach(o=>{const l=n[o],a=he.addons[o]?he.addons[o].defaultState:"$$LADLE_unknown";l===a&&delete n[o]})},pu=n=>{if(!n.controlInitialized)return;const o=dn.parse(location.search),l={};Object.keys(o).forEach(c=>{c.startsWith("arg-")||(l[c]=o[c])});const a={...l,mode:n.mode,rtl:n.rtl,source:n.source,story:n.story,theme:n.theme,width:n.width,control:n.control};Fh(a),location.search!==wl(a)&&(sn(`Updating URL to ${wl(a)}`),ec.push(wl(a)))},wl=n=>{Fh(n);const o={};return Object.keys(n).forEach(l=>{l==="control"?Object.keys(n[l]).forEach(a=>{const c=n[l][a];if(c.type===Te.Action)return;let d=c.value,f=!1;d=encodeURI(typeof c.value=="string"?c.value:JSON.stringify(c.value));try{f=JSON.stringify(c.value)===JSON.stringify(c.defaultValue),!f&&JSON.stringify(d)!==JSON.stringify(c.defaultValue)&&(o[`arg-${a}`]=d)}catch{}}):o[l]=n[l]}),`?${dn.stringify(o)}`},jh=n=>n.isExpanded&&n.children&&n.children.length?jh(n.children[n.children.length-1]):n.id,Ih=(n,o,l)=>{for(let a=0;a<n.length;a++){if(n[a].id===o)return l;if(n[a].isExpanded&&n[a].children&&n[a].children.length){const c=Ih(n[a].children,o,n[a].id);if(c)return c}}return null},Ph=(n,o,l)=>{for(let a=0;a<n.length;a++){if(n[a].id===o)return a===0?l:jh(n[a-1]);if(n[a].isExpanded&&n[a].children&&n[a].children.length){const c=Ph(n[a].children,o,n[a].id);if(c)return c}}return null},Dh=(n,o)=>{for(let l=0;l<n.length;l++){if(n[l].id===o)return n[l].children;const a=Dh(n[l].children,o);if(a.length)return a}return[]},Mh=(n,o)=>n[0].isLinkable?n[0]:Mh(n[0].children),Bh=(n,o)=>{for(let l=0;l<n.length;l++){if(n[l].id===o&&n[l].isExpanded&&n[l].children&&n[l].children.length)return n[l].children[0].id;if(n[l].isExpanded&&n[l].children&&n[l].children.length){const a=Bh(n[l].children,o);if(a)return a}}return null},zh=(n,o,l)=>{for(let a=0;a<n.length;a++){if(n[a].id===o)return n[a].isExpanded&&n[a].children&&n[a].children.length?n[a].children[0].id:n[a+1]?n[a+1].id:l;if(n[a].isExpanded&&n[a].children&&n[a].children.length){const c=zh(n[a].children,o,n[a+1]?n[a+1].id:l);if(c)return c}}return null},Uh=n=>{const o=n[n.length-1];return o.isExpanded&&o.children&&o.children.length?Uh(o.children):o.id},vl=(n,o)=>n.map((l,a)=>{const c={...l};return c.id===o.id&&(c.isExpanded=!c.isExpanded),o.id==="+"&&a===0&&(c.isExpanded=!0),o.id==="-"&&(c.isExpanded=!1),c.children&&c.children.length&&(c.children=vl(c.children,c.id===o.id?{id:c.isExpanded?"+":"-"}:o)),c});function e1(n,o){let l;return function(...a){l!==void 0&&clearTimeout(l),l=window.setTimeout(()=>{n.apply(this,a),l=void 0},o)}}const t1=({stories:n,story:o,updateStory:l,allExpanded:a,searchRef:c,setTreeRootRef:d,hotkeys:f})=>{const y=N.useRef({}),[h,m]=N.useState(Xs(n,o,a));N.useEffect(()=>{m(Xs(n,o,a))},[n.join(",")]);const[S,k]=N.useState(h.length?h[0].id:null),C=v=>{var w;v&&y&&y.current[v]&&((w=y.current[v])==null||w.focus()),k(v||h[0].id),!v&&c.current.focus()},A=v=>{v&&(l(v),m(Xs(n,v,a)),setTimeout(()=>C(v),1))};$t(he.hotkeys.nextStory,()=>{const v=n.findIndex(w=>w===o);A(n[v+1])},{preventDefault:!0,enableOnFormTags:!0,enabled:f}),$t(he.hotkeys.previousStory,()=>{const v=n.findIndex(w=>w===o);A(n[v-1])},{preventDefault:!0,enableOnFormTags:!0,enabled:f}),$t(he.hotkeys.nextComponent,()=>{const v=n.findIndex(j=>j===o),w=n[v].split("--"),I=w[w.length-2];for(let j=v+1;j<n.length;j++){const B=n[j].split("--");if(B[B.length-2]!==I){A(n[j]);return}}},{preventDefault:!0,enableOnFormTags:!0,enabled:f}),$t(he.hotkeys.previousComponent,()=>{const v=n.findIndex(j=>j===o),w=n[v].split("--"),I=w[w.length-2];for(let j=v-1;j>=0;j--){const B=n[j].split("--"),q=j>0?n[j-1].split("--"):["",""];if(B[B.length-2]!==I&&q[q.length-2]!==B[B.length-2]){A(n[j]);return}}},{preventDefault:!0,enableOnFormTags:!0,enabled:f});const b=(v,w)=>{if(!(v.metaKey||v.ctrlKey||v.altKey))switch(v.key){case"ArrowRight":v.preventDefault(),v.stopPropagation(),w.isExpanded?C(Bh(h,w.id)):m(vl(h,w));break;case"ArrowLeft":v.preventDefault(),v.stopPropagation(),w.isExpanded?m(vl(h,w)):C(Ih(h,w.id,null));break;case"ArrowUp":v.preventDefault(),v.stopPropagation(),C(Ph(h,w.id,null));break;case"ArrowDown":{v.preventDefault(),v.stopPropagation();const I=zh(h,w.id,null);I&&C(I);break}case" ":case"Enter":v.target.href||(v.preventDefault(),v.stopPropagation(),m(vl(h,w)));break;case"Home":v.preventDefault(),v.stopPropagation(),h.length&&C(h[0].id);break;case"End":v.preventDefault(),v.stopPropagation(),C(Uh(h));break}};return g.jsx("ul",{role:"tree",style:{marginInlineStart:"-6px"},ref:v=>d(v),children:g.jsx($h,{tree:h,fullTree:h,story:o,updateStory:l,onItemClick:v=>{const w=vl(h,v),I=Mh(Dh(w,v.id),v.id);I&&o!==I.id&&I.isExpanded&&l(I.id),m(w)},selectedItemId:S,onKeyDownFn:b,treeItemRefs:y})})},$h=({tree:n,fullTree:o,story:l,updateStory:a,onItemClick:c,onKeyDownFn:d,selectedItemId:f,treeItemRefs:y})=>g.jsx(N.Fragment,{children:n.map(h=>g.jsxs("li",{onDragStart:m=>m.preventDefault(),onKeyDown:m=>d(m,h),"aria-expanded":h.isExpanded,title:h.name,tabIndex:h.id===f&&!h.isLinkable?0:-1,ref:h.isLinkable?void 0:m=>y.current[h.id]=m,role:"treeitem",className:Lh({"ladle-linkable":h.isLinkable,"ladle-active":h.id===l}),style:h.isLinkable?{}:{marginTop:"0.5em"},children:[h.isLinkable?g.jsxs("div",{style:{display:"flex"},children:[g.jsx(Em,{}),g.jsx("a",{tabIndex:h.id===f?0:-1,ref:m=>y.current[h.id]=m,href:wl({story:h.id}),onKeyDown:m=>l!==h.id&&d(m,h),onClick:m=>{!m.ctrlKey&&!m.metaKey&&(m.preventDefault(),l!==h.id&&a(h.id))},children:h.name})]}):g.jsxs("div",{style:{display:"flex",cursor:"pointer"},title:h.name,onClick:()=>c(h),children:[g.jsx(xm,{rotate:!h.isExpanded}),g.jsx("div",{style:{textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"},children:h.name})]}),Object.keys(h.children).length>0&&h.isExpanded&&g.jsx("ul",{role:"group",children:g.jsx($h,{tree:h.children,fullTree:o,story:l,updateStory:a,selectedItemId:f,onKeyDownFn:d,onItemClick:c,treeItemRefs:y})})]},h.id))}),Hh="583a85",Pu=`ladle-settings-${Hh}`,Vh={appId:Hh},n1=n=>{const o=localStorage.getItem(Pu);let l=Vh;try{o&&(l=JSON.parse(o))}catch{}localStorage.setItem(Pu,JSON.stringify({...l,...n}))},r1=()=>{const n=localStorage.getItem(Pu);let o=Vh;try{n&&(o=JSON.parse(n))}catch{}return o},o1=240,hu=192,yu=920,gu=e1(n1,250),l1=({stories:n,story:o,updateStory:l,hotkeys:a,search:c,setSearch:d})=>{const[f,y]=N.useState(r1().sidebarWidth||o1),[h,m]=N.useState(!1),S=N.useRef(null),k=N.useRef(null),C=N.useRef(null);N.useEffect(()=>{window.getComputedStyle(S.current.parentElement).getPropertyValue("flex-direction")==="row-reverse"&&document.documentElement.setAttribute("data-reversed","")},[]),N.useEffect(()=>{const w=j=>{h&&y(B=>{const q=document.documentElement.hasAttribute("data-reversed")?B+j.movementX:B-j.movementX;return q<hu?(gu({sidebarWidth:hu}),hu):q>yu?(gu({sidebarWidth:yu}),yu):(gu({sidebarWidth:q}),q)})},I=()=>{h&&(document.body.style.cursor="auto",m(!1))};return window.addEventListener("mousemove",w),window.addEventListener("mouseup",I),()=>{window.removeEventListener("mousemove",w),window.removeEventListener("mouseup",I)}},[h,m,y,S.current]),$t(he.hotkeys.search,()=>{var w;return(w=k.current)==null?void 0:w.focus()},{preventDefault:!0,enabled:a});const A=c.toLocaleLowerCase().replace(new RegExp("\\s+","g"),"-"),b=n.filter(w=>w.includes(A)),v=w=>{var I;w.key==="ArrowDown"&&((I=C.current)!=null&&I.firstChild)&&C.current.firstChild.focus()};return g.jsxs(g.Fragment,{children:[g.jsx("div",{role:"separator","aria-orientation":"vertical",ref:S,className:Lh("ladle-resize-handle",{"ladle-resize-active":h}),onDragStart:w=>w.preventDefault(),onDragEnd:w=>w.preventDefault(),onDrop:w=>w.preventDefault(),onDragOver:w=>w.preventDefault(),onDragEnter:w=>w.preventDefault(),onDragLeave:w=>w.preventDefault(),onMouseDown:w=>{w.preventDefault(),h||(document.body.style.cursor="col-resize",m(!0))}}),g.jsxs("nav",{role:"navigation",className:"ladle-aside",style:{minWidth:`${f}px`},children:[g.jsx("input",{placeholder:"Search","aria-label":"Search stories",value:c,ref:k,onKeyDown:v,onChange:w=>d(w.target.value)}),g.jsx(t1,{searchRef:k,stories:b,story:o,hotkeys:a,updateStory:l,allExpanded:c!==""||he.expandStoryTree,setTreeRootRef:w=>C.current=w})]})]})},i1=n=>{switch(dn.parse(n).mode){case ht.Full:return ht.Full;case ht.Preview:return ht.Preview;default:return he.addons.mode.defaultState}},a1=({dispatch:n})=>{const o=`Open fullscreen mode. Can be toggled by pressing ${he.hotkeys.fullscreen.join(" or ")}.`;return g.jsx("li",{children:g.jsxs("button",{"aria-label":o,title:o,onClick:()=>n({type:Re.UpdateMode,value:ht.Preview}),type:"button",children:[g.jsx(vm,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:o}),g.jsx("label",{children:"Open fullscreen mode"})]})})};var s1=Object.create,tc=Object.defineProperty,u1=Object.getOwnPropertyDescriptor,Wh=Object.getOwnPropertyNames,c1=Object.getPrototypeOf,d1=Object.prototype.hasOwnProperty,nc=(n,o)=>function(){return o||(0,n[Wh(n)[0]])((o={exports:{}}).exports,o),o.exports},f1=(n,o)=>{for(var l in o)tc(n,l,{get:o[l],enumerable:!0})},p1=(n,o,l,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let c of Wh(o))!d1.call(n,c)&&c!==l&&tc(n,c,{get:()=>o[c],enumerable:!(a=u1(o,c))||a.enumerable});return n},h1=(n,o,l)=>(l=n!=null?s1(c1(n)):{},p1(!n||!n.__esModule?tc(l,"default",{value:n,enumerable:!0}):l,n)),y1=nc({"node_modules/is-object/index.js"(n,o){o.exports=function(a){return typeof a=="object"&&a!==null}}}),g1=nc({"node_modules/is-window/index.js"(n,o){o.exports=function(l){if(l==null)return!1;var a=Object(l);return a===a.window}}}),m1=nc({"node_modules/is-dom/index.js"(n,o){var l=y1(),a=g1();function c(d){return!l(d)||!a(window)||typeof window.Node!="function"?!1:typeof d.nodeType=="number"&&typeof d.nodeName=="string"}o.exports=c}}),na={};f1(na,{chromeDark:()=>Gh,chromeLight:()=>Yh});var Gh={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"rgb(36, 36, 36)",BASE_COLOR:"rgb(213, 213, 213)",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(227, 110, 236)",OBJECT_VALUE_NULL_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_REGEXP_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_STRING_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_NUMBER_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_BOOLEAN_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(85, 106, 242)",HTML_TAG_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(155, 187, 220)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(242, 151, 102)",HTML_COMMENT_COLOR:"rgb(137, 137, 137)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"rgb(145, 145, 145)",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"rgb(85, 85, 85)",TABLE_TH_BACKGROUND_COLOR:"rgb(44, 44, 44)",TABLE_TH_HOVER_COLOR:"rgb(48, 48, 48)",TABLE_SORT_ICON_COLOR:"black",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 50%, rgba(51, 139, 255, 0.0980392) 50%, rgba(51, 139, 255, 0.0980392))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},Yh={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"white",BASE_COLOR:"black",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(136, 19, 145)",OBJECT_VALUE_NULL_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_REGEXP_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_STRING_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_NUMBER_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_BOOLEAN_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(13, 34, 170)",HTML_TAG_COLOR:"rgb(168, 148, 166)",HTML_TAGNAME_COLOR:"rgb(136, 18, 128)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(153, 69, 0)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(26, 26, 166)",HTML_COMMENT_COLOR:"rgb(35, 110, 37)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"#6e6e6e",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"#aaa",TABLE_TH_BACKGROUND_COLOR:"#eee",TABLE_TH_HOVER_COLOR:"hsla(0, 0%, 90%, 1)",TABLE_SORT_ICON_COLOR:"#6e6e6e",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(to bottom, white, white 50%, rgb(234, 243, 255) 50%, rgb(234, 243, 255))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},Zh=N.createContext([{},()=>{}]),mu={WebkitTouchCallout:"none",WebkitUserSelect:"none",KhtmlUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",OUserSelect:"none",userSelect:"none"},Gi=n=>({DOMNodePreview:{htmlOpenTag:{base:{color:n.HTML_TAG_COLOR},tagName:{color:n.HTML_TAGNAME_COLOR,textTransform:n.HTML_TAGNAME_TEXT_TRANSFORM},htmlAttributeName:{color:n.HTML_ATTRIBUTE_NAME_COLOR},htmlAttributeValue:{color:n.HTML_ATTRIBUTE_VALUE_COLOR}},htmlCloseTag:{base:{color:n.HTML_TAG_COLOR},offsetLeft:{marginLeft:-n.TREENODE_PADDING_LEFT},tagName:{color:n.HTML_TAGNAME_COLOR,textTransform:n.HTML_TAGNAME_TEXT_TRANSFORM}},htmlComment:{color:n.HTML_COMMENT_COLOR},htmlDoctype:{color:n.HTML_DOCTYPE_COLOR}},ObjectPreview:{objectDescription:{fontStyle:"italic"},preview:{fontStyle:"italic"},arrayMaxProperties:n.OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES,objectMaxProperties:n.OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES},ObjectName:{base:{color:n.OBJECT_NAME_COLOR},dimmed:{opacity:.6}},ObjectValue:{objectValueNull:{color:n.OBJECT_VALUE_NULL_COLOR},objectValueUndefined:{color:n.OBJECT_VALUE_UNDEFINED_COLOR},objectValueRegExp:{color:n.OBJECT_VALUE_REGEXP_COLOR},objectValueString:{color:n.OBJECT_VALUE_STRING_COLOR},objectValueSymbol:{color:n.OBJECT_VALUE_SYMBOL_COLOR},objectValueNumber:{color:n.OBJECT_VALUE_NUMBER_COLOR},objectValueBoolean:{color:n.OBJECT_VALUE_BOOLEAN_COLOR},objectValueFunctionPrefix:{color:n.OBJECT_VALUE_FUNCTION_PREFIX_COLOR,fontStyle:"italic"},objectValueFunctionName:{fontStyle:"italic"}},TreeView:{treeViewOutline:{padding:0,margin:0,listStyleType:"none"}},TreeNode:{treeNodeBase:{color:n.BASE_COLOR,backgroundColor:n.BASE_BACKGROUND_COLOR,lineHeight:n.TREENODE_LINE_HEIGHT,cursor:"default",boxSizing:"border-box",listStyle:"none",fontFamily:n.TREENODE_FONT_FAMILY,fontSize:n.TREENODE_FONT_SIZE},treeNodePreviewContainer:{},treeNodePlaceholder:{whiteSpace:"pre",fontSize:n.ARROW_FONT_SIZE,marginRight:n.ARROW_MARGIN_RIGHT,...mu},treeNodeArrow:{base:{color:n.ARROW_COLOR,display:"inline-block",fontSize:n.ARROW_FONT_SIZE,marginRight:n.ARROW_MARGIN_RIGHT,...parseFloat(n.ARROW_ANIMATION_DURATION)>0?{transition:`transform ${n.ARROW_ANIMATION_DURATION} ease 0s`}:{},...mu},expanded:{WebkitTransform:"rotateZ(90deg)",MozTransform:"rotateZ(90deg)",transform:"rotateZ(90deg)"},collapsed:{WebkitTransform:"rotateZ(0deg)",MozTransform:"rotateZ(0deg)",transform:"rotateZ(0deg)"}},treeNodeChildNodesContainer:{margin:0,paddingLeft:n.TREENODE_PADDING_LEFT}},TableInspector:{base:{color:n.BASE_COLOR,position:"relative",border:`1px solid ${n.TABLE_BORDER_COLOR}`,fontFamily:n.BASE_FONT_FAMILY,fontSize:n.BASE_FONT_SIZE,lineHeight:"120%",boxSizing:"border-box",cursor:"default"}},TableInspectorHeaderContainer:{base:{top:0,height:"17px",left:0,right:0,overflowX:"hidden"},table:{tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",height:"100%",width:"100%",margin:0}},TableInspectorDataContainer:{tr:{display:"table-row"},td:{boxSizing:"border-box",border:"none",height:"16px",verticalAlign:"top",padding:"1px 4px",WebkitUserSelect:"text",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px"},div:{position:"static",top:"17px",bottom:0,overflowY:"overlay",transform:"translateZ(0)",left:0,right:0,overflowX:"hidden"},table:{positon:"static",left:0,top:0,right:0,bottom:0,borderTop:"0 none transparent",margin:0,backgroundImage:n.TABLE_DATA_BACKGROUND_IMAGE,backgroundSize:n.TABLE_DATA_BACKGROUND_SIZE,tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",width:"100%",fontSize:n.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorTH:{base:{position:"relative",height:"auto",textAlign:"left",backgroundColor:n.TABLE_TH_BACKGROUND_COLOR,borderBottom:`1px solid ${n.TABLE_BORDER_COLOR}`,fontWeight:"normal",verticalAlign:"middle",padding:"0 4px",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px",":hover":{backgroundColor:n.TABLE_TH_HOVER_COLOR}},div:{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",fontSize:n.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorLeftBorder:{none:{borderLeft:"none"},solid:{borderLeft:`1px solid ${n.TABLE_BORDER_COLOR}`}},TableInspectorSortIcon:{display:"block",marginRight:3,width:8,height:7,marginTop:-7,color:n.TABLE_SORT_ICON_COLOR,fontSize:12,...mu}}),Du="chromeLight",qh=N.createContext(Gi(na[Du])),Ot=n=>N.useContext(qh)[n],rc=n=>({theme:l=Du,...a})=>{const c=N.useMemo(()=>{switch(Object.prototype.toString.call(l)){case"[object String]":return Gi(na[l]);case"[object Object]":return Gi(l);default:return Gi(na[Du])}},[l]);return $.createElement(qh.Provider,{value:c},$.createElement(n,{...a}))},v1=({expanded:n,styles:o})=>$.createElement("span",{style:{...o.base,...n?o.expanded:o.collapsed}},"▶"),w1=N.memo(n=>{n={expanded:!0,nodeRenderer:({name:S})=>$.createElement("span",null,S),onClick:()=>{},shouldShowArrow:!1,shouldShowPlaceholder:!0,...n};const{expanded:o,onClick:l,children:a,nodeRenderer:c,title:d,shouldShowArrow:f,shouldShowPlaceholder:y}=n,h=Ot("TreeNode"),m=c;return $.createElement("li",{"aria-expanded":o,role:"treeitem",style:h.treeNodeBase,title:d},$.createElement("div",{style:h.treeNodePreviewContainer,onClick:l},f||N.Children.count(a)>0?$.createElement(v1,{expanded:o,styles:h.treeNodeArrow}):y&&$.createElement("span",{style:h.treeNodePlaceholder}," "),$.createElement(m,{...n})),$.createElement("ol",{role:"group",style:h.treeNodeChildNodesContainer},o?a:void 0))}),ra="$",dp="*";function Yi(n,o){return!o(n).next().done}var E1=n=>Array.from({length:n},(o,l)=>[ra].concat(Array.from({length:l},()=>"*")).join(".")),x1=(n,o,l,a,c)=>{const d=[].concat(E1(a)).concat(l).filter(y=>typeof y=="string"),f=[];return d.forEach(y=>{const h=y.split("."),m=(S,k,C)=>{if(C===h.length){f.push(k);return}const A=h[C];if(C===0)Yi(S,o)&&(A===ra||A===dp)&&m(S,ra,C+1);else if(A===dp)for(const{name:b,data:v}of o(S))Yi(v,o)&&m(v,`${k}.${b}`,C+1);else{const b=S[A];Yi(b,o)&&m(b,`${k}.${A}`,C+1)}};m(n,"",0)}),f.reduce((y,h)=>(y[h]=!0,y),{...c})},Qh=N.memo(n=>{const{data:o,dataIterator:l,path:a,depth:c,nodeRenderer:d}=n,[f,y]=N.useContext(Zh),h=Yi(o,l),m=!!f[a],S=N.useCallback(()=>h&&y(k=>({...k,[a]:!m})),[h,y,a,m]);return $.createElement(w1,{expanded:m,onClick:S,shouldShowArrow:h,shouldShowPlaceholder:c>0,nodeRenderer:d,...n},m?[...l(o)].map(({name:k,data:C,...A})=>$.createElement(Qh,{name:k,data:C,depth:c+1,path:`${a}.${k}`,key:k,dataIterator:l,nodeRenderer:d,...A})):null)}),Kh=N.memo(({name:n,data:o,dataIterator:l,nodeRenderer:a,expandPaths:c,expandLevel:d})=>{const f=Ot("TreeView"),y=N.useState({}),[,h]=y;return N.useLayoutEffect(()=>h(m=>x1(o,l,c,d,m)),[o,l,c,d]),$.createElement(Zh.Provider,{value:y},$.createElement("ol",{role:"tree",style:f.treeViewOutline},$.createElement(Qh,{name:n,data:o,dataIterator:l,depth:0,path:ra,nodeRenderer:a})))}),oc=({name:n,dimmed:o=!1,styles:l={}})=>{const a=Ot("ObjectName"),c={...a.base,...o?a.dimmed:{},...l};return $.createElement("span",{style:c},n)},El=({object:n,styles:o})=>{const l=Ot("ObjectValue"),a=c=>({...l[c],...o});switch(typeof n){case"bigint":return $.createElement("span",{style:a("objectValueNumber")},String(n),"n");case"number":return $.createElement("span",{style:a("objectValueNumber")},String(n));case"string":return $.createElement("span",{style:a("objectValueString")},'"',n,'"');case"boolean":return $.createElement("span",{style:a("objectValueBoolean")},String(n));case"undefined":return $.createElement("span",{style:a("objectValueUndefined")},"undefined");case"object":return n===null?$.createElement("span",{style:a("objectValueNull")},"null"):n instanceof Date?$.createElement("span",null,n.toString()):n instanceof RegExp?$.createElement("span",{style:a("objectValueRegExp")},n.toString()):Array.isArray(n)?$.createElement("span",null,`Array(${n.length})`):n.constructor?typeof n.constructor.isBuffer=="function"&&n.constructor.isBuffer(n)?$.createElement("span",null,`Buffer[${n.length}]`):$.createElement("span",null,n.constructor.name):$.createElement("span",null,"Object");case"function":return $.createElement("span",null,$.createElement("span",{style:a("objectValueFunctionPrefix")},"ƒ "),$.createElement("span",{style:a("objectValueFunctionName")},n.name,"()"));case"symbol":return $.createElement("span",{style:a("objectValueSymbol")},n.toString());default:return $.createElement("span",null)}},Xh=Object.prototype.hasOwnProperty,k1=Object.prototype.propertyIsEnumerable;function Mu(n,o){const l=Object.getOwnPropertyDescriptor(n,o);if(l.get)try{return l.get()}catch{return l.get}return n[o]}function fp(n,o){return n.length===0?[]:n.slice(1).reduce((l,a)=>l.concat([o,a]),[n[0]])}var Bu=({data:n})=>{const o=Ot("ObjectPreview"),l=n;if(typeof l!="object"||l===null||l instanceof Date||l instanceof RegExp)return $.createElement(El,{object:l});if(Array.isArray(l)){const a=o.arrayMaxProperties,c=l.slice(0,a).map((f,y)=>$.createElement(El,{key:y,object:f}));l.length>a&&c.push($.createElement("span",{key:"ellipsis"},"…"));const d=l.length;return $.createElement($.Fragment,null,$.createElement("span",{style:o.objectDescription},d===0?"":`(${d}) `),$.createElement("span",{style:o.preview},"[",fp(c,", "),"]"))}else{const a=o.objectMaxProperties,c=[];for(const f in l)if(Xh.call(l,f)){let y;c.length===a-1&&Object.keys(l).length>a&&(y=$.createElement("span",{key:"ellipsis"},"…"));const h=Mu(l,f);if(c.push($.createElement("span",{key:f},$.createElement(oc,{name:f||'""'}),": ",$.createElement(El,{object:h}),y)),y)break}const d=l.constructor?l.constructor.name:"Object";return $.createElement($.Fragment,null,$.createElement("span",{style:o.objectDescription},d==="Object"?"":`${d} `),$.createElement("span",{style:o.preview},"{",fp(c,", "),"}"))}},S1=({name:n,data:o})=>typeof n=="string"?$.createElement("span",null,$.createElement(oc,{name:n}),$.createElement("span",null,": "),$.createElement(Bu,{data:o})):$.createElement(Bu,{data:o}),b1=({name:n,data:o,isNonenumerable:l=!1})=>{const a=o;return $.createElement("span",null,typeof n=="string"?$.createElement(oc,{name:n,dimmed:l}):$.createElement(Bu,{data:n}),$.createElement("span",null,": "),$.createElement(El,{object:a}))},_1=(n,o)=>function*(a){if(!(typeof a=="object"&&a!==null||typeof a=="function"))return;const d=Array.isArray(a);if(!d&&a[Symbol.iterator]){let f=0;for(const y of a){if(Array.isArray(y)&&y.length===2){const[h,m]=y;yield{name:h,data:m}}else yield{name:f.toString(),data:y};f++}}else{const f=Object.getOwnPropertyNames(a);o===!0&&!d?f.sort():typeof o=="function"&&f.sort(o);for(const y of f)if(k1.call(a,y)){const h=Mu(a,y);yield{name:y||'""',data:h}}else if(n){let h;try{h=Mu(a,y)}catch{}h!==void 0&&(yield{name:y,data:h,isNonenumerable:!0})}n&&a!==Object.prototype&&(yield{name:"__proto__",data:Object.getPrototypeOf(a),isNonenumerable:!0})}},C1=({depth:n,name:o,data:l,isNonenumerable:a})=>n===0?$.createElement(S1,{name:o,data:l}):$.createElement(b1,{name:o,data:l,isNonenumerable:a}),O1=({showNonenumerable:n=!1,sortObjectKeys:o,nodeRenderer:l,...a})=>{const c=_1(n,o),d=l||C1;return $.createElement(Kh,{nodeRenderer:d,dataIterator:c,...a})},T1=rc(O1);function A1(n){if(typeof n=="object"){let o=[];if(Array.isArray(n)){const a=n.length;o=[...Array(a).keys()]}else n!==null&&(o=Object.keys(n));const l=o.reduce((a,c)=>{const d=n[c];return typeof d=="object"&&d!==null&&Object.keys(d).reduce((y,h)=>(y.includes(h)||y.push(h),y),a),a},[]);return{rowHeaders:o,colHeaders:l}}}var R1=({rows:n,columns:o,rowsData:l})=>{const a=Ot("TableInspectorDataContainer"),c=Ot("TableInspectorLeftBorder");return $.createElement("div",{style:a.div},$.createElement("table",{style:a.table},$.createElement("colgroup",null),$.createElement("tbody",null,n.map((d,f)=>$.createElement("tr",{key:d,style:a.tr},$.createElement("td",{style:{...a.td,...c.none}},d),o.map(y=>{const h=l[f];return typeof h=="object"&&h!==null&&Xh.call(h,y)?$.createElement("td",{key:y,style:{...a.td,...c.solid}},$.createElement(El,{object:h[y]})):$.createElement("td",{key:y,style:{...a.td,...c.solid}})}))))))},N1=n=>$.createElement("div",{style:{position:"absolute",top:1,right:0,bottom:1,display:"flex",alignItems:"center"}},n.children),L1=({sortAscending:n})=>{const o=Ot("TableInspectorSortIcon"),l=n?"▲":"▼";return $.createElement("div",{style:o},l)},pp=({sortAscending:n=!1,sorted:o=!1,onClick:l=void 0,borderStyle:a={},children:c,...d})=>{const f=Ot("TableInspectorTH"),[y,h]=N.useState(!1),m=N.useCallback(()=>h(!0),[]),S=N.useCallback(()=>h(!1),[]);return $.createElement("th",{...d,style:{...f.base,...a,...y?f.base[":hover"]:{}},onMouseEnter:m,onMouseLeave:S,onClick:l},$.createElement("div",{style:f.div},c),o&&$.createElement(N1,null,$.createElement(L1,{sortAscending:n})))},F1=({indexColumnText:n="(index)",columns:o=[],sorted:l,sortIndexColumn:a,sortColumn:c,sortAscending:d,onTHClick:f,onIndexTHClick:y})=>{const h=Ot("TableInspectorHeaderContainer"),m=Ot("TableInspectorLeftBorder");return $.createElement("div",{style:h.base},$.createElement("table",{style:h.table},$.createElement("tbody",null,$.createElement("tr",null,$.createElement(pp,{borderStyle:m.none,sorted:l&&a,sortAscending:d,onClick:y},n),o.map(S=>$.createElement(pp,{borderStyle:m.solid,key:S,sorted:l&&c===S,sortAscending:d,onClick:f.bind(null,S)},S))))))},j1=({data:n,columns:o})=>{const l=Ot("TableInspector"),[{sorted:a,sortIndexColumn:c,sortColumn:d,sortAscending:f},y]=N.useState({sorted:!1,sortIndexColumn:!1,sortColumn:void 0,sortAscending:!1}),h=N.useCallback(()=>{y(({sortIndexColumn:b,sortAscending:v})=>({sorted:!0,sortIndexColumn:!0,sortColumn:void 0,sortAscending:b?!v:!0}))},[]),m=N.useCallback(b=>{y(({sortColumn:v,sortAscending:w})=>({sorted:!0,sortIndexColumn:!1,sortColumn:b,sortAscending:b===v?!w:!0}))},[]);if(typeof n!="object"||n===null)return $.createElement("div",null);let{rowHeaders:S,colHeaders:k}=A1(n);o!==void 0&&(k=o);let C=S.map(b=>n[b]),A;if(d!==void 0?A=C.map((b,v)=>typeof b=="object"&&b!==null?[b[d],v]:[void 0,v]):c&&(A=S.map((b,v)=>[S[v],v])),A!==void 0){const b=(w,I)=>(j,B)=>{const q=w(j),te=w(B),X=typeof q,Y=typeof te,J=(ge,ke)=>ge<ke?-1:ge>ke?1:0;let le;if(X===Y)le=J(q,te);else{const ge={string:0,number:1,object:2,symbol:3,boolean:4,undefined:5,function:6};le=J(ge[X],ge[Y])}return I||(le=-le),le},v=A.sort(b(w=>w[0],f)).map(w=>w[1]);S=v.map(w=>S[w]),C=v.map(w=>C[w])}return $.createElement("div",{style:l.base},$.createElement(F1,{columns:k,sorted:a,sortIndexColumn:c,sortColumn:d,sortAscending:f,onTHClick:m,onIndexTHClick:h}),$.createElement(R1,{rows:S,columns:k,rowsData:C}))},I1=rc(j1),P1=80,Jh=n=>n.childNodes.length===0||n.childNodes.length===1&&n.childNodes[0].nodeType===Node.TEXT_NODE&&n.textContent.length<P1,D1=({tagName:n,attributes:o,styles:l})=>$.createElement("span",{style:l.base},"<",$.createElement("span",{style:l.tagName},n),(()=>{if(o){const a=[];for(let c=0;c<o.length;c++){const d=o[c];a.push($.createElement("span",{key:c}," ",$.createElement("span",{style:l.htmlAttributeName},d.name),'="',$.createElement("span",{style:l.htmlAttributeValue},d.value),'"'))}return a}})(),">"),hp=({tagName:n,isChildNode:o=!1,styles:l})=>$.createElement("span",{style:Object.assign({},l.base,o&&l.offsetLeft)},"</",$.createElement("span",{style:l.tagName},n),">"),M1={1:"ELEMENT_NODE",3:"TEXT_NODE",7:"PROCESSING_INSTRUCTION_NODE",8:"COMMENT_NODE",9:"DOCUMENT_NODE",10:"DOCUMENT_TYPE_NODE",11:"DOCUMENT_FRAGMENT_NODE"},B1=({isCloseTag:n,data:o,expanded:l})=>{const a=Ot("DOMNodePreview");if(n)return $.createElement(hp,{styles:a.htmlCloseTag,isChildNode:!0,tagName:o.tagName});switch(o.nodeType){case Node.ELEMENT_NODE:return $.createElement("span",null,$.createElement(D1,{tagName:o.tagName,attributes:o.attributes,styles:a.htmlOpenTag}),Jh(o)?o.textContent:!l&&"…",!l&&$.createElement(hp,{tagName:o.tagName,styles:a.htmlCloseTag}));case Node.TEXT_NODE:return $.createElement("span",null,o.textContent);case Node.CDATA_SECTION_NODE:return $.createElement("span",null,"<![CDATA["+o.textContent+"]]>");case Node.COMMENT_NODE:return $.createElement("span",{style:a.htmlComment},"<!--",o.textContent,"-->");case Node.PROCESSING_INSTRUCTION_NODE:return $.createElement("span",null,o.nodeName);case Node.DOCUMENT_TYPE_NODE:return $.createElement("span",{style:a.htmlDoctype},"<!DOCTYPE ",o.name,o.publicId?` PUBLIC "${o.publicId}"`:"",!o.publicId&&o.systemId?" SYSTEM":"",o.systemId?` "${o.systemId}"`:"",">");case Node.DOCUMENT_NODE:return $.createElement("span",null,o.nodeName);case Node.DOCUMENT_FRAGMENT_NODE:return $.createElement("span",null,o.nodeName);default:return $.createElement("span",null,M1[o.nodeType])}},z1=function*(n){if(n&&n.childNodes){if(Jh(n))return;for(let l=0;l<n.childNodes.length;l++){const a=n.childNodes[l];a.nodeType===Node.TEXT_NODE&&a.textContent.trim().length===0||(yield{name:`${a.tagName}[${l}]`,data:a})}n.tagName&&(yield{name:"CLOSE_TAG",data:{tagName:n.tagName},isCloseTag:!0})}},U1=n=>$.createElement(Kh,{nodeRenderer:B1,dataIterator:z1,...n}),$1=rc(U1),H1=h1(m1()),V1=({table:n=!1,data:o,...l})=>n?$.createElement(I1,{data:o,...l}):(0,H1.default)(o)?$.createElement($1,{data:o,...l}):$.createElement(T1,{data:o,...l});const W1=({dispatch:n,globalState:o})=>{const[l,a]=N.useState(!1),c="Log of events triggered by user.";return g.jsx("li",{children:g.jsxs("button",{"aria-label":c,title:c,onClick:()=>a(!0),className:l?"ladle-active":"","data-testid":"addon-action",type:"button",children:[g.jsx(Cm,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:c}),g.jsx("label",{children:"Actions"}),o.action.length?g.jsx("div",{className:"ladle-badge",children:o.action.length}):null,g.jsxs(So,{maxWidth:"60em",isOpen:l,close:()=>a(!1),label:"Dialog with a log of events triggered by user.",children:[o.action.map((d,f)=>g.jsx(V1,{table:!1,sortObjectKeys:!0,theme:{...o.theme===Ze.Light?Yh:Gh,BASE_BACKGROUND_COLOR:"var(--ladle-bg-color-secondary)"},showNonenumerable:!1,name:d.name,data:d.event},f)),g.jsx("button",{onClick:()=>{n({type:Re.UpdateAction,clear:!0,value:void 0})},type:"button",children:"Clear actions"})]})]})})},G1=n=>{const o=dn.parse(n).rtl;return o==="true"?!0:o==="false"?!1:he.addons.rtl.defaultState},Y1=({dispatch:n,globalState:o})=>{const l="Switch text direction to right to left.",a="Switch text direction to left to right.";return $t(he.hotkeys.rtl,()=>n({type:Re.UpdateRtl,value:!o.rtl}),{enabled:o.hotkeys&&he.addons.rtl.enabled}),g.jsx("li",{children:g.jsxs("button",{"aria-label":o.rtl?a:l,title:o.rtl?a:l,className:o.rtl?"ladle-active":"",onClick:()=>n({type:Re.UpdateRtl,value:!o.rtl}),type:"button",children:[g.jsx(gm,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:o.rtl?a:l}),g.jsx("label",{children:"Right to left"})]})})},Z1={fullscreen:"Toggle fullscreen mode",search:"Focus search input in the sidebar",nextStory:"Go to the next story",previousStory:"Go to the previous story",nextComponent:"Go to the next component",previousComponent:"Go to the previous component",control:"Toggle controls addon",darkMode:"Toggle dark mode",width:"Toggle width addon",rtl:"Toggle right-to-left mode",a11y:"Toggle accessibility addon",source:"Toggle story source addon"},q1=({children:n})=>(navigator.platform.toLowerCase().includes("mac")?n=n.replace(/alt/g,"⌥ opt").replace(/meta/g,"⌘ cmd"):navigator.platform.toLowerCase().includes("win")&&(n=n.replace(/meta/g,"⊞ win")),n=n.replace(/shift/g,"⇧ shift"),n=n.replace(/arrowright/g,"→").replace(/arrowleft/g,"←").replace(/arrowup/g,"↑").replace(/arrowdown/g,"↓").replace(/\+/g," ＋ "),g.jsx(Or,{children:n})),Q1=({globalState:n})=>{const[o,l]=N.useState(!1),a="Get more information about Ladle.";return g.jsx("li",{children:g.jsxs("button",{"aria-label":a,title:a,onClick:()=>l(!0),className:o?"ladle-active":"",type:"button",children:[g.jsx(ym,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:a}),g.jsx("label",{children:"About Ladle"}),g.jsxs(So,{isOpen:o,close:()=>l(!1),label:"Dialog with information about Ladle.",children:[g.jsx("h3",{children:"Hotkeys"}),n.hotkeys?g.jsxs(g.Fragment,{children:[g.jsx("ul",{style:{listStyle:"none",marginLeft:0,paddingLeft:0},children:Object.keys(he.hotkeys).map(c=>he.hotkeys[c].length?g.jsxs("li",{children:[g.jsx("span",{style:{display:"inline-block",width:"200px"},children:he.hotkeys[c].map((d,f)=>g.jsxs("span",{children:[g.jsx(q1,{children:d}),he.hotkeys[c].length>f+1?" or ":""]},d))}),g.jsx("span",{style:{display:"inline-block"},children:Z1[c]})]},c):null)}),g.jsxs("p",{children:["Hotkeys can be disabled through"," ",g.jsx(Or,{children:"Story.meta = { hotkeys: false }"}),"."]})]}):g.jsxs("p",{children:["Hotkeys are disabled for this story by"," ",g.jsx(Or,{children:"meta.hotkeys = false"}),"."]}),g.jsxs("p",{children:["Ladle is a modern and fast playground for React components powered by Vite. For more information visit"," ",g.jsx("a",{href:"https://www.ladle.dev/",children:"ladle.dev"})," or our"," ",g.jsx("a",{href:"https://discord.gg/H6FSHjyW7e",children:"discord"}),"."]})]})]})})},K1=async(n,o,l)=>{const a=await w0(()=>import("./empty-module-BIHI7g3E.js"),[]);try{const c=await a.default.run(document.getElementsByTagName("main"));n(c.violations),o(!0)}catch{}},X1=({violation:n})=>{const[o,l]=N.useState(!1);return g.jsxs("li",{children:[n.help," (",n.nodes.length,")."," ",o?g.jsxs(g.Fragment,{children:[g.jsxs("ul",{children:[g.jsxs("li",{children:["ID: ",n.id]}),g.jsxs("li",{children:["Impact: ",n.impact]}),g.jsxs("li",{children:["Description: ",n.description]}),g.jsx("li",{children:g.jsx("a",{href:n.helpUrl,children:"Documentation"})})]}),g.jsx("p",{children:"Violating nodes:"}),g.jsx("ul",{children:n.nodes.map(a=>g.jsx("li",{children:g.jsx(Or,{children:a.html})},a.html))}),g.jsx("p",{children:g.jsx("a",{href:"#",onClick:()=>l(!1),children:"Hide details"})})]}):g.jsx("a",{href:"#",onClick:()=>l(!0),children:"Show details"})]})},J1=({reportFinished:n,violations:o})=>n?o.length===0?g.jsxs("p",{children:["There are no ",g.jsx("a",{href:"https://github.com/dequelabs/axe-core",children:"axe"})," ","accessibility violations. Good job!"]}):g.jsxs(g.Fragment,{children:[g.jsxs("h3",{children:["There are ",o.length," ",g.jsx("a",{href:"https://github.com/dequelabs/axe-core",children:"axe"})," accessibility violations"]}),g.jsx("ul",{children:o.map(l=>g.jsx(X1,{violation:l},l.id))})]}):g.jsx("p",{children:"Report is loading..."}),eE=({globalState:n})=>{const[o,l]=N.useState(!1),[a,c]=N.useState(!1),[d,f]=N.useState([]);N.useEffect(()=>{},[]);const y="Show accessibility report.",h=()=>{K1(f,c,null).catch(console.error),setTimeout(()=>l(!o),100)};return $t(he.hotkeys.a11y,()=>o?l(!1):h(),{enabled:n.hotkeys&&he.addons.a11y.enabled}),g.jsx("li",{children:g.jsxs("button",{"aria-label":y,"data-testid":"addon-a11y",title:y,onClick:h,className:o?"a11y-active":"",type:"button",children:[g.jsx(bm,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:y}),g.jsx("label",{children:"Accessibility report"}),d.length?g.jsx("div",{className:"ladle-badge",children:d.length}):null,g.jsx(So,{isOpen:o,close:()=>l(!1),label:"Dialog with the story accessibility report.",children:g.jsx(J1,{reportFinished:a,violations:d})})]})})},tE=n=>{const o=dn.parse(n).width;let l=0;return Object.keys(he.addons.width.options).forEach(a=>{(a===o||parseInt(o,10)===he.addons.width.options[a])&&(l=he.addons.width.options[a])}),l!==0?l:he.addons.width.defaultState},nE=({globalState:n,dispatch:o})=>{const l="Change the story viewport.",[a,c]=N.useState(!1);$t(he.hotkeys.width,()=>c(h=>!h),{enabled:n.hotkeys&&he.addons.width.enabled});const d=xo[n.story];let f=d&&d.meta?d.meta.meta.width:0,y=he.addons.width.options;return Object.keys(y).forEach(h=>{h===f&&(f=y[h])}),f&&!Object.values(y).includes(f)&&(y={custom:f,...y}),g.jsx("li",{children:g.jsxs("button",{"aria-label":l,"data-testid":"addon-width",title:l,onClick:()=>c(!0),className:a?"width-active":"",type:"button",children:[g.jsx(_m,{}),g.jsx("span",{className:"ladle-addon-tooltip",children:l}),g.jsx("label",{children:"Set story width"}),g.jsxs(So,{isOpen:a,close:()=>c(!1),label:"Dialog with the story width selector.",children:[g.jsx("p",{children:"Select story width"}),g.jsxs("div",{children:[g.jsx("input",{onChange:()=>o({type:Re.UpdateWidth,value:0}),type:"radio",id:"width-unset",name:"width",value:0,checked:n.width===0}),g.jsx("label",{htmlFor:"width-unset",style:{paddingLeft:"8px"},children:"unset"})]}),Object.keys(y).map(h=>g.jsxs("div",{children:[g.jsx("input",{onChange:()=>o({type:Re.UpdateWidth,value:y[h]}),type:"radio",id:`width-${h}`,name:"width",value:y[h],checked:n.width===y[h]}),g.jsxs("label",{htmlFor:`width-${h}`,style:{paddingLeft:"8px"},children:[y[h],"px - ",h]})]},h)),g.jsx("p",{})]})]})})},rE=({globalState:n,dispatch:o})=>Object.keys(he.addons).every(l=>he.addons[l].enabled===!1)?null:g.jsx("header",{role:"banner",className:"ladle-addons",children:g.jsxs("ul",{children:[he.addons.control.enabled&&Object.keys(n.control).length>0&&g.jsx(g0,{globalState:n,dispatch:o}),he.addons.theme.enabled&&g.jsx(S0,{globalState:n,dispatch:o}),he.addons.mode.enabled&&g.jsx(a1,{globalState:n,dispatch:o}),he.addons.width.enabled&&g.jsx(nE,{globalState:n,dispatch:o}),he.addons.rtl.enabled&&g.jsx(Y1,{globalState:n,dispatch:o}),he.addons.source.enabled&&g.jsx(Uw,{globalState:n,dispatch:o}),he.addons.a11y.enabled&&g.jsx(eE,{globalState:n,dispatch:o}),he.addons.ladle.enabled&&g.jsx(Q1,{globalState:n,dispatch:o}),he.addons.control.enabled&&n.action.length>0&&g.jsx(W1,{globalState:n,dispatch:o})]})}),oE=(n,o)=>{switch(sn("Action dispatched",o),o.type){case Re.UpdateAll:return{...n,...o.value};case Re.UpdateMode:return{...n,mode:o.value};case Re.UpdateAction:{const l={...n};return o.clear&&(l.action=[]),o.value?{...n,action:[...l.action,o.value]}:l}case Re.UpdateRtl:return{...n,rtl:o.value};case Re.UpdateSource:return{...n,source:o.value};case Re.UpdateStory:return{...n,story:o.value,control:{},controlInitialized:!1,width:0,action:[]};case Re.UpdateTheme:return{...n,theme:o.value};case Re.UpdateWidth:return{...n,width:o.value};case Re.UpdateControl:return{...n,control:o.value,controlInitialized:!0};case Re.UpdateControlIntialized:return{...n,controlInitialized:o.value};case Re.UpdateHotkeys:return{...n,hotkeys:o.value};default:return n}},zu=Ap(Object.keys(xo),he.storyOrder);sn("Stories found",zu);const vu=(n,o)=>({theme:mh(n),mode:i1(n),story:Cp(n,he.defaultStory),rtl:G1(n),source:Bw(n),width:tE(n),control:h0(n,o?o.control:{}),action:[],controlInitialized:!1,hotkeys:!0}),lE=()=>{const n=vu(location.search),[o,l]=N.useReducer(oE,n),a=N.useRef({}),[c,d]=N.useState("");let f="";o.control&&Object.keys(o.control).forEach(h=>{o.control[h].type==="background"&&(f=o.control[h].value||"")}),$t(he.hotkeys.fullscreen,()=>{l({type:Re.UpdateMode,value:o.mode===ht.Full?ht.Preview:ht.Full})},{preventDefault:!0,enabled:o.hotkeys&&he.addons.mode.enabled}),N.useEffect(()=>{document.getElementsByClassName("ladle-background")[0].style.background=f},[f]),N.useEffect(()=>{a.current=o}),N.useEffect(()=>{window.ladleDispatch=l},[]);const y=a.current;return N.useEffect(()=>{var h,m;sn("Global state update",o),Vg(location.search)||pu(o),pu(o),o.story!==y.story&&(document.title=`${Tp(o.story)} | Ladle`),o.theme!==y.theme&&document.documentElement.setAttribute("data-theme",o.theme),o.rtl!==y.rtl&&(o.rtl?document.documentElement.setAttribute("dir","rtl"):document.documentElement.removeAttribute("dir")),o.mode!==y.mode&&(document.documentElement.setAttribute("data-mode",o.mode),o.mode===ht.Preview?(h=document.getElementById("ladle-root"))==null||h.removeAttribute("class"):(m=document.getElementById("ladle-root"))==null||m.setAttribute("class","ladle-wrapper"))},[o]),N.useEffect(()=>{const h=ec.listen(({location:m,action:S})=>{if(S===Cr.Pop){const k={};Object.keys(o.control).forEach(A=>{const b=vu(m.search,o).control[A];k[A]={...o.control[A],value:b?b.value:o.control[A].defaultValue}});const C=vu(m.search,o);l({type:Re.UpdateAll,value:{...C,control:k,controlInitialized:o.story===C.story}})}});return()=>h()},[o]),o.mode===ht.Preview?g.jsx(Tf.Provider,{value:{globalState:o,dispatch:l},children:g.jsx(lp,{globalState:o,dispatch:l})}):g.jsxs(Tf.Provider,{value:{globalState:o,dispatch:l},children:[g.jsx("main",{className:"ladle-main",children:zu.length>0?g.jsx(lp,{globalState:o,dispatch:l}):g.jsx(Ww,{})}),g.jsx(l1,{search:c,setSearch:d,stories:zu,hotkeys:o.hotkeys,story:o.story,updateStory:h=>{Jw(),pu({...o,story:h,control:{}}),l({type:Re.UpdateStory,value:h})}}),g.jsx(rE,{globalState:o,dispatch:l})]})},iE=document.getElementById("ladle-root"),aE=_0.createRoot(iE);aE.render(g.jsx(lE,{}));
