{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "types": ["vite/client"],
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "noEmit": true,
    "jsx": "react",
    "baseUrl": "./src",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "paths": {
      "#components/*": ["components/*"],
      "#sections/*": ["sections/*"],
      "#pages/*": ["pages/*"],
      "#utils/*": ["utils/*"],
      "#renderer/*": ["renderer/*"]
    }
  },
  "include": ["src"],
  "exclude": ["*.config"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
