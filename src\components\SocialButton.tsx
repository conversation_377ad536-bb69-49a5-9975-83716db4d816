import { IconButton, useColorModeValue } from "@chakra-ui/react";
import React, { ReactNode } from "react";

interface SocialButtonProps  {
  children: ReactNode;
  label: string;
  href: string;
}

export const SocialButton = ({
  children,
  label,
  href,
  ...rest
}: SocialButtonProps) => {
  const bgColorHover = useColorModeValue("blackAlpha.200", "whiteAlpha.200");
  const bgColor = useColorModeValue("colors.brand.primary.600", "colors.brand.primary.100");
  return (
    <IconButton
    {...rest}
      bg={bgColor}
      rounded="full"
      w={8}
      h={8}
      cursor="pointer"
      as="a"
      href={href}
      display="inline-flex"
      alignItems="center"
      justifyContent="center"
      transition="all 0.3s"
      _hover={{
        bg: bgColorHover,
        transform: "translateY(-2px)",
      }}
      target="_blank"
      aria-label={label}
    >
      {children}
    </IconButton>
  );
};
