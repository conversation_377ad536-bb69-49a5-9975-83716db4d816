import { themeByA<PERSON> } from "#utils/themes";
import {
  Box,
  Heading,
  ListIcon,
  ListItem,
  Text,
  UnorderedList,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import * as React from "react";
import { IconType } from "react-icons";
import { DiResponsive } from "react-icons/di/index.js";
import { FaRegHandshake } from "react-icons/fa/index.js";
import { GiAmericanFootballHelmet } from "react-icons/gi/index.js";
import {
  SiChakraui,
  SiEslint,
  SiExpress,
  SiGit,
  SiGithub,
  SiMarkdown,
  SiNetlify,
  SiNodedotjs,
  SiPrettier,
  SiPwa,
  SiReact,
  SiServerless,
  SiSketch,
  SiTypescript,
  SiVisualstudio,
  SiVite,
  SiVitest,
} from "react-icons/si/index.js";

const heading = "TenK Solutions, LLC Technology Stack";

const frontendTechs = [
  ["React", "For building UIs through components", SiReact],
  ["TypeScript", "This is the way...", SiTypescript],
  ["Vite", "Build tool focused on lightning fast dev server and HMR", SiVite],
  [
    "Chakra UI",
    "Accessible React component library for rapid development",
    SiChakraui,
  ],
  // ["Framer Motion", "Complex animation capabilities for modern UIs", SiFramer],
  // [
  //   "Emotion",
  //   "Enables CSS styling within JavaScript/TypeScript",
  //   SiCsswizardry,
  // ],
  [
    "React Helmet",
    "Handles dynamically updates page metadata and SEO data",
    GiAmericanFootballHelmet,
  ],
  ["React Icons", "Quickly implement vector icons", SiSketch],
  [
    "React Markdown",
    "Display Markdown content through React components",
    SiMarkdown,
  ],
  ["Vecteezy", "Artificial Intelligence Stock Videos", FaRegHandshake],
];

const backendTechs = [
  [
    "Node.js",
    "JavaScript runtime enabling server-side JS execution",
    SiNodedotjs,
  ],
  ["Express", "A flexible Node.js web application framework", SiExpress],
];

const toolingTech = [
  ["Visual Studio Code", "A code editor for web development", SiVisualstudio],
  ["ESLint", "Identifying and reporting on patterns in JavaScript", SiEslint],
  [
    "Prettier",
    "An opinionated code formatter enforcing consistencies",
    SiPrettier,
  ],
  [
    "Vite Plugins",
    "Extend Vite via plugins like vite-plugin-react, vike & vitepress",
    SiVitest,
  ],
];

const infrastructureTech = [
  [
    "Netlify",
    "Provides CDN networking, atomic deployments, & DNS management",
    SiNetlify,
  ],
  ["Github", "Provides code hosting and version control", SiGithub],
];
const methodologiesTech = [
  [
    "Server-side Generation (SSG)",
    "HTML rendered on server for performance, then rendered in browser via javascript",
    SiServerless,
  ],
  ["PWA", "Progressive Web App (PWA) for offline support", SiPwa],
  [
    "Responsive Design",
    "Site adapts smoothly across all device sizes",
    DiResponsive,
  ],
  [
    "Git Workflow",
    "Following proven practices like branching, PRs, semantic versioning",
    SiGit,
  ],
];

const COLORS = themeByAI;

export default function TechPage() {
  const bg_fg_color = useColorModeValue(
    COLORS.colors.brand.primary[300],
    COLORS.colors.brand.primary[50]
  );
  const urlEncodedColor = encodeURIComponent(bg_fg_color);
  const patternOpacity = 0.2;

  return (
    <VStack
      style={{
        padding: "3em",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='199' viewBox='0 0 100 199'%3E%3Cg fill='${urlEncodedColor}' fill-opacity='${patternOpacity}'%3E%3Cpath d='M0 199V0h1v1.99L100 199h-1.12L1 4.22V199H0zM100 2h-.12l-1-2H100v2z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      <Heading as="h1">{heading} </Heading>
      <Box maxW="3xl" p={3}>
        <Text as={"p"} textAlign={"left"}>
          This page outlines the technology stack, infrastructure, tooling and
          techniques used in implementing this website.
          <br />
        </Text>
      </Box>
      <VStack spacing={6}>
        <Section title="Frontend Technologies" techs={frontendTechs} />
        <Section title="Backend Technologies" techs={backendTechs} />
        <Section
          title="Infrastructure Technologies"
          techs={infrastructureTech}
        />
        <Section title="Tooling" techs={toolingTech} />
        <Section title="Methodologies" techs={methodologiesTech} />
      </VStack>
    </VStack>
  );
}

export function Section({
  title,
  techs,
}: {
  title: string;
  techs: (string | IconType)[][];
}) {
  const bg_color = useColorModeValue(
    COLORS.colors.brand.secondary[50],
    COLORS.colors.brand.secondary[900]
  );
  const bg_fg_color = useColorModeValue(
    COLORS.colors.brand.primary[200],
    COLORS.colors.brand.secondary[50]
  );
  const urlEncodedColor = encodeURIComponent(bg_fg_color);
  const patternOpacity = 0.2;

  return (
    <VStack
      width="full"
      padding={8}
      boxShadow={"md"}
      // backgroundBlendMode={"difference"}
      backgroundPosition={"-1.5em 2.5em"}
      borderRadius={"md"}
      backgroundColor={bg_color}
      style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='199' viewBox='0 0 100 199'%3E%3Cg fill='${urlEncodedColor}' fill-opacity='${patternOpacity}'%3E%3Cpath d='M0 199V0h1v1.99L100 199h-1.12L1 4.22V199H0zM100 2h-.12l-1-2H100v2z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      <Heading paddingBlock={2} as="h2">
        {title}
      </Heading>
      <Box w="full" px={5}>
        <TechList techs={techs} />
      </Box>
    </VStack>
  );
}

export function TechList({
  techs,
}: {
  techs: (React.ElementType | string | any)[][];
}) {
  return (
    <UnorderedList>
      {techs.map((tech, i) => (
        <ListItem key={i}>
          <ListIcon as={tech[2]} />
          <strong>{tech[0]}</strong> - {tech[1]}
        </ListItem>
      ))}
    </UnorderedList>
  );
}
