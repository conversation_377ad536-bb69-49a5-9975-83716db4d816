"use client";

import { fadeInUp, staggerChildren } from "#utils/animationVariants";
import {
  Box,
  Container,
  Heading,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";

const MotionBox = motion(Box);

const QUESTIONS = [
  {
    question:
      "How can I use technology to help my small business reach the next growth milestone?",
    answer:
      "Our IT experts can provide customized solutions like workflow automation, data analytics, and custom software to help your small business hit growth goals and milestones. We partner with you to understand your needs and deploy the right technology.",
  },
  {
    question:
      "What IT services can help me set up a new retail location or office?",
    answer:
      "We install and configure networks, WiFi, POS systems, VoIP phones, security cameras, and any other required business technology.",
  },
  {
    question: "What types of custom software can improve my workflows?",
    answer:
      "Our expert developers can build customized software and web apps tailored to optimize your unique business workflows. From inventory or order management to HR systems, we can improve efficiency with solutions designed specifically for your processes.",
  },
  {
    question:
      "How can I optimize my existing IT infrastructure to improve performance?",
    answer:
      "We provide full managed IT services to help optimize your technology stack. This includes monitoring, updates, troubleshooting, security, help desk support, and more. We'll make sure your infrastructure aligns to business goals.",
  },
  {
    question:
      "How can automating processes improve my efficiency and cut costs?",
    answer:
      "By automating repetitive tasks, our solutions can significantly cut costs while improving quality and efficiency. We identify automation opportunities and implement the right technologies like scripting, RPA, APIs, and more to optimize your processes.",
  },

  {
    question: "How can you refresh my existing website design?",
    answer:
      "Our expert web developers can overhaul your current website with a completely new modern design. We'll carry over and optimize your content while giving your site a visual makeover with the latest design trends and features.",
  },
];

const QuestionnaireSlider = () => {
  const boxBgcolor = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("gray.600", "gray.400");
  return (
    <MotionBox
      as="section"
      py={16}
      variants={staggerChildren}
      initial="hidden"
      whileInView="visible"
      // viewport={{ once: true, margin: "-100px" }}
    >
      <Container maxW="container.xl">
        <MotionBox variants={fadeInUp}>
          <Heading
            textAlign="center"
            mb={8}
            bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
            bgClip="text"
          >
            Frequently Asked Questions
          </Heading>
        </MotionBox>

        <MotionBox variants={staggerChildren} display="grid" gap={6}>
          {QUESTIONS.map((qa, index) => (
            <MotionBox
              key={index}
              variants={fadeInUp}
              p={6}
              borderRadius="xl"
              bg={boxBgcolor}
              boxShadow="lg"
              _hover={{ transform: "translateY(-4px)", boxShadow: "xl" }}
              transition="all 0.3s"
            >
              <Text fontWeight="bold" mb={2}>
                {qa.question}
              </Text>
              <Text color={textColor}>{qa.answer}</Text>
            </MotionBox>
          ))}
        </MotionBox>
      </Container>
    </MotionBox>
  );
};

export default QuestionnaireSlider;
