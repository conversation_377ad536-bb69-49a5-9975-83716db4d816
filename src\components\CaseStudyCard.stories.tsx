import React from "react";
import type { Story<PERSON>ef<PERSON>, Story } from "@ladle/react";
import { StoryThemeProvider, storyDecorators } from "../stories/StoryThemeProvider";
import { SimpleGrid, VStack, Text } from "@chakra-ui/react";
import { CaseStudyCard } from "./CaseStudyCard";

// Sample case study data
const sampleCaseStudy = {
  title: "Cloud Migration & Infrastructure Modernization",
  imgSrc: "/img/clients/techcorp-logo.webp",
  companyDesc: "Mid-size technology consulting firm",
  companyName: "TechCorp Solutions",
  summary: `
**Challenge:** Legacy infrastructure causing performance bottlenecks and security concerns.

**Solution:** 
- Migrated to AWS cloud infrastructure
- Implemented automated backup systems
- Enhanced security protocols
- Established 24/7 monitoring

**Results:**
- 40% improvement in system performance
- 99.9% uptime achieved
- Reduced IT costs by 25%
- Enhanced security posture
  `,
  uri: "https://example.com/case-study-1",
};

const sampleCaseStudies = [
  {
    ...sampleCaseStudy,
    title: "Network Security Overhaul",
    companyName: "SecureFinance Inc",
    companyDesc: "Financial services company",
    imgSrc: "/img/clients/securefinance-logo.webp",
    summary: `
**Challenge:** Outdated security infrastructure vulnerable to modern threats.

**Solution:**
- Implemented next-gen firewall
- Deployed endpoint detection and response
- Established security awareness training
- Created incident response procedures

**Results:**
- Zero security incidents in 12 months
- Passed compliance audits
- Improved employee security awareness
    `,
    uri: "https://example.com/case-study-2",
  },
  {
    ...sampleCaseStudy,
    title: "Digital Transformation Initiative",
    companyName: "HealthCare Partners",
    companyDesc: "Regional healthcare provider",
    imgSrc: "/img/clients/healthcare-logo.webp",
    summary: `
**Challenge:** Paper-based processes slowing patient care and operations.

**Solution:**
- Implemented electronic health records (EHR)
- Deployed telemedicine platform
- Automated appointment scheduling
- Enhanced data analytics capabilities

**Results:**
- 50% reduction in administrative time
- Improved patient satisfaction scores
- Enhanced care coordination
- Better compliance reporting
    `,
    uri: "https://example.com/case-study-3",
  },
];

export default {
  title: "Components/Molecules/CaseStudyCard",
  component: CaseStudyCard,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
  args: sampleCaseStudy,
  argTypes: {
    title: {
      control: { type: "text" },
      description: "Case study title",
    },
    companyName: {
      control: { type: "text" },
      description: "Client company name",
    },
    companyDesc: {
      control: { type: "text" },
      description: "Brief company description",
    },
    summary: {
      control: { type: "text" },
      description: "Case study summary (supports Markdown)",
    },
    imgSrc: {
      control: { type: "text" },
      description: "Company logo image URL",
    },
    uri: {
      control: { type: "text" },
      description: "Link to full case study",
    },
  },
} satisfies StoryDefault;

/**
 * Default case study card
 */
export const Default: Story = {
  args: sampleCaseStudy,
  decorators: [storyDecorators.centered],
};

/**
 * Multiple case study cards
 */
export const MultipleCaseStudies: Story = {
  render: () => (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} maxW="7xl">
      {sampleCaseStudies.map((caseStudy, index) => (
        <CaseStudyCard key={index} {...caseStudy} />
      ))}
    </SimpleGrid>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Interactive case study card
 */
export const Interactive: Story = {
  args: sampleCaseStudy,
  decorators: [storyDecorators.centered],
};

/**
 * With different content lengths
 */
export const VariableContent: Story = {
  render: () => (
    <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} maxW="5xl">
      <CaseStudyCard
        title="Quick IT Assessment"
        companyName="StartupCo"
        companyDesc="Early-stage technology startup"
        imgSrc="/img/clients/startup-logo.webp"
        summary="**Challenge:** Rapid growth outpacing IT infrastructure. **Solution:** Scalable cloud setup. **Results:** 200% capacity increase."
        uri="https://example.com/short-case"
      />
      <CaseStudyCard
        {...sampleCaseStudy}
        summary={`
**Challenge:** Complex legacy infrastructure causing multiple operational challenges including performance bottlenecks, security vulnerabilities, and high maintenance costs.

**Solution:** 
- Comprehensive cloud migration strategy
- Implementation of modern DevOps practices
- Automated monitoring and alerting systems
- Staff training and knowledge transfer
- Phased rollout to minimize disruption

**Results:**
- 40% improvement in system performance
- 99.9% uptime achieved consistently
- Reduced IT operational costs by 25%
- Enhanced security posture with zero incidents
- Improved team productivity and satisfaction
- Better disaster recovery capabilities
- Scalable infrastructure for future growth

**Technologies Used:** AWS, Docker, Kubernetes, Terraform, Ansible, Prometheus, Grafana
        `}
      />
    </SimpleGrid>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * With missing/fallback images
 */
export const WithFallbackImages: Story = {
  render: () => (
    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} maxW="4xl">
      <CaseStudyCard
        {...sampleCaseStudy}
        imgSrc="/img/clients/nonexistent-logo.webp"
        companyName="Company with Missing Logo"
        title="Fallback Image Test"
      />
      <CaseStudyCard
        {...sampleCaseStudy}
        imgSrc=""
        companyName="Company with Empty Image"
        title="Empty Image Source Test"
      />
    </SimpleGrid>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Hover animation demonstration
 */
export const HoverAnimation: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Hover Animation Demo
      </Text>
      <Text fontSize="sm" color="gray.600" textAlign="center">
        Hover over the cards to see the scale animation effect
      </Text>
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} maxW="4xl">
        <CaseStudyCard {...sampleCaseStudies[0]} />
        <CaseStudyCard {...sampleCaseStudies[1]} />
      </SimpleGrid>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Responsive layout demonstration
 */
export const ResponsiveLayout: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Responsive Grid Layout
      </Text>
      <SimpleGrid 
        columns={{ base: 1, sm: 2, lg: 3, xl: 4 }} 
        spacing={4} 
        w="full"
      >
        {[...sampleCaseStudies, sampleCaseStudy].map((caseStudy, index) => (
          <CaseStudyCard key={index} {...caseStudy} />
        ))}
      </SimpleGrid>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};
