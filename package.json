{"name": "tenksolutions", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "yarn build && yarn server", "dev:stories": "yarn ladle serve", "prod": "yarn lint && yarn build && yarn server:prod", "server": "node --trace-warnings --loader ts-node/esm ./src/server/index.ts ", "server:prod": "cross-env NODE_ENV=production yarn server", "build": "tsc && vike build", "build:stories": "yarn ladle build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "pre": "tsc && vitest run && vike build && vike preview", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\"", "test": "vitest"}, "dependencies": {"@chakra-ui/icons": "^2.0.18", "@chakra-ui/react": "^2.5.5", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@ladle/react": "^5.0.3", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/node": "^20.10.4", "chakra-ui": "^0.3.9", "compression": "^1.7.4", "cross-env": "^7.0.3", "express": "^4.18.2", "framer-motion": "^10.12.4", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "react": "^18.2.0", "react-calendly": "^4.1.1", "react-dom": "^18.2.0", "react-ga": "^3.3.1", "react-helmet": "^6.1.0", "react-icons": "^4.12.0", "react-intersection-observer": "^9.14.0", "react-markdown": "^8.0.7", "sirv": "^2.0.3", "sort-by": "^1.2.0", "ts-node": "^10.9.2", "vike": "^0.4.147", "vite-plugin-eslint": "^1.8.1", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-helmet": "^6.1.6", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0-beta.0", "eslint": "^8.38.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "jsdom": "^25.0.1", "tsx": "^4.7.0", "typescript": "^5.0.2", "vite": "^4.3.0", "vitest": "^1.6.0"}}