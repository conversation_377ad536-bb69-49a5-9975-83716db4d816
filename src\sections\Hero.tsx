import HeroVideo from "#components/HeroVideo";
import {
  Box,
  Heading,
  ScaleFade,
  Text,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react";
import React, { useEffect } from "react";

export default function CallToActionWithIllustration() {
  // const brandTextColor = useColorModeValue(
  //   "colors.brand.secondary.900",
  //   "colors.brand.primary.200"
  // );
  const { isOpen, onOpen } = useDisclosure();

  useEffect(() => {
    onOpen();
  }, [onOpen]);

  const blendMode = useColorModeValue("screen", "multiply");
  const backgroundColors = useColorModeValue(
    "colors.brand.secondary.100",
    "colors.brand.secondary.900"
  );

  return (
    <Box position="relative" overflow="hidden">
      <ScaleFade initialScale={0.9} in={isOpen}>
        <HeroVideo
          videoSrc={"/img/bg/pcback.mp4"}
          blendMode={blendMode}
          backgroundColors={backgroundColors}
        >
          <Box
            maxW="container.xl"
            mx="auto"
            px={{ base: 4, lg: 8 }}
            position="relative"
            zIndex={2}
          >
            <Heading
              as="h1"
              fontWeight={600}
              fontSize={{ base: "4xl", md: "6xl", lg: "7xl" }}
              lineHeight="110%"
              textAlign="center"
              textShadow="0 2px 4px rgba(0,0,0,0.2)"
              bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
              bgClip="text"
              animation="fadeIn 2s ease-in"
            >
              Empower Your Business Growth
            </Heading>

            <Text
              as="h2"
              mt={6}
              fontSize={{ base: "xl", md: "2xl", lg: "3xl" }}
              textAlign="center"
              maxW="800px"
              mx="auto"
              color={useColorModeValue("gray.700", "gray.300")}
              textShadow="0 1px 2px rgba(0,0,0,0.1)"
            >
              Delivering innovative IT solutions that drive success for professional services firms in the DMV-area
            </Text>
          </Box>
        </HeroVideo>
      </ScaleFade>
    </Box>
  );
}
