<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="description"
      content="Ladle: Playground for your React Components."
    />
    <meta name="theme-color" content="#ffffff" />
    <link rel="icon" href="/assets/favicon-gjFwcFbF.svg" />
    <link rel="mask-icon" href="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2024%2024'%20%3e%3cpath%20d='M22,14H9V5A4,4,0,0,0,1,5V8A1,1,0,0,0,3,8V5A2,2,0,0,1,7,5V15a8,8,0,0,0,16,0A1,1,0,0,0,22,14Zm-7,7a6.01,6.01,0,0,1-5.917-5H20.917A6.01,6.01,0,0,1,15,21Z'/%3e%3c/svg%3e" color="#000000" />
    <link rel="apple-touch-icon" href="/assets/touch-icon-ByyUZD7t.png" />
    <link
      rel="manifest"
      href="/assets/manifest-B-VxemCC.webmanifest"
      crossorigin="use-credentials"
    />
    <title>Ladle</title>
    <script type="module" crossorigin src="/assets/index-DVJ0OKc1.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-Bsr3yfDJ.css">
  </head>

  <body>
    <div class="ladle-background"></div>
    <div id="ladle-root" class="ladle-wrapper"></div>
  </body>
</html>
