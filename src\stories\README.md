# TenK Solutions Component Library - Ladle Stories

This directory contains the Ladle story configuration and templates for our component library development workflow.

## 🚀 Quick Start

```bash
# Start the Ladle development server
yarn dev:stories

# Build stories for production
yarn build:stories
```

## 📁 Directory Structure

```
src/stories/
├── README.md                    # This documentation
├── StoryThemeProvider.tsx       # Theme provider for stories
├── Welcome.stories.tsx          # Welcome/intro story
└── templates/
    └── ComponentStoryTemplate.tsx # Template for new stories
```

## 🎨 Theme Integration

All stories use our custom Chakra UI theme through the `StoryThemeProvider`. This ensures:

- Consistent branding across all component stories
- Proper color mode support (light/dark)
- Access to custom theme tokens and component variants
- Integration with TenK Solutions design system

### Using the Theme Provider

```tsx
import { StoryThemeProvider } from "../StoryThemeProvider";

export default {
  title: "Components/MyComponent",
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
};
```

## 📝 Creating New Stories

### 1. Use the Template

Copy the `ComponentStoryTemplate.tsx` and rename it to match your component:

```bash
cp src/stories/templates/ComponentStoryTemplate.tsx src/components/MyComponent.stories.tsx
```

### 2. Update the Template

1. Replace all instances of "YourComponent" with your component name
2. Import your actual component
3. Update the title and category
4. Configure component-specific props and variants
5. Add relevant stories for your component's use cases

### 3. Story Categories

Organize stories using these categories:

- **Components/Atoms** - Basic UI elements (buttons, inputs, icons)
- **Components/Molecules** - Component combinations (cards, forms)
- **Components/Organisms** - Complex components (navigation, headers)
- **Sections** - Page sections (hero, features, contact)
- **Pages** - Full page layouts

### 4. Standard Story Types

Each component should include these standard stories:

- **Default** - Basic component state
- **AllVariants** - All available variants
- **Sizes** - Different size options
- **Interactive** - With controls for testing
- **States** - Different component states (disabled, loading, etc.)
- **WithThemeColors** - Demonstrates theme integration

## 🎛️ Story Decorators

Use the provided decorators for consistent presentation:

```tsx
import { storyDecorators } from "../StoryThemeProvider";

export const MyStory: Story = {
  decorators: [storyDecorators.centered], // Centers the component
  // or storyDecorators.padded    // Adds padding
  // or storyDecorators.fullWidth // Full width container
  // or storyDecorators.card      // Card-like presentation
};
```

## 🔧 Controls and Args

Configure controls for interactive testing:

```tsx
export default {
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "primary", "secondary"],
      description: "Component variant",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disable the component",
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg"],
      description: "Component size",
    },
  },
};
```

## 📋 Story Naming Conventions

- Use PascalCase for story names: `Default`, `AllVariants`, `WithIcon`
- Use descriptive names that explain the story's purpose
- Group related stories with consistent prefixes: `State_Normal`, `State_Disabled`

## 🎯 Best Practices

### Component Stories Should:

1. **Show all variants** - Demonstrate every visual variant of the component
2. **Test edge cases** - Include empty states, long content, error states
3. **Be interactive** - Use controls to allow testing different props
4. **Document usage** - Include clear descriptions and examples
5. **Test accessibility** - Ensure components work with screen readers
6. **Be responsive** - Test how components behave at different screen sizes

### Story Organization:

1. **Co-locate stories** - Place `.stories.tsx` files next to their components
2. **Use consistent naming** - Follow the `ComponentName.stories.tsx` pattern
3. **Group logically** - Use categories to organize related components
4. **Keep focused** - Each story should demonstrate one specific aspect

## 🚀 Development Workflow

### Adding a New Component Story:

1. Create your component in `src/components/`
2. Copy the story template to the same directory
3. Rename and customize the template
4. Run `yarn dev:stories` to see your stories
5. Iterate on the component and stories together
6. Test different states and edge cases
7. Document any special usage patterns

### Testing Components:

1. Use the interactive stories to test different props
2. Check both light and dark themes
3. Test responsive behavior by resizing the viewport
4. Verify accessibility with the a11y addon
5. Test keyboard navigation and screen reader compatibility

## 🔍 Troubleshooting

### Common Issues:

1. **Theme not applied** - Ensure you're using `StoryThemeProvider`
2. **Controls not working** - Check your `argTypes` configuration
3. **Stories not appearing** - Verify the file naming convention
4. **Import errors** - Check your path aliases in `vite.ladle.config.ts`

### Getting Help:

- Check the Ladle documentation: https://ladle.dev/
- Review existing stories for examples
- Test your component in isolation first
- Use the browser dev tools to debug styling issues

## 📚 Resources

- [Ladle Documentation](https://ladle.dev/)
- [Chakra UI Documentation](https://chakra-ui.com/)
- [Component Story Format](https://storybook.js.org/docs/react/api/csf)
- [Accessibility Testing](https://web.dev/accessibility/)
