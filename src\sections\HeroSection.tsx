import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Text
} from "@chakra-ui/react";
import { motion } from "framer-motion"; // If you want animations, install: npm i framer-motion
import React from "react";

// You may want to create a types file later
interface Metric {
  number: string;
  label: string;
}

const metrics: Metric[] = [
  { number: "200+", label: "Technology Partners" },
  { number: "15+", label: "Years Experience" },
  { number: "98%", label: "Client Retention" },
];

const MotionBox = motion(Box);

export const HeroSection: React.FC = () => {
  // const bgGradient = useColorModeValue(
  //   "linear(to-r, blue.600, purple.600)",
  //   "linear(to-r, blue.700, purple.700)"
  // );

  const overlayGradient =
    "linear(to-r, blue.600 15%, rgba(56, 28, 201, 0.8) 50%, rgba(78, 46, 214, 0.8) 100%)";

  return (
    <Box
      as="section"
      position="relative"
      h={{ base: "100vh", md: "85vh" }}
      minH="700px"
      color="white"
      overflow="hidden"
    >
      {/* Video Background */}
      <Box
        as="video"
        autoPlay
        muted
        loop
        playsInline
        position="absolute"
        top="0"
        left="0"
        w="100%"
        h="100%"
        objectFit="cover"
        zIndex={0}
      >
        <source src="/img/bg/pcback.mp4" type="video/mp4" />
      </Box>

      {/* Gradient Overlay */}
      <Box
        position="absolute"
        top="0"
        left="0"
        w="100%"
        h="100%"
        bgGradient={overlayGradient}
        zIndex={1}
      />

      {/* Content */}
      <Container maxW="container.xl" position="relative" zIndex={2} h="100%">
        <Stack
          spacing={8}
          alignItems="center"
          textAlign="center"
          justify="center"
          h="100%"
        >
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Heading
              as="h1"
              size="2xl"
              fontWeight="extrabold"
              lineHeight="shorter"
              mb={4}
            >
              Enterprise Technology Solutions
              <Text as="span" display="block">
                Built for Growth
              </Text>
            </Heading>
            <Text
              fontSize={{ base: "lg", md: "xl" }}
              maxW="2xl"
              mx="auto"
              opacity={0.9}
            >
              Empowering mid to large businesses with custom IT solutions that
              drive innovation and scale operations.
            </Text>
          </MotionBox>

          <Stack
            direction={{ base: "column", sm: "row" }}
            spacing={4}
            justify="center"
          >
            <Button
              size="lg"
              colorScheme="white"
              color="blue.600"
              bg="white"
              _hover={{ bg: "gray.100" }}
              height="64px"
              px={8}
            >
              Schedule Consultation
            </Button>
            <Button
              size="lg"
              variant="outline"
              borderWidth={2}
              height="64px"
              px={8}
            >
              Get Quote
            </Button>
          </Stack>

          <Flex wrap="wrap" justify="center" gap={8} mt={16} px={4}>
            {metrics.map((metric, index) => (
              <Stack key={index} align="center" spacing={2}>
                <Text fontSize="4xl" fontWeight="bold" lineHeight="1">
                  {metric.number}
                </Text>
                <Text fontSize="sm" opacity={0.8}>
                  {metric.label}
                </Text>
              </Stack>
            ))}
          </Flex>
        </Stack>
      </Container>
    </Box>
  );
};
