import { 
  Box, 
  Heading, 
  Text, 
  VStack, 
  HStack, 
  Badge, 
  Code,
  Divider,
  List,
  ListItem,
  ListIcon,
  Button,
  useColorModeValue
} from "@chakra-ui/react";
import React from "react";
import { CheckCircleIcon, InfoIcon } from "@chakra-ui/icons";
import { StoryThemeProvider } from "./StoryThemeProvider";

export default {
  title: "Welcome",
  decorators: [(Story: React.ComponentType) => (
    <StoryThemeProvider>
      <Story />
    </StoryThemeProvider>
  )],
};

export const Welcome = () => {
  const bgGradient = useColorModeValue(
    "linear(to-r, brand.primary.50, brand.secondary.50)",
    "linear(to-r, brand.primary.900, brand.secondary.900)"
  );
  
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  return (
    <Box 
      minH="100vh" 
      bgGradient={bgGradient}
      p={8}
    >
      <VStack spacing={8} maxW="4xl" mx="auto">
        <VStack spacing={4} textAlign="center">
          <Heading size="2xl" color="brand.primary.500">
            TenK Solutions Component Library
          </Heading>
          <Text fontSize="xl" color="gray.600">
            Welcome to our comprehensive component development environment
          </Text>
          <HStack spacing={2}>
            <Badge colorScheme="blue" variant="solid">Chakra UI</Badge>
            <Badge colorScheme="purple" variant="solid">Ladle</Badge>
            <Badge colorScheme="green" variant="solid">TypeScript</Badge>
          </HStack>
        </VStack>

        <Divider />

        <HStack spacing={8} align="start" w="full">
          <Box 
            flex={1} 
            bg={cardBg} 
            p={6} 
            borderRadius="xl" 
            borderWidth="1px" 
            borderColor={borderColor}
            boxShadow="lg"
          >
            <VStack align="start" spacing={4}>
              <HStack>
                <InfoIcon color="blue.500" />
                <Heading size="md">Getting Started</Heading>
              </HStack>
              <Text fontSize="sm" color="gray.600">
                This component library is built with Ladle for component development and testing.
                Each component has multiple stories showcasing different states and use cases.
              </Text>
              <List spacing={2} fontSize="sm">
                <ListItem>
                  <ListIcon as={CheckCircleIcon} color="green.500" />
                  Browse components in the sidebar
                </ListItem>
                <ListItem>
                  <ListIcon as={CheckCircleIcon} color="green.500" />
                  Test different component states
                </ListItem>
                <ListItem>
                  <ListIcon as={CheckCircleIcon} color="green.500" />
                  View component source code
                </ListItem>
                <ListItem>
                  <ListIcon as={CheckCircleIcon} color="green.500" />
                  Test accessibility features
                </ListItem>
              </List>
            </VStack>
          </Box>

          <Box 
            flex={1} 
            bg={cardBg} 
            p={6} 
            borderRadius="xl" 
            borderWidth="1px" 
            borderColor={borderColor}
            boxShadow="lg"
          >
            <VStack align="start" spacing={4}>
              <Heading size="md">Component Categories</Heading>
              <VStack align="start" spacing={2} fontSize="sm">
                <Text><strong>Atoms:</strong> Basic UI elements (buttons, inputs, icons)</Text>
                <Text><strong>Molecules:</strong> Component combinations (cards, forms)</Text>
                <Text><strong>Organisms:</strong> Complex components (navigation, headers)</Text>
                <Text><strong>Sections:</strong> Page sections (hero, features, contact)</Text>
              </VStack>
              <Code p={2} borderRadius="md" fontSize="xs">
                yarn dev:stories
              </Code>
            </VStack>
          </Box>
        </HStack>

        <Box 
          w="full" 
          bg={cardBg} 
          p={6} 
          borderRadius="xl" 
          borderWidth="1px" 
          borderColor={borderColor}
          boxShadow="lg"
        >
          <VStack spacing={4}>
            <Heading size="md">Theme Integration</Heading>
            <Text fontSize="sm" color="gray.600" textAlign="center">
              All components use our custom Chakra UI theme with TenK Solutions branding.
              The theme includes custom colors, fonts, and component variants.
            </Text>
            <HStack spacing={4}>
              <Button variant="solid" colorScheme="brand.primary">Primary Button</Button>
              <Button variant="secondary">Secondary Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="ghost">Ghost Button</Button>
            </HStack>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
};

Welcome.storyName = "Welcome to Component Library";
