import React from "react";
import type { StoryDefault, Story } from "@ladle/react";
import { StoryThemeProvider, storyDecorators } from "../stories/StoryThemeProvider";
import { VStack, HStack, Text, Box, Button, Spinner } from "@chakra-ui/react";
import { LoadingSpinner } from "./LoadingSpinner";

export default {
  title: "Components/Atoms/LoadingSpinner",
  component: LoadingSpinner,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
} satisfies StoryDefault;

/**
 * Default loading spinner (fixed position)
 */
export const Default: Story = {
  render: () => (
    <Box position="relative" height="300px" bg="gray.50" _dark={{ bg: "gray.800" }}>
      <Text position="absolute" top="4" left="4" fontSize="sm" color="gray.600">
        Fixed position spinner (centered in viewport)
      </Text>
      <LoadingSpinner />
    </Box>
  ),
  decorators: [storyDecorators.fullWidth],
};

/**
 * Spinner variations using <PERSON>kra's Spinner component
 */
export const SpinnerVariations: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Spinner Variations
      </Text>
      
      <VStack spacing={4}>
        <Text fontSize="sm" color="gray.600">Different Sizes</Text>
        <HStack spacing={6} justify="center">
          <VStack spacing={2}>
            <Spinner size="xs" color="brand.primary.500" />
            <Text fontSize="xs">XS</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner size="sm" color="brand.primary.500" />
            <Text fontSize="xs">SM</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner size="md" color="brand.primary.500" />
            <Text fontSize="xs">MD</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner size="lg" color="brand.primary.500" />
            <Text fontSize="xs">LG</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner size="xl" color="brand.primary.500" />
            <Text fontSize="xs">XL</Text>
          </VStack>
        </HStack>
      </VStack>

      <VStack spacing={4}>
        <Text fontSize="sm" color="gray.600">Different Colors</Text>
        <HStack spacing={6} justify="center" wrap="wrap">
          <VStack spacing={2}>
            <Spinner color="brand.primary.500" size="lg" />
            <Text fontSize="xs">Primary</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner color="brand.secondary.500" size="lg" />
            <Text fontSize="xs">Secondary</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner color="blue.500" size="lg" />
            <Text fontSize="xs">Blue</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner color="green.500" size="lg" />
            <Text fontSize="xs">Green</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner color="purple.500" size="lg" />
            <Text fontSize="xs">Purple</Text>
          </VStack>
        </HStack>
      </VStack>

      <VStack spacing={4}>
        <Text fontSize="sm" color="gray.600">Different Speeds</Text>
        <HStack spacing={6} justify="center">
          <VStack spacing={2}>
            <Spinner speed="0.3s" color="brand.primary.500" size="lg" />
            <Text fontSize="xs">Fast</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner speed="0.65s" color="brand.primary.500" size="lg" />
            <Text fontSize="xs">Normal</Text>
          </VStack>
          <VStack spacing={2}>
            <Spinner speed="1.2s" color="brand.primary.500" size="lg" />
            <Text fontSize="xs">Slow</Text>
          </VStack>
        </HStack>
      </VStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Loading states in different contexts
 */
export const InContext: Story = {
  render: () => {
    const [isLoading, setIsLoading] = React.useState(false);
    
    const simulateLoading = () => {
      setIsLoading(true);
      setTimeout(() => setIsLoading(false), 3000);
    };

    return (
      <VStack spacing={6} align="stretch">
        <Text fontWeight="bold" textAlign="center">
          Loading States in Context
        </Text>
        
        {/* Button with loading state */}
        <Box p={4} borderRadius="lg" bg="white" boxShadow="md" _dark={{ bg: "gray.800" }}>
          <VStack spacing={4}>
            <Text fontSize="sm" fontWeight="semibold">Button Loading State</Text>
            <Button
              onClick={simulateLoading}
              isLoading={isLoading}
              loadingText="Processing..."
              colorScheme="brand.primary"
              spinner={<Spinner color="white" size="sm" />}
            >
              {isLoading ? "Loading..." : "Start Loading"}
            </Button>
          </VStack>
        </Box>

        {/* Card with loading overlay */}
        <Box 
          p={4} 
          borderRadius="lg" 
          bg="white" 
          boxShadow="md" 
          _dark={{ bg: "gray.800" }}
          position="relative"
          minH="120px"
        >
          <VStack spacing={4}>
            <Text fontSize="sm" fontWeight="semibold">Card with Loading Overlay</Text>
            <Text fontSize="sm" color="gray.600">
              This demonstrates how the spinner appears over content
            </Text>
            {isLoading && (
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                bottom="0"
                bg="whiteAlpha.800"
                _dark={{ bg: "blackAlpha.800" }}
                display="flex"
                alignItems="center"
                justifyContent="center"
                borderRadius="lg"
              >
                <Spinner color="brand.primary.500" size="lg" />
              </Box>
            )}
          </VStack>
        </Box>

        {/* Inline loading */}
        <Box p={4} borderRadius="lg" bg="white" boxShadow="md" _dark={{ bg: "gray.800" }}>
          <HStack spacing={4} align="center">
            <Text fontSize="sm" fontWeight="semibold">Inline Loading:</Text>
            {isLoading ? (
              <HStack spacing={2}>
                <Spinner size="sm" color="brand.primary.500" />
                <Text fontSize="sm" color="gray.600">Loading data...</Text>
              </HStack>
            ) : (
              <Text fontSize="sm" color="green.500">✓ Data loaded</Text>
            )}
          </HStack>
        </Box>
      </VStack>
    );
  },
  decorators: [storyDecorators.padded],
};

/**
 * Theme integration demonstration
 */
export const WithThemeColors: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Theme Color Integration
      </Text>
      
      <HStack spacing={8} justify="center" wrap="wrap">
        <VStack spacing={3}>
          <Box
            p={6}
            borderRadius="lg"
            bg="brand.primary.50"
            borderWidth="2px"
            borderColor="brand.primary.200"
            _dark={{ 
              bg: "brand.primary.900", 
              borderColor: "brand.primary.700" 
            }}
          >
            <Spinner color="brand.primary.500" size="lg" thickness="4px" />
          </Box>
          <Text fontSize="sm">Primary Theme</Text>
        </VStack>
        
        <VStack spacing={3}>
          <Box
            p={6}
            borderRadius="lg"
            bg="brand.secondary.50"
            borderWidth="2px"
            borderColor="brand.secondary.200"
            _dark={{ 
              bg: "brand.secondary.900", 
              borderColor: "brand.secondary.700" 
            }}
          >
            <Spinner color="brand.secondary.500" size="lg" thickness="4px" />
          </Box>
          <Text fontSize="sm">Secondary Theme</Text>
        </VStack>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};
