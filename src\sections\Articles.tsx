import {
  Box,
  Container,
  HStack,
  Heading,
  Link,
  ListItem,
  Stack,
  Tag,
  Text,
  UnorderedList,
  VStack,
} from "@chakra-ui/react";

import * as React from "react";

interface Props {
  marginTop?: number;
  tags: any[];
}

const BlogTags = (props: Props) => {
  const { marginTop = 0, tags } = props;

  return (
    <HStack spacing={2} marginTop={marginTop}>
      {tags.map((tag) => {
        return (
          <Tag size={"md"} variant="solid" colorScheme="orange" key={tag}>
            {tag}
          </Tag>
        );
      })}
    </HStack>
  );
};

const LINKEDIN_ARTICLES = [
  "https://www.linkedin.com/embed/feed/update/urn:li:ugcPost:7097944030923087872",
  "https://www.linkedin.com/embed/feed/update/urn:li:ugcPost:7084586724848226304",
  "https://www.linkedin.com/embed/feed/update/urn:li:ugcPost:7063203578114396161",
];
const ArticleList = () => {
  return (
    <Container maxW={"7xl"} p="12">
      <Heading as="h1">Articles by TenK Solutions, LLC</Heading>
      <Box
        marginTop={{ base: "3", sm: "7" }}
        display="flex"
        flexDirection={{ base: "column", sm: "row" }}
        justifyContent="space-between"
      >
        <Box
          height="100%"
          display="flex"
          flex="1"
          flexDirection="column"
          justifyContent="center"
          marginTop={{ base: "3", sm: "0" }}
        >
          <BlogTags tags={["Engineering", "Product"]} />
          {/* <Heading marginTop="1">
            <Text textDecoration="none" _hover={{ textDecoration: "none" }}>
              Blog article title
            </Text>
          </Heading>
          <Text
            as="p"
            marginTop="2"
            color={useColorModeValue("gray.700", "gray.200")}
            fontSize="lg"
          >
            Lorem Ipsum is simply dummy text of the printing and typesetting
            industry. Lorem Ipsum has been the industry&apos;s standard dummy
            text ever since the 1500s, when an unknown printer took a galley of
            type and scrambled it to make a type specimen book.
          </Text> */}
          <Stack
            spacing={2}
            direction={{ base: "column", md: "row" }}
            overflowX={"scroll"}
            paddingY={3}
            my={3}
            boxShadow={"lg"}
          >
            {LINKEDIN_ARTICLES.map((url) => (
              <Box key={url}>
                <iframe
                  src={url}
                  height={"600"}
                  width={"400"}
                  allowFullScreen={false}
                  title="TenK Solutions Embedded Articles"
                />
              </Box>
            ))}
          </Stack>
        </Box>
      </Box>

      <VStack paddingTop="40px" spacing="2" alignItems="flex-start">
        <Heading as="h2">What we write about</Heading>
        <Text as="p" fontSize="lg">
          At TenK Solutions, our IT consultants author informative articles to
          help businesses understand and adopt the technologies that will make
          them more productive, secure, and successful.
        </Text>
        <Text as="p" fontSize="lg">
          We regularly publish pieces on the most relevant topics in IT
          management, security, cloud, analytics, and emerging tech trends.
        </Text>
        <Text as="p" fontSize="lg">
          Some of the topics we cover include:
          <UnorderedList listStylePos={"inside"} mb={3} fontSize={"md"}>
            <ListItem>
              The business benefits of <b>migrating to the cloud</b>
            </ListItem>
            <ListItem>
              Securing your enterprise from <b>modern cyber threats</b>
            </ListItem>
            <ListItem>
              Choosing the right <b>IT service provider</b> for your needs
            </ListItem>
            <ListItem>
              <b>Adopting analytics and AI</b> to gain strategic insights
            </ListItem>
            <ListItem>
              <b>Digital transformation</b> strategies for IT leaders
            </ListItem>
            <ListItem>
              <b>Optimizing costs</b> with managed IT services
            </ListItem>
            <ListItem>
              The future of work and <b>enabling remote/hybrid teams</b>
            </ListItem>
            <ListItem>
              Evaluating <b>new technologies</b> like blockchain and IoT
            </ListItem>
            <ListItem>
              <b>Data governance</b> best practices
            </ListItem>
            <ListItem>
              <b>IT budgeting</b> and spending optimization
            </ListItem>
            <ListItem>
              Why CIOs should care about <b>customer experience</b>
            </ListItem>
            <ListItem>
              <b>SaaS</b> vs. IaaS vs. PaaS
            </ListItem>
          </UnorderedList>
          <Text as={"p"}>
            Bookmark this page or{" "}
            <Link
              isExternal
              href="https://linkedin.com/company/tenksolutions"
              title="TenK Solutions LinkedIn Article"
            >
              follow us on LinkedIn
            </Link>{" "}
            to stay up-to-date on the latest IT articles from the TenK Solutions
            team. Our goal is to deliver practical, actionable content to help
            your organization leverage technology for competitive advantage.
          </Text>
        </Text>
      </VStack>
    </Container>
  );
};

export default ArticleList;
