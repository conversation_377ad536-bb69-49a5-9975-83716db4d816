# TenK Solutions - Ladle Component Development Workflow

This document outlines the comprehensive workflow for developing, testing, and maintaining components using Ladle in the TenK Solutions project.

## 🚀 Quick Start

```bash
# Start the Ladle development server
yarn dev:stories

# Build stories for production
yarn build:stories

# Run the main application
yarn dev
```

## 📁 Project Structure

```
src/
├── stories/                     # Ladle configuration and shared utilities
│   ├── README.md               # Detailed documentation
│   ├── StoryThemeProvider.tsx  # Theme provider for stories
│   ├── Welcome.stories.tsx     # Welcome/intro story
│   └── templates/
│       └── ComponentStoryTemplate.tsx  # Template for new stories
├── components/                  # Component library
│   ├── [Component].tsx         # Component implementation
│   └── [Component].stories.tsx # Co-located story file
├── sections/                    # Page sections
│   ├── [Section].tsx           # Section implementation
│   └── [Section].stories.tsx   # Co-located story file
└── utils/
    └── themes.ts               # Theme configuration
```

## 🎨 Theme Integration

### Chakra UI Theme System
- **Primary Brand Colors**: `brand.primary.50` to `brand.primary.900`
- **Secondary Brand Colors**: `brand.secondary.50` to `brand.secondary.900`
- **Custom Components**: <PERSON><PERSON>, Card, Container, Heading variants
- **Responsive Design**: Mobile-first approach with breakpoints

### Using Theme in Stories
```tsx
import { StoryThemeProvider } from "../stories/StoryThemeProvider";

export default {
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
};
```

## 📝 Component Development Workflow

### 1. Create a New Component

1. **Create the component file**: `src/components/MyComponent.tsx`
2. **Implement the component** with proper TypeScript types
3. **Use Chakra UI** components and theme tokens
4. **Add responsive design** considerations
5. **Include accessibility** features (ARIA labels, keyboard navigation)

### 2. Create Component Stories

1. **Copy the template**:
   ```bash
   cp src/stories/templates/ComponentStoryTemplate.tsx src/components/MyComponent.stories.tsx
   ```

2. **Customize the template**:
   - Replace "YourComponent" with your component name
   - Import your actual component
   - Update the title and category
   - Configure component-specific props and variants

3. **Add standard stories**:
   - **Default**: Basic component state
   - **AllVariants**: All available variants
   - **Sizes**: Different size options
   - **Interactive**: With controls for testing
   - **States**: Different component states
   - **WithThemeColors**: Theme integration

### 3. Story Categories

Organize stories using these categories:
- **Components/Atoms**: Basic UI elements (buttons, inputs, icons)
- **Components/Molecules**: Component combinations (cards, forms)
- **Components/Organisms**: Complex components (navigation, headers)
- **Sections**: Page sections (hero, features, contact)
- **Pages**: Full page layouts

### 4. Testing and Iteration

1. **Start Ladle**: `yarn dev:stories`
2. **Test different states** using interactive controls
3. **Check responsive behavior** by resizing viewport
4. **Verify accessibility** with screen readers
5. **Test theme integration** in light/dark modes
6. **Iterate on component** based on story feedback

## 🎛️ Story Configuration

### Basic Story Structure
```tsx
import type { StoryDefault, Story } from "@ladle/react";

export default {
  title: "Components/Atoms/MyComponent",
  component: MyComponent,
  decorators: [/* theme provider */],
  args: {/* default props */},
  argTypes: {/* control configuration */},
} satisfies StoryDefault;

export const Default: Story = {
  args: {/* story-specific props */},
  decorators: [/* story-specific decorators */],
};
```

### Available Decorators
```tsx
import { storyDecorators } from "../stories/StoryThemeProvider";

// Center content in viewport
decorators: [storyDecorators.centered]

// Add padding around content
decorators: [storyDecorators.padded]

// Full width container
decorators: [storyDecorators.fullWidth]

// Card-like presentation
decorators: [storyDecorators.card]
```

### Control Types
```tsx
argTypes: {
  variant: {
    control: { type: "select" },
    options: ["default", "primary", "secondary"],
  },
  disabled: {
    control: { type: "boolean" },
  },
  size: {
    control: { type: "select" },
    options: ["sm", "md", "lg"],
  },
}
```

## 🔧 Development Best Practices

### Component Design
1. **Follow atomic design principles**: Atoms → Molecules → Organisms
2. **Use TypeScript**: Proper typing for props and state
3. **Responsive by default**: Mobile-first design approach
4. **Accessibility first**: ARIA labels, keyboard navigation, screen reader support
5. **Theme integration**: Use design tokens and theme-aware styling
6. **Performance**: Optimize for bundle size and runtime performance

### Story Writing
1. **Comprehensive coverage**: Show all component states and variants
2. **Interactive testing**: Use controls for dynamic prop testing
3. **Edge cases**: Include error states, empty states, loading states
4. **Documentation**: Clear descriptions and usage examples
5. **Responsive testing**: Demonstrate behavior at different screen sizes
6. **Accessibility testing**: Verify keyboard navigation and screen reader compatibility

### Code Quality
1. **Consistent naming**: Follow established conventions
2. **Clean code**: Readable, maintainable, well-commented
3. **Error handling**: Graceful degradation and error boundaries
4. **Testing**: Unit tests for complex logic
5. **Performance**: Avoid unnecessary re-renders and optimize bundle size

## 🚀 Deployment and Integration

### Story Deployment
1. **Build stories**: `yarn build:stories`
2. **Deploy to hosting**: Stories can be deployed independently
3. **Share with team**: Use deployed stories for design reviews

### Component Integration
1. **Test in isolation**: Verify component works in Ladle
2. **Integration testing**: Test in actual application context
3. **Documentation**: Update component documentation
4. **Code review**: Peer review for quality assurance
5. **Deployment**: Deploy to production after testing

## 📚 Resources and References

### Documentation
- [Ladle Documentation](https://ladle.dev/)
- [Chakra UI Documentation](https://chakra-ui.com/)
- [Framer Motion Documentation](https://www.framer.com/motion/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)

### Internal Resources
- `src/stories/README.md`: Detailed story documentation
- `src/stories/templates/`: Story templates
- `src/utils/themes.ts`: Theme configuration
- Component examples in `src/components/*.stories.tsx`

### Getting Help
1. **Check existing stories**: Look at similar components for patterns
2. **Review documentation**: Both internal and external docs
3. **Test in isolation**: Use Ladle to debug component issues
4. **Ask the team**: Collaborate on complex components
5. **Use dev tools**: Browser dev tools for debugging styles

## 🔍 Troubleshooting

### Common Issues
- **Theme not applied**: Ensure StoryThemeProvider is used
- **Controls not working**: Check argTypes configuration
- **Stories not appearing**: Verify file naming convention
- **Import errors**: Check path aliases in vite.ladle.config.ts
- **Animation issues**: Check Framer Motion setup
- **Responsive issues**: Test at different viewport sizes

### Debug Steps
1. Check browser console for errors
2. Verify component imports and exports
3. Test component in isolation
4. Check theme provider setup
5. Validate story configuration
6. Review Ladle configuration files

---

This workflow ensures consistent, high-quality component development that integrates seamlessly with the TenK Solutions design system and development practices.
