import { ChakraProvider, extendTheme } from "@chakra-ui/react";
import React from "react";

import { themeByAI } from "#utils/themes";
const custom_theme = {
  fonts: {
    body: "system-ui, sans-serif",
    heading: "'Exo 2', Georgia, sans-serif",
    mono: "Menlo, monospace",
  },
  // attempting to find way to change default background colors, in order to get nav's boxShadow to show up on regular page
  // styles: {
  //   backgroundColor: {
  //     _dark: "red",
  //   },
  //   _light: {
  //     backgroundColor: "red",
  //   },
  // },
  colors: {
    ...themeByAI,
  },
  components: {
    ...themeByAI.components,
  },
};

const theme = extendTheme(custom_theme);

const ThemeWrapper = ({ children }: { children: React.ReactNode }) => {
  return <ChakraProvider theme={theme}>{children}</ChakraProvider>;
};

export default ThemeWrapper;
