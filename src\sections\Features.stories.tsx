import { Box, Text, VStack } from "@chakra-ui/react";
import type { Story, StoryDefault } from "@ladle/react";
import React from "react";
import {
  StoryThemeProvider,
  storyDecorators,
} from "../stories/StoryThemeProvider";
import { Features } from "./Features";

export default {
  title: "Sections/Features",
  component: Features,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
} satisfies StoryDefault;

/**
 * Default features section
 */
export const Default: Story = {
  render: () => <Features />,
  decorators: [storyDecorators.fullWidth],
};

/**
 * Features section documentation
 */
export const Documentation: Story = {
  render: () => (
    <VStack spacing={8} align="stretch" maxW="4xl" mx="auto" p={6}>
      <Text fontWeight="bold" fontSize="lg" textAlign="center">
        Features Section Documentation
      </Text>

      <Box p={6} bg="gray.50" _dark={{ bg: "gray.800" }} borderRadius="lg">
        <VStack spacing={4} align="start">
          <Text fontWeight="semibold">Features:</Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">
              • Responsive grid layout (1 column mobile, 2 tablet, 3 desktop)
            </Text>
            <Text fontSize="sm">• Animated entrance with stagger effect</Text>
            <Text fontSize="sm">• Gradient heading text</Text>
            <Text fontSize="sm">
              • Uses FeaturesCard components for each service
            </Text>
            <Text fontSize="sm">
              • Data-driven content from OFFERED_SERVICES
            </Text>
            <Text fontSize="sm">• Scroll-triggered animations</Text>
          </VStack>

          <Text fontWeight="semibold" mt={4}>
            Content Structure:
          </Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">
              • Section heading: "Comprehensive IT Solutions"
            </Text>
            <Text fontSize="sm">• Descriptive subheading</Text>
            <Text fontSize="sm">• Grid of service feature cards</Text>
            <Text fontSize="sm">
              • Each card includes: title, description, icon, features, image,
              CTA
            </Text>
          </VStack>

          <Text fontWeight="semibold" mt={4}>
            Dependencies:
          </Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">• FeaturesCard component</Text>
            <Text fontSize="sm">• OFFERED_SERVICES data array</Text>
            <Text fontSize="sm">• Framer Motion for animations</Text>
            <Text fontSize="sm">• staggerChildren animation variant</Text>
          </VStack>

          <Text fontWeight="semibold" mt={4}>
            Animation Behavior:
          </Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">• Triggers when section comes into view</Text>
            <Text fontSize="sm">• Staggered entrance for child elements</Text>
            <Text fontSize="sm">
              • One-time animation (doesn't repeat on scroll)
            </Text>
            <Text fontSize="sm">• 100px margin before triggering</Text>
          </VStack>
        </VStack>
      </Box>

      <Box>
        <Text fontSize="sm" fontWeight="semibold" mb={4}>
          Live Component:
        </Text>
        <Box
          border="1px solid"
          borderColor="gray.200"
          borderRadius="md"
          overflow="hidden"
        >
          <Features />
        </Box>
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};

/**
 * Responsive layout demonstration
 */
export const ResponsiveDemo: Story = {
  render: () => (
    <VStack spacing={8} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Responsive Features Section Demo
      </Text>

      <VStack spacing={6} align="stretch">
        <Box>
          <Text fontSize="sm" fontWeight="semibold" mb={2} textAlign="center">
            Mobile View (375px) - Single Column
          </Text>
          <Box
            w="375px"
            mx="auto"
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            overflow="hidden"
            maxH="600px"
            overflowY="auto"
          >
            <Features />
          </Box>
        </Box>

        <Box>
          <Text fontSize="sm" fontWeight="semibold" mb={2} textAlign="center">
            Tablet View (768px) - Two Columns
          </Text>
          <Box
            w="768px"
            mx="auto"
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            overflow="hidden"
            maxH="600px"
            overflowY="auto"
          >
            <Features />
          </Box>
        </Box>

        <Box>
          <Text fontSize="sm" fontWeight="semibold" mb={2} textAlign="center">
            Desktop View (Full Width) - Three Columns
          </Text>
          <Box
            w="full"
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            overflow="hidden"
            maxH="600px"
            overflowY="auto"
          >
            <Features />
          </Box>
        </Box>
      </VStack>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};

/**
 * Animation demonstration
 */
export const AnimationDemo: Story = {
  render: () => {
    const [key, setKey] = React.useState(0);

    return (
      <VStack spacing={6} align="stretch">
        <Text fontWeight="bold" textAlign="center">
          Features Animation Demo
        </Text>
        <Text fontSize="sm" color="gray.600" textAlign="center">
          Click the button below to replay the staggered entrance animation
        </Text>
        <Box textAlign="center">
          <button
            onClick={() => setKey((prev) => prev + 1)}
            style={{
              padding: "8px 16px",
              backgroundColor: "#3182ce",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
            }}
          >
            Replay Animation
          </button>
        </Box>
        <Box
          key={key}
          border="1px solid"
          borderColor="gray.200"
          borderRadius="md"
          overflow="hidden"
          maxH="600px"
          overflowY="auto"
        >
          <Features />
        </Box>
      </VStack>
    );
  },
  decorators: [storyDecorators.fullWidth],
};

/**
 * Section in context
 */
export const InContext: Story = {
  render: () => (
    <VStack spacing={0} align="stretch">
      <Box bg="gray.100" _dark={{ bg: "gray.900" }} p={8} textAlign="center">
        <Text fontSize="sm" color="gray.600">
          Previous Section
        </Text>
      </Box>
      <Features />
      <Box bg="gray.100" _dark={{ bg: "gray.900" }} p={8} textAlign="center">
        <Text fontSize="sm" color="gray.600">
          Next Section
        </Text>
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};
