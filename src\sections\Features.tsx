import { FeaturesCard } from "#components/FeaturesCard";
import { OFFERED_SERVICES } from "#pages/data";
import { staggerChildren } from "#utils/animationVariants";
import {
  Box,
  Container,
  Heading,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";

const MotionBox = motion(Box);

// const Feature = ({
//   title,
//   text,
//   icon,
// }: {
//   title: string;
//   text: string;
//   icon: IconType | undefined;
// }) => {
//   return (
//     <MotionBox
//       p={6}
//       bg={useColorModeValue("white", "gray.800")}
//       rounded="xl"
//       shadow="base"
//       whileHover={{ y: -4, boxShadow: "2xl" }}
//       transition={{ duration: 0.2 }}
//       variants={fadeInUp}
//     >
//       <Stack spacing={4}>
//         <Box
//           w={12}
//           h={12}
//           bg={useColorModeValue("brand.primary.50", "brand.primary.900")}
//           rounded="lg"
//           display="flex"
//           alignItems="center"
//           justifyContent="center"
//         >
//           <Icon
//             as={icon}
//             w={6}
//             h={6}
//             color={useColorModeValue("brand.primary.500", "brand.primary.200")}
//           />
//         </Box>
//         <Heading size="md" fontWeight="600">
//           {title}
//         </Heading>
//         <Text color={useColorModeValue("gray.600", "gray.400")}>{text}</Text>
//       </Stack>
//     </MotionBox>
//   );
// };

export const Features = () => {
  return (
    <Box as="section" py={20} bg={useColorModeValue("gray.50", "gray.900")}>
      <Container maxW="container.xl">
        <MotionBox
          variants={staggerChildren}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          <Stack spacing={12} align="center">
            <Stack spacing={4} textAlign="center" maxW="2xl">
              <Heading
                fontSize={{ base: "3xl", md: "4xl" }}
                bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
                bgClip="text"
              >
                Comprehensive IT Solutions
              </Heading>
              <Text
                fontSize="lg"
                color={useColorModeValue("gray.600", "gray.400")}
              >
                Empowering your business with cutting-edge technology and expert
                support
              </Text>
            </Stack>

            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={10}>
              {OFFERED_SERVICES.map(([title, text, icon,features, image, cta], index) => (
                <>
                  <FeaturesCard
                    key={index}
                    heading={title}
                    description={text}
                    icon={icon}
                    features={features}
                    image={image}
                    cta={cta}
                  />
                </>
              ))}
            </SimpleGrid>
          </Stack>
        </MotionBox>
      </Container>
    </Box>
  );
};
