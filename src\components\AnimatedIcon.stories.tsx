import {
  ArrowForwardIcon,
  CheckCircleIcon,
  InfoIcon,
  MoonIcon,
  StarIcon,
  SunIcon,
  WarningIcon,
} from "@chakra-ui/icons";
import { Box, Button, HStack, Text, VStack } from "@chakra-ui/react";
import type { Story, StoryDefault } from "@ladle/react";
import React from "react";
import { FaHeart } from "react-icons/fa/index.js";
import {
  StoryThemeProvider,
  storyDecorators,
} from "../stories/StoryThemeProvider";
import { AnimatedIcon } from "./AnimatedIcon";

export default {
  title: "Components/Atoms/AnimatedIcon",
  component: AnimatedIcon,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
  args: {
    as: StarIcon,
    boxSize: 6,
    color: "brand.primary.500",
  },
  argTypes: {
    isHovered: {
      control: { type: "boolean" },
      description: "Trigger hover animation",
    },
    boxSize: {
      control: { type: "select" },
      options: [3, 4, 5, 6, 8, 10, 12],
      description: "Icon size",
    },
    color: {
      control: { type: "select" },
      options: [
        "brand.primary.500",
        "brand.secondary.500",
        "blue.500",
        "green.500",
        "red.500",
        "purple.500",
        "orange.500",
      ],
      description: "Icon color",
    },
  },
} satisfies StoryDefault;

/**
 * Default animated icon
 */
export const Default: Story = {
  args: {
    as: StarIcon,
    isHovered: false,
  },
  decorators: [storyDecorators.centered],
};

/**
 * Interactive hover demonstration
 */
export const Interactive: Story = {
  render: () => {
    const [hoveredIcon, setHoveredIcon] = React.useState<string | null>(null);

    const icons = [
      { name: "star", icon: StarIcon, color: "yellow.500" },
      { name: "check", icon: CheckCircleIcon, color: "green.500" },
      { name: "warning", icon: WarningIcon, color: "orange.500" },
      { name: "info", icon: InfoIcon, color: "blue.500" },
      { name: "arrow", icon: ArrowForwardIcon, color: "purple.500" },
      { name: "heart", icon: FaHeart, color: "red.500" },
    ];

    return (
      <VStack spacing={6} align="stretch">
        <Text fontWeight="bold" textAlign="center">
          Interactive Animated Icons
        </Text>
        <Text fontSize="sm" color="gray.600" textAlign="center">
          Hover over the icons to see the animation
        </Text>

        <HStack spacing={6} justify="center" wrap="wrap">
          {icons.map(({ name, icon, color }) => (
            <VStack
              key={name}
              spacing={2}
              p={4}
              borderRadius="lg"
              bg="gray.50"
              _dark={{ bg: "gray.700" }}
              cursor="pointer"
              onMouseEnter={() => setHoveredIcon(name)}
              onMouseLeave={() => setHoveredIcon(null)}
              transition="all 0.2s"
              _hover={{ bg: "gray.100", _dark: { bg: "gray.600" } }}
            >
              <AnimatedIcon
                as={icon}
                boxSize={8}
                color={color}
                isHovered={hoveredIcon === name}
              />
              <Text fontSize="xs" textTransform="capitalize">
                {name}
              </Text>
            </VStack>
          ))}
        </HStack>
      </VStack>
    );
  },
  decorators: [storyDecorators.padded],
};

/**
 * Different sizes
 */
export const Sizes: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Icon Sizes
      </Text>

      <HStack spacing={6} justify="center" align="center" wrap="wrap">
        {[3, 4, 6, 8, 10, 12].map((size) => (
          <VStack key={size} spacing={2}>
            <AnimatedIcon
              as={StarIcon}
              boxSize={size}
              color="brand.primary.500"
              isHovered={true}
            />
            <Text fontSize="xs">{size}</Text>
          </VStack>
        ))}
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Animation states comparison
 */
export const AnimationStates: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Animation States
      </Text>

      <HStack spacing={12} justify="center">
        <VStack spacing={4}>
          <Text fontSize="sm" fontWeight="semibold">
            Normal State
          </Text>
          <Box p={6} borderRadius="lg" bg="gray.50" _dark={{ bg: "gray.700" }}>
            <AnimatedIcon
              as={StarIcon}
              boxSize={10}
              color="brand.primary.500"
              isHovered={false}
            />
          </Box>
        </VStack>

        <VStack spacing={4}>
          <Text fontSize="sm" fontWeight="semibold">
            Animated State
          </Text>
          <Box p={6} borderRadius="lg" bg="gray.50" _dark={{ bg: "gray.700" }}>
            <AnimatedIcon
              as={StarIcon}
              boxSize={10}
              color="brand.primary.500"
              isHovered={true}
            />
          </Box>
        </VStack>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Button integration example
 */
export const InButtons: Story = {
  render: () => {
    const [activeButton, setActiveButton] = React.useState<string | null>(null);

    return (
      <VStack spacing={6} align="stretch">
        <Text fontWeight="bold" textAlign="center">
          Animated Icons in Buttons
        </Text>

        <HStack spacing={4} justify="center" wrap="wrap">
          <Button
            leftIcon={
              <AnimatedIcon
                as={CheckCircleIcon}
                isHovered={activeButton === "success"}
              />
            }
            colorScheme="green"
            onMouseEnter={() => setActiveButton("success")}
            onMouseLeave={() => setActiveButton(null)}
          >
            Success
          </Button>

          <Button
            leftIcon={
              <AnimatedIcon
                as={WarningIcon}
                isHovered={activeButton === "warning"}
              />
            }
            colorScheme="orange"
            onMouseEnter={() => setActiveButton("warning")}
            onMouseLeave={() => setActiveButton(null)}
          >
            Warning
          </Button>

          <Button
            rightIcon={
              <AnimatedIcon
                as={ArrowForwardIcon}
                isHovered={activeButton === "next"}
              />
            }
            colorScheme="brand.primary"
            onMouseEnter={() => setActiveButton("next")}
            onMouseLeave={() => setActiveButton(null)}
          >
            Next
          </Button>
        </HStack>
      </VStack>
    );
  },
  decorators: [storyDecorators.padded],
};

/**
 * Theme color integration
 */
export const WithThemeColors: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Theme Color Integration
      </Text>

      <HStack spacing={8} justify="center" wrap="wrap">
        <VStack spacing={3}>
          <Box
            p={6}
            borderRadius="lg"
            bg="brand.primary.50"
            borderWidth="2px"
            borderColor="brand.primary.200"
            _dark={{
              bg: "brand.primary.900",
              borderColor: "brand.primary.700",
            }}
          >
            <AnimatedIcon
              as={SunIcon}
              boxSize={8}
              color="brand.primary.500"
              isHovered={true}
            />
          </Box>
          <Text fontSize="sm">Primary Theme</Text>
        </VStack>

        <VStack spacing={3}>
          <Box
            p={6}
            borderRadius="lg"
            bg="brand.secondary.50"
            borderWidth="2px"
            borderColor="brand.secondary.200"
            _dark={{
              bg: "brand.secondary.900",
              borderColor: "brand.secondary.700",
            }}
          >
            <AnimatedIcon
              as={MoonIcon}
              boxSize={8}
              color="brand.secondary.500"
              isHovered={true}
            />
          </Box>
          <Text fontSize="sm">Secondary Theme</Text>
        </VStack>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};
