import React from "react";
import type { StoryDefault, Story } from "@ladle/react";
import { StoryThemeProvider, storyDecorators } from "../stories/StoryThemeProvider";
import { VStack, Text, Box } from "@chakra-ui/react";
import CallToActionWithIllustration from "./Hero";

export default {
  title: "Sections/Hero",
  component: CallToActionWithIllustration,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
} satisfies StoryDefault;

/**
 * Default hero section
 */
export const Default: Story = {
  render: () => <CallToActionWithIllustration />,
  decorators: [storyDecorators.fullWidth],
};

/**
 * Hero section documentation
 */
export const Documentation: Story = {
  render: () => (
    <VStack spacing={8} align="stretch" maxW="4xl" mx="auto" p={6}>
      <Text fontWeight="bold" fontSize="lg" textAlign="center">
        Hero Section Documentation
      </Text>
      
      <Box p={6} bg="gray.50" _dark={{ bg: "gray.800" }} borderRadius="lg">
        <VStack spacing={4} align="start">
          <Text fontWeight="semibold">Features:</Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">• Video background with blend mode support</Text>
            <Text fontSize="sm">• Responsive typography scaling</Text>
            <Text fontSize="sm">• Gradient text effects</Text>
            <Text fontSize="sm">• Fade-in animation on mount</Text>
            <Text fontSize="sm">• Color mode aware styling</Text>
            <Text fontSize="sm">• Text shadows for better readability</Text>
          </VStack>
          
          <Text fontWeight="semibold" mt={4}>Content:</Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">• Main heading: "Empower Your Business Growth"</Text>
            <Text fontSize="sm">• Subheading: IT solutions for DMV professional services</Text>
            <Text fontSize="sm">• Video background: PC/technology themed</Text>
          </VStack>
          
          <Text fontWeight="semibold" mt={4}>Dependencies:</Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">• HeroVideo component for background video</Text>
            <Text fontSize="sm">• Chakra UI ScaleFade for animations</Text>
            <Text fontSize="sm">• Brand color theme integration</Text>
          </VStack>
          
          <Text fontWeight="semibold" mt={4}>Responsive Behavior:</Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">• Mobile: 4xl heading, xl subheading</Text>
            <Text fontSize="sm">• Tablet: 6xl heading, 2xl subheading</Text>
            <Text fontSize="sm">• Desktop: 7xl heading, 3xl subheading</Text>
            <Text fontSize="sm">• Responsive padding and margins</Text>
          </VStack>
        </VStack>
      </Box>
      
      <Box>
        <Text fontSize="sm" fontWeight="semibold" mb={4}>Live Component:</Text>
        <Box border="1px solid" borderColor="gray.200" borderRadius="md" overflow="hidden">
          <CallToActionWithIllustration />
        </Box>
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};

/**
 * Hero section in different viewports
 */
export const ResponsiveDemo: Story = {
  render: () => (
    <VStack spacing={8} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Responsive Hero Section Demo
      </Text>
      
      <VStack spacing={6} align="stretch">
        <Box>
          <Text fontSize="sm" fontWeight="semibold" mb={2} textAlign="center">
            Mobile View (375px)
          </Text>
          <Box 
            w="375px" 
            mx="auto" 
            border="1px solid" 
            borderColor="gray.200" 
            borderRadius="md" 
            overflow="hidden"
          >
            <CallToActionWithIllustration />
          </Box>
        </Box>
        
        <Box>
          <Text fontSize="sm" fontWeight="semibold" mb={2} textAlign="center">
            Tablet View (768px)
          </Text>
          <Box 
            w="768px" 
            mx="auto" 
            border="1px solid" 
            borderColor="gray.200" 
            borderRadius="md" 
            overflow="hidden"
          >
            <CallToActionWithIllustration />
          </Box>
        </Box>
        
        <Box>
          <Text fontSize="sm" fontWeight="semibold" mb={2} textAlign="center">
            Desktop View (Full Width)
          </Text>
          <Box 
            w="full" 
            border="1px solid" 
            borderColor="gray.200" 
            borderRadius="md" 
            overflow="hidden"
          >
            <CallToActionWithIllustration />
          </Box>
        </Box>
      </VStack>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};

/**
 * Animation demonstration
 */
export const AnimationDemo: Story = {
  render: () => {
    const [key, setKey] = React.useState(0);
    
    return (
      <VStack spacing={6} align="stretch">
        <Text fontWeight="bold" textAlign="center">
          Hero Animation Demo
        </Text>
        <Text fontSize="sm" color="gray.600" textAlign="center">
          Click the button below to replay the fade-in animation
        </Text>
        <Box textAlign="center">
          <button
            onClick={() => setKey(prev => prev + 1)}
            style={{
              padding: "8px 16px",
              backgroundColor: "#3182ce",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
            }}
          >
            Replay Animation
          </button>
        </Box>
        <Box 
          key={key}
          border="1px solid" 
          borderColor="gray.200" 
          borderRadius="md" 
          overflow="hidden"
        >
          <CallToActionWithIllustration />
        </Box>
      </VStack>
    );
  },
  decorators: [storyDecorators.fullWidth],
};
