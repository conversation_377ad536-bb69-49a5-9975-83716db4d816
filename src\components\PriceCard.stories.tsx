import React from "react";
import type { StoryDefault, Story } from "@ladle/react";
import { StoryThemeProvider, storyDecorators } from "../stories/StoryThemeProvider";
import { VStack, HStack, SimpleGrid } from "@chakra-ui/react";
import { PriceCard } from "./PriceCard";
import { 
  CheckCircleIcon, 
  StarIcon, 
  ShieldIcon,
  SupportIcon,
  TimeIcon,
  PhoneIcon,
  EmailIcon,
  SettingsIcon
} from "@chakra-ui/icons";

// Sample data for stories
const basicItems = [
  { icon: CheckCircleIcon, item: "Basic IT Support" },
  { icon: PhoneIcon, item: "Phone Support" },
  { icon: EmailIcon, item: "Email Support" },
  { icon: TimeIcon, item: "Business Hours Only" },
];

const premiumItems = [
  { icon: CheckCircleIcon, item: "Full IT Management" },
  { icon: ShieldIcon, item: "24/7 Security Monitoring" },
  { icon: SupportIcon, item: "Priority Support" },
  { icon: StarIcon, item: "Dedicated Account Manager" },
  { icon: SettingsIcon, item: "Custom Configurations" },
];

const enterpriseItems = [
  { icon: CheckCircleIcon, item: "Enterprise IT Solutions" },
  { icon: ShieldIcon, item: "Advanced Security Suite" },
  { icon: SupportIcon, item: "24/7 Premium Support" },
  { icon: StarIcon, item: "Strategic IT Consulting" },
  { icon: SettingsIcon, item: "Custom Development" },
  { icon: TimeIcon, item: "SLA Guarantees" },
];

export default {
  title: "Components/Molecules/PriceCard",
  component: PriceCard,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
  args: {
    title: "Basic Plan",
    items: basicItems,
    cta: "Get Started",
    isFeatured: false,
  },
  argTypes: {
    isFeatured: {
      control: { type: "boolean" },
      description: "Show as featured/popular plan",
    },
    title: {
      control: { type: "text" },
      description: "Plan title",
    },
    cta: {
      control: { type: "text" },
      description: "Call-to-action button text",
    },
  },
} satisfies StoryDefault;

/**
 * Default price card
 */
export const Default: Story = {
  args: {
    title: "Basic Plan",
    items: basicItems,
    cta: "Get Started",
    isFeatured: false,
  },
  decorators: [storyDecorators.centered],
};

/**
 * Featured price card
 */
export const Featured: Story = {
  args: {
    title: "Premium Plan",
    items: premiumItems,
    cta: "Choose Premium",
    isFeatured: true,
  },
  decorators: [storyDecorators.centered],
};

/**
 * All plan variations
 */
export const AllPlans: Story = {
  render: () => (
    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} maxW="6xl">
      <PriceCard
        title="Basic"
        items={basicItems}
        cta="Get Started"
        isFeatured={false}
      />
      <PriceCard
        title="Premium"
        items={premiumItems}
        cta="Choose Premium"
        isFeatured={true}
      />
      <PriceCard
        title="Enterprise"
        items={enterpriseItems}
        cta="Contact Sales"
        isFeatured={false}
      />
    </SimpleGrid>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Different feature list lengths
 */
export const VariableFeatures: Story = {
  render: () => (
    <HStack spacing={8} align="start" wrap="wrap" justify="center">
      <PriceCard
        title="Starter"
        items={[
          { icon: CheckCircleIcon, item: "Basic Support" },
          { icon: EmailIcon, item: "Email Support" },
        ]}
        cta="Start Free"
        isFeatured={false}
      />
      <PriceCard
        title="Professional"
        items={basicItems}
        cta="Go Pro"
        isFeatured={false}
      />
      <PriceCard
        title="Enterprise"
        items={enterpriseItems}
        cta="Contact Us"
        isFeatured={true}
      />
    </HStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Interactive testing
 */
export const Interactive: Story = {
  args: {
    title: "Custom Plan",
    items: premiumItems,
    cta: "Get Started",
    isFeatured: false,
  },
  decorators: [storyDecorators.centered],
};

/**
 * Different CTA variations
 */
export const CTAVariations: Story = {
  render: () => (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} maxW="7xl">
      <PriceCard
        title="Free Trial"
        items={basicItems.slice(0, 3)}
        cta="Start Free Trial"
        isFeatured={false}
      />
      <PriceCard
        title="Popular"
        items={premiumItems.slice(0, 4)}
        cta="Choose Plan"
        isFeatured={true}
      />
      <PriceCard
        title="Enterprise"
        items={enterpriseItems.slice(0, 4)}
        cta="Contact Sales"
        isFeatured={false}
      />
      <PriceCard
        title="Custom"
        items={[
          { icon: SettingsIcon, item: "Custom Solutions" },
          { icon: SupportIcon, item: "Dedicated Support" },
          { icon: ShieldIcon, item: "Enterprise Security" },
        ]}
        cta="Request Quote"
        isFeatured={false}
      />
    </SimpleGrid>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Responsive layout demonstration
 */
export const ResponsiveLayout: Story = {
  render: () => (
    <VStack spacing={8} align="stretch">
      <SimpleGrid columns={{ base: 1, sm: 2, lg: 3 }} spacing={6}>
        <PriceCard
          title="Mobile First"
          items={basicItems}
          cta="Get Started"
          isFeatured={false}
        />
        <PriceCard
          title="Responsive"
          items={premiumItems}
          cta="Choose Plan"
          isFeatured={true}
        />
        <PriceCard
          title="Adaptive"
          items={enterpriseItems}
          cta="Contact Us"
          isFeatured={false}
        />
      </SimpleGrid>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};
