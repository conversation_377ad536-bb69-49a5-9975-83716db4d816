import React from "react";
import type { StoryDefault, Story } from "@ladle/react";
import { StoryThemeProvider, storyDecorators } from "../stories/StoryThemeProvider";
import { VStack, HStack, Text, Box, useColorMode } from "@chakra-ui/react";
import ColorModeToggle from "./ColorModeToggle";

export default {
  title: "Components/Atoms/ColorModeToggle",
  component: ColorModeToggle,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
} satisfies StoryDefault;

/**
 * Default color mode toggle
 */
export const Default: Story = {
  render: () => <ColorModeToggle />,
  decorators: [storyDecorators.centered],
};

/**
 * Color mode toggle with context information
 */
export const WithContext: Story = {
  render: () => {
    const ColorModeDemo = () => {
      const { colorMode } = useColorMode();
      
      return (
        <VStack spacing={6} align="center">
          <Text fontWeight="bold" fontSize="lg">
            Color Mode Toggle Demo
          </Text>
          <Box
            p={6}
            borderRadius="lg"
            borderWidth="2px"
            borderColor="gray.200"
            bg="gray.50"
            _dark={{
              bg: "gray.700",
              borderColor: "gray.600",
            }}
          >
            <VStack spacing={4}>
              <Text>Current mode: <strong>{colorMode}</strong></Text>
              <ColorModeToggle />
              <Text fontSize="sm" color="gray.600" textAlign="center">
                Toggle to switch between light and dark themes
              </Text>
            </VStack>
          </Box>
        </VStack>
      );
    };
    
    return <ColorModeDemo />;
  },
  decorators: [storyDecorators.padded],
};

/**
 * Multiple toggles showing consistency
 */
export const Multiple: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Multiple Color Mode Toggles
      </Text>
      <Text fontSize="sm" color="gray.600" textAlign="center">
        All toggles are synchronized and show the same state
      </Text>
      <HStack spacing={8} justify="center" wrap="wrap">
        <VStack spacing={2}>
          <Text fontSize="sm">Toggle 1</Text>
          <ColorModeToggle />
        </VStack>
        <VStack spacing={2}>
          <Text fontSize="sm">Toggle 2</Text>
          <ColorModeToggle />
        </VStack>
        <VStack spacing={2}>
          <Text fontSize="sm">Toggle 3</Text>
          <ColorModeToggle />
        </VStack>
      </HStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Toggle in different containers
 */
export const InContainers: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Color Mode Toggle in Different Contexts
      </Text>
      
      {/* In a card */}
      <Box
        p={4}
        borderRadius="lg"
        bg="white"
        boxShadow="md"
        _dark={{ bg: "gray.800" }}
      >
        <HStack justify="space-between" align="center">
          <Text>In a Card Component</Text>
          <ColorModeToggle />
        </HStack>
      </Box>
      
      {/* In a header-like container */}
      <Box
        p={4}
        borderRadius="lg"
        bg="brand.primary.50"
        borderWidth="1px"
        borderColor="brand.primary.200"
        _dark={{ 
          bg: "brand.primary.900", 
          borderColor: "brand.primary.700" 
        }}
      >
        <HStack justify="space-between" align="center">
          <Text>In a Header Context</Text>
          <ColorModeToggle />
        </HStack>
      </Box>
      
      {/* In a navigation-like container */}
      <Box
        p={4}
        borderRadius="lg"
        bg="gray.100"
        _dark={{ bg: "gray.900" }}
      >
        <HStack justify="space-between" align="center">
          <Text>In Navigation Context</Text>
          <ColorModeToggle />
        </HStack>
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Accessibility demonstration
 */
export const Accessibility: Story = {
  render: () => (
    <VStack spacing={6} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Accessibility Features
      </Text>
      <VStack spacing={4} align="center">
        <Text fontSize="sm" color="gray.600" textAlign="center" maxW="md">
          This toggle includes proper ARIA labels and keyboard navigation support.
          Try using Tab to focus and Space/Enter to toggle.
        </Text>
        <Box
          p={6}
          borderRadius="lg"
          borderWidth="2px"
          borderStyle="dashed"
          borderColor="blue.300"
          bg="blue.50"
          _dark={{
            bg: "blue.900",
            borderColor: "blue.600",
          }}
        >
          <VStack spacing={3}>
            <Text fontSize="sm" fontWeight="semibold">
              Keyboard Navigation Test
            </Text>
            <ColorModeToggle />
            <Text fontSize="xs" color="gray.600">
              Focus with Tab, activate with Space or Enter
            </Text>
          </VStack>
        </Box>
      </VStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};
