import React from "react";
import { Helmet } from "react-helmet";

const TitleArray = [
  "Innovative IT Solutions & Consulting Services",
  "Your Trusted Partner for Managed IT Services",
  "Cloud Solutions & Security Services for Business",
  "Top IT Consulting & Cloud Solutions",
  "Empowering Businesses with Expert IT Services",
  "Contact TenK Solutions - Expert IT Solutions for SMBs",
];
const OrganizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "TenK Solutions, LLC",
  url: "https://tenksolutions.com",
  logo: "https://tenksolutions.com/img/favicon.webp",
  sameAs: [
    "https://www.linkedin.com/company/tenk-solutions",
    "https://www.instagram.com/tenk_solutions",
    "https://www.facebook.com/tenksolutions",
    "https://twitter.com/tenksolutions",
  ],
  contactPoint: [
    {
      "@type": "ContactPoint",
      telephone: "******-573-7837", // Replace with your actual contact number
      contactType: "Customer Service",
      areaServed: "US",
      availableLanguage: "English",
    },
  ],
  address: {
    "@type": "PostalAddress",
    // streetAddress: "1234 Example Rd.", // Replace with actual address if available
    addressLocality: "Silver Spring",
    addressRegion: "MD",
    postalCode: "20910",
    addressCountry: "US",
  },
  description:
    "IT Consultancy firm based in Montgomery County, MD, empowering small and medium-sized businesses in the Metro DMV area with managed IT services, cybersecurity, and cloud solutions.",
  foundingDate: "2020",
  founder: {
    "@type": "Person",
    name: "Kiel Byrne", // Replace with actual founder's name
  },
};

const LocalSchema = {
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  name: "TenK Solutions, LLC",
  image: "https://tenksolutions.com/img/favicon.webp",
  "@id": "https://tenksolutions.com",
  url: "https://tenksolutions.com",
  telephone: "******-573-7837",
  address: {
    "@type": "PostalAddress",
    streetAddress: "10713 Lester St.",
    addressLocality: "Silver Spring",
    addressRegion: "MD",
    postalCode: "20902",
    addressCountry: "US",
  },
  geo: {
    "@type": "GeoCoordinates",
    latitude: 39.083997, // Replace with actual coordinates
    longitude: -77.152757,
  },
  openingHoursSpecification: [
    {
      "@type": "OpeningHoursSpecification",
      dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      opens: "09:00",
      closes: "17:00",
    },
  ],
  sameAs: [
    "https://www.linkedin.com/company/TenkSolutions",
    "https://www.instagram.com/tenksolutions",
    "https://www.facebook.com/tenksolutionsllc",
    "https://x.com/tenk_solutions",
  ],
};

const BreadCrumbsSchema = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  itemListElement: [
    {
      "@type": "ListItem",
      position: 1,
      name: "Home",
      item: "https://tenksolutions.com",
    },
    {
      "@type": "ListItem",
      position: 2,
      name: "Case Studies",
      item: "https://tenksolutions.com/case-studies/",
    },
    {
      "@type": "ListItem",
      position: 3,
      name: "Contact Us",
      item: "https://tenksolutions.com/contact-us/",
    },
    {
      "@type": "ListItem",
      position: 4,
      name: "About Us",
      item: "https://tenksolutions.com/about-us/",
    },
  ],
};
const WebSiteSchema = {
  "@context": "XXXXXXXXXXXXXXXXXX",
  "@type": "WebSite",
  name: "TenK Solutions, LLC",
  url: "https://tenksolutions.com/",
  description:
    "IT Consultancy firm based in Montgomery County, MD, empowering small and medium-sized businesses in the Metro DMV area with managed IT services, cybersecurity, and cloud solutions.",
};

const Head = () => {
  const metaData = {
    title: TitleArray[Math.floor(Math.random() * TitleArray.length)],
    description:
      "Expert IT consulting and cloud solutions provider empowering businesses with innovative technology, managed IT services, and robust security strategies.",
    // "Connect with TenK Solutions for IT consulting that powers small and medium businesses in the DMV. Schedule a consultation today!"

    image: "https://tenksolutions.com/img/favicon.webp",
    domain: "tenksolutions.com",
    keywords: `IT consulting, managed IT services, cloud solutions, cybersecurity, network security, business IT support, IT solutions provider, IT management, data protection, business technology solutions`,
  };

  return (
    <div>
      {/* @ts-ignore - React Helmet type compatibility issue with React 18 */}
      <Helmet>
        <title>{metaData.title}</title>

        <link rel="icon" href="/img/favicon.webp" />
        <link rel="apple-touch-icon" href={metaData.image} />
        <link rel="manifest" href="/manifest.json" />

        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content={metaData.description} />
        <meta name="keywords" content={metaData.keywords} />

        {/* <!-- Structured Data --> */}
        <script type="application/ld+json">
          {JSON.stringify(OrganizationSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(LocalSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(BreadCrumbsSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(WebSiteSchema)}
        </script>

        {/* <!-- Open Graph Meta Tags --> */}
        <meta
          property="og:url"
          content={`https://${metaData.domain}/contact-us/`}
        />
        <meta property="og:type" content="website" />
        <meta property="og:title" content={metaData.title} />
        <meta property="og:description" content={metaData.description} />
        <meta property="og:image" content={metaData.image} />

        {/* <!-- Twitter Meta Tags --> */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta property="twitter:domain" content={metaData.domain} />
        <meta
          property="twitter:url"
          content={`https://${metaData.domain}/contact-us/`}
        />
        <meta name="twitter:title" content={metaData.title} />
        <meta name="twitter:description" content={metaData.description} />
        <meta name="twitter:image" content={metaData.image} />
        <meta charSet="UTF-8" />
      </Helmet>
    </div>
  );
};

export default Head;
