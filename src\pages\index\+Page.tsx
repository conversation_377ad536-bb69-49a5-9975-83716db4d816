import { CurvedDivider } from "#components/CurvedDivider";
import { ParallaxSection } from "#components/ParallaxSection";
import { ContactSection } from "#sections/Contact";
import { Features } from "#sections/Features";
import { HeroSection } from "#sections/HeroSection";
import PartnersSection from "#sections/Partners";
import QuestionnaireSlider from "#sections/QuestionsAndAnswers";
import WhatWeDo from "#sections/WhatWeDo";
import React from "react";

function HomePage() {
  return (
    <>
      <HeroSection />

      <ParallaxSection offsetY={20}>
        <WhatWeDo />
      </ParallaxSection>

      <CurvedDivider />

      <ParallaxSection offsetY={30}>
        <QuestionnaireSlider />
      </ParallaxSection>

      <CurvedDivider flip />

      <ParallaxSection offsetY={40}>
        <Features />
      </ParallaxSection>

      <CurvedDivider />

      <ParallaxSection offsetY={20}>
        <PartnersSection />
      </ParallaxSection>

      <CurvedDivider flip />

      <ContactSection />
    </>
  );
}

export default HomePage;
