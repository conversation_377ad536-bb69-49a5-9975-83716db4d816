import { Box } from "@chakra-ui/react";
import { AnimatePresence, motion } from "framer-motion";
import React, { useState } from "react";

const MotionBox = motion(Box);

interface HeroVideoProps {
  videoSrc: string;
  children: React.ReactNode;
  blendMode?: string;
  backgroundColors?: string;
}

export default function HeroVideo({
  videoSrc,
  children,
  blendMode = "screen",
  backgroundColors,
}: HeroVideoProps) {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <Box position="relative" overflow="hidden" minH="100vh">
      <AnimatePresence>
        <MotionBox
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          zIndex={0}
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoaded ? 1 : 0 }}
          transition={{ duration: 1 }}
        >
          <video
            autoPlay
            muted
            loop
            playsInline
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              mixBlendMode: (blendMode as any),
            }}
            onLoadedData={() => setIsLoaded(true)}
          >
            <source src={videoSrc} type="video/mp4" />
          </video>
        </MotionBox>
      </AnimatePresence>

      <MotionBox
        position="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        bg={backgroundColors}
        opacity={0.9}
        zIndex={1}
      />

      <MotionBox
        position="relative"
        zIndex={2}
        minH="100vh"
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        px={4}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.8 }}
      >
        {children}
      </MotionBox>
    </Box>
  );
}
