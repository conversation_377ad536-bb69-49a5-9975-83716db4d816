import { Box, Text, VStack } from "@chakra-ui/react";
import type { Story, StoryDefault } from "@ladle/react";
import React from "react";
import {
  StoryThemeProvider,
  storyDecorators,
} from "../stories/StoryThemeProvider";
import Navigation from "./Navigation";

// Note: Navigation component requires page context
// In a real application, this would be provided by the router

export default {
  title: "Components/Organisms/Navigation",
  component: Navigation,
  decorators: [
    (Story: React.ComponentType) => (
      <StoryThemeProvider>
        <Story />
      </StoryThemeProvider>
    ),
  ],
} satisfies StoryDefault;

/**
 * Default navigation
 */
export const Default: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <Text fontSize="sm" color="gray.600" textAlign="center">
        Main navigation component with responsive design
      </Text>
      <Box w="full">
        <Navigation />
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};

/**
 * Mobile responsive demonstration
 */
export const MobileView: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <Text fontSize="sm" color="gray.600" textAlign="center">
        Navigation in mobile viewport (resize browser to see mobile menu)
      </Text>
      <Box
        w="full"
        maxW="375px"
        mx="auto"
        border="1px solid"
        borderColor="gray.200"
        borderRadius="lg"
        overflow="hidden"
      >
        <Navigation />
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Desktop view
 */
export const DesktopView: Story = {
  render: () => (
    <VStack spacing={4} align="stretch">
      <Text fontSize="sm" color="gray.600" textAlign="center">
        Navigation in desktop viewport
      </Text>
      <Box w="full" minW="1024px">
        <Navigation />
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.fullWidth],
};

/**
 * Navigation states
 */
export const NavigationStates: Story = {
  render: () => (
    <VStack spacing={8} align="stretch">
      <Text fontWeight="bold" textAlign="center">
        Navigation Component States
      </Text>

      <VStack spacing={6} align="stretch">
        <Box>
          <Text fontSize="sm" fontWeight="semibold" mb={2}>
            Default State
          </Text>
          <Box
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            overflow="hidden"
          >
            <Navigation />
          </Box>
        </Box>

        <Text fontSize="sm" color="gray.600" textAlign="center">
          Note: This component includes:
          <br />
          • Responsive hamburger menu for mobile
          <br />
          • Color mode toggle
          <br />
          • Dynamic navigation links
          <br />
          • Logo and branding
          <br />• Mobile navigation drawer
        </Text>
      </VStack>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};

/**
 * Component documentation
 */
export const Documentation: Story = {
  render: () => (
    <VStack spacing={6} align="stretch" maxW="4xl">
      <Text fontWeight="bold" fontSize="lg" textAlign="center">
        Navigation Component Documentation
      </Text>

      <Box p={6} bg="gray.50" _dark={{ bg: "gray.800" }} borderRadius="lg">
        <VStack spacing={4} align="start">
          <Text fontWeight="semibold">Features:</Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">
              • Responsive design with mobile hamburger menu
            </Text>
            <Text fontSize="sm">• Integrated color mode toggle</Text>
            <Text fontSize="sm">
              • Dynamic navigation links based on page context
            </Text>
            <Text fontSize="sm">• Logo and branding integration</Text>
            <Text fontSize="sm">• Mobile navigation drawer component</Text>
            <Text fontSize="sm">
              • Accessibility features (ARIA labels, keyboard navigation)
            </Text>
          </VStack>

          <Text fontWeight="semibold" mt={4}>
            Dependencies:
          </Text>
          <VStack spacing={2} align="start" pl={4}>
            <Text fontSize="sm">
              • usePageContext hook for current page detection
            </Text>
            <Text fontSize="sm">• ColorModeToggle component</Text>
            <Text fontSize="sm">• MobileNav component</Text>
            <Text fontSize="sm">• NavLink components</Text>
          </VStack>

          <Text fontWeight="semibold" mt={4}>
            Usage:
          </Text>
          <Box
            as="pre"
            fontSize="sm"
            bg="gray.100"
            _dark={{ bg: "gray.700" }}
            p={3}
            borderRadius="md"
            overflow="auto"
          >
            {`import Navigation from "#components/Navigation";

// Used in PageShell layout
<Navigation />`}
          </Box>
        </VStack>
      </Box>

      <Box>
        <Text fontSize="sm" fontWeight="semibold" mb={2}>
          Live Component:
        </Text>
        <Box
          border="1px solid"
          borderColor="gray.200"
          borderRadius="md"
          overflow="hidden"
        >
          <Navigation />
        </Box>
      </Box>
    </VStack>
  ),
  decorators: [storyDecorators.padded],
};
